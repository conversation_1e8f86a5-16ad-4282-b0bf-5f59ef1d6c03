/**
 * Copyright (c) 2013 - 2019, Nordic Semiconductor ASA
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Nordic
 *    Semiconductor ASA integrated circuit in a product or a software update for
 *    such product, must reproduce the above copyright notice, this list of
 *    conditions and the following disclaimer in the documentation and/or other
 *    materials provided with the distribution.
 *
 * 3. Neither the name of Nordic Semiconductor ASA nor the names of its
 *    contributors may be used to endorse or promote products derived from this
 *    software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Nordic Semiconductor ASA integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY NORDIC SEMICONDUCTOR ASA "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL NORDIC SEMICONDUCTOR ASA OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

/**@file
 *
 * @defgroup ble_sdk_app_connectivity_main main.c
 * @{
 * @ingroup ble_sdk_app_connectivity
 *
 * @brief BLE Connectivity application.
 */

#include <stdbool.h>
#include "nrf_sdm.h"
#include "nrf_soc.h"
#include "app_error.h"
#include "app_scheduler.h"
#include "nrf_sdh.h"
#include "ser_hal_transport.h"
#include "ser_conn_handlers.h"
#include "boards.h"
#include "nrf_drv_clock.h"

#include "nrf_log.h"
#include "nrf_log_ctrl.h"
#include "nrf_log_default_backends.h"
#include "app_timer.h"
#include "ser_phy_debug_comm.h"
#include "ser_conn_user_pkt_decoder.h"
#include "nrf_gpio.h"
#include "nrf_gpiote.h"
#include "nrf_drv_gpiote.h"
#include "app_scheduler_ant.h"
#include "ser_phy_config_conn.h"

#define RELEASE_VERSION         1

static void on_idle(void)
{

    if (!NRF_LOG_PROCESS())
    {
      // Wait for an event.
      if (nrf_sdh_is_enabled())
      {
          ret_code_t ret_code = sd_app_evt_wait();
          ASSERT((ret_code == NRF_SUCCESS) || (ret_code == NRF_ERROR_SOFTDEVICE_NOT_ENABLED));
          UNUSED_VARIABLE(ret_code);
      }
      else
      {
          // Wait for an event.
          __WFE();
          // Clear the internal event register.
          __SEV();
          __WFE();
      }
    }
}

#if !RELEASE_VERSION
static void leds_timeout_handler(void * p_context)
{
    nrf_gpio_pin_toggle(LED_1);
}

APP_TIMER_DEF(m_leds_timer_id); 
static void led_timer_init(void)
{
    app_timer_create(&m_leds_timer_id,
                                APP_TIMER_MODE_REPEATED,
                                leds_timeout_handler);
    app_timer_start(m_leds_timer_id, APP_TIMER_TICKS(1000), NULL);
}
#endif

static void ser_phy_idle_request(nrf_drv_gpiote_pin_t pin,
                                       nrf_gpiote_polarity_t action)
{
    if (nrf_gpio_pin_read(pin) == 0){
        system_run_high_performance = true;
    }
    else{
        system_run_high_performance = false;
    }
}

static void connectivity_slave_idle_state_pin_init(void)
{
    nrf_gpio_cfg_input(SLAVE_IDLE_STATE_PIN,NRF_GPIO_PIN_PULLUP);
    if (!nrf_drv_gpiote_is_init())
    {
        (void)nrf_drv_gpiote_init();
    }
    NVIC_SetPriority(GPIOTE_IRQn, APP_IRQ_PRIORITY_LOWEST);

    nrf_drv_gpiote_in_config_t config = GPIOTE_CONFIG_IN_SENSE_TOGGLE(true);
    /* Enable pullup to ensure high state while connectivity device is reset */
    config.pull = NRF_GPIO_PIN_PULLUP;
    ret_code_t err_code = nrf_drv_gpiote_in_init(SLAVE_IDLE_STATE_PIN,
        &config, ser_phy_idle_request);
    nrf_drv_gpiote_in_event_enable(SLAVE_IDLE_STATE_PIN,true);

    system_run_high_performance = !(nrf_gpio_pin_read(SLAVE_IDLE_STATE_PIN));
}

static void connectivity_dfu_mode_state_rsp(void)
{
    if(NRF_POWER->GPREGRET2 == 0x42){
        NRF_POWER->GPREGRET2 = 0x00;
    }
}

static void sdh_resume_check(void)
{
    if (nrf_sdh_is_suspended()){
        if ((!master_is_sleep()) && (app_sched_queue_space_get_ant() || app_sched_queue_space_get())){
           nrf_sdh_resume();
        }
    } 
}

/**@brief Main function of the connectivity application. */ 
int main(void)
{
    uint32_t err_code = NRF_SUCCESS;

    APP_ERROR_CHECK(NRF_LOG_INIT(NULL));

    NRF_LOG_DEFAULT_BACKENDS_INIT();

    NRF_LOG_INFO("BLE connectivity started");
#if !RELEASE_VERSION
    bsp_board_init(BSP_INIT_LEDS);

    bsp_board_led_on(0);
    bsp_board_led_on(1);
#endif
    connectivity_dfu_mode_state_rsp();
    connectivity_slave_idle_state_pin_init();
    /* Force constant latency mode to control SPI slave timing */
    NRF_POWER->TASKS_CONSTLAT = 1;

    /* Initialize scheduler queue. */
    //lint -save -e666 -e587
    APP_SCHED_INIT(SER_CONN_SCHED_MAX_EVENT_DATA_SIZE, SER_CONN_SCHED_QUEUE_BLE_SIZE);
    APP_SCHED_INIT_ANT(SER_CONN_SCHED_MAX_ANT_EVENT_DATA_SIZE, SER_CONN_SCHED_QUEUE_ANT_SIZE);
    //lint -restore

    /* Initialize SoftDevice.
     * SoftDevice Event IRQ is not scheduled but immediately copies BLE events to the application
     * scheduler queue */

    err_code = nrf_drv_clock_init();
    if ((err_code != NRF_SUCCESS) &&
        (err_code != NRF_ERROR_MODULE_ALREADY_INITIALIZED))
    {
        APP_ERROR_CHECK(err_code);
    }

    nrf_drv_clock_hfclk_request(NULL);
    while (!nrf_drv_clock_hfclk_is_running())
    {}

    /* Open serialization HAL Transport layer and subscribe for HAL Transport events. */
    err_code = ser_hal_transport_open(ser_conn_hal_transport_event_handle);
    APP_ERROR_CHECK(err_code);

    err_code = nrf_sdh_enable_request();
    APP_ERROR_CHECK(err_code);

    err_code = app_timer_init();
    APP_ERROR_CHECK(err_code);
#if !RELEASE_VERSION    
    led_timer_init();
#endif    
    ser_conn_on_no_mem_handler_set(on_idle);
    
    NRF_LOG_INFO("BLE connectivity main started");
    /* Enter main loop. */
    for (;;)
    {
        /* Process SoftDevice events. */

        app_sched_execute();
        sdh_resume_check();
        ser_conn_rx_process();

        app_sched_execute_ant();
        sdh_resume_check();
        ser_conn_rx_process();

//        system_run_high_performance = nrf_gpio_pin_read(SLAVE_IDLE_STATE_PIN);
        if(!system_run_high_performance && !app_sched_queue_space_get_ant() && !app_sched_queue_space_get()){
             on_idle();
        }
    }
}
/** @} */
