[LPThreshold]
#LED,  PD,   TESTE<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, LLPRAT<PERSON>, 
LED0,  PD0,  1,      11.0,  0.8,    0.037,      
LED0,  PD1,  0,      11.0,  0.8,    0.037,      
LED0,  PD2,  1,      11.0,  0.8,    0.037,      
LED0,  PD3,  0,      11.0,  0.8,    0.037,      
                                
LED1,  PD0,  0,      24.0,  0.8,    0.015,      
LED1,  PD1,  0,      24.0,  0.8,    0.015,      
LED1,  PD2,  0,      24.0,  0.8,    0.015,      
LED1,  PD3,  0,      24.0,  0.8,    0.015,      
                                
LED2,  PD0,  1,      24.0,  0.8,    0.015,      
LED2,  PD1,  0,      24.0,  2.8,    0.015,      
LED2,  PD2,  1,      24.0,  0.8,    0.015,      
LED2,  PD3,  0,      24.0,  2.8,    0.015,      
                                
LED3,  PD0,  1,      24.0,  0.8,    0.015,      
LED3,  PD1,  0,      24.0,  0.8,    0.015,      
LED3,  PD2,  1,      24.0,  0.8,    0.015,      
LED3,  PD3,  0,      24.0,  0.8,    0.015,      
                                
LED4,  PD0,  0,      11.0,  0.8,    0.015,      
LED4,  PD1,  1,      11.0,  0.8,    0.015,      
LED4,  PD2,  0,      11.0,  0.8,    0.015,      
LED4,  PD3,  1,      11.0,  0.8,    0.015,      
                                
LED5,  PD0,  0,      24.0,  0.8,    0.015,      
LED5,  PD1,  0,      24.0,  0.8,    0.015,      
LED5,  PD2,  0,      24.0,  0.8,    0.015,      
LED5,  PD3,  0,      24.0,  0.8,    0.015,      
                                
LED6,  PD0,  0,      24.0,  2.8,    0.015,      
LED6,  PD1,  1,      24.0,  0.8,    0.015,      
LED6,  PD2,  0,      24.0,  2.8,    0.015,      
LED6,  PD3,  1,      24.0,  0.8,    0.015,      
                                
LED7,  PD0,  0,      24.0,  0.8,    0.015,      
LED7,  PD1,  1,      24.0,  0.8,    0.015,      
LED7,  PD2,  0,      24.0,  0.8,    0.015,      
LED7,  PD3,  1,      24.0,  0.8,    0.015,      

[PPGTestCFG]
#TESTITEM, S_NUM, S_SKIPNUM, S_MUTIPLE, TIA_RF, TIA_CF, ADC_N, BG_LVL, ACG, LEDDRV, SLOT_TMR, FIFO_LINE,
CTR,       100,   0,         9,         0,      0,      5,     2,      0,   51,     985,      32,
NOISE,     100,   100,       9,         4,      0,      5,     1,      3,   0,      985,      32,
BASENOISE, 100,   0,         9,         4,      0,      6,     0,      0,   0,      985,      32,
LEAKLIGHT, 20,    0,         9,         4,      0,      5,     2,      0,   51,     985,      32,

[HighPassFilterCFG]
# fs= 100 ,fc= 0.6 ,N= 6
#a,1.00000000000000,-5.85434350824134,14.28228756828198,-18.58523089819364,13.60541708572088,-5.31257392706289,0.86444368216594,
#b,0.92975463546354,-5.57852781278125,13.94631953195312,-18.59509270927083,13.94631953195312,-5.57852781278125,0.92975463546354,
# fs= 100 ,fc= 0.8 ,N= 6
a,1.00000000000000,-5.80579309703828,14.04773386830920,-18.13186088426856,13.16715655966765,-5.10069928436602,0.82346285235115,
b,0.90744853978126,-5.44469123868758,13.61172809671895,-18.14897079562527,13.61172809671895,-5.44469123868758,0.90744853978126,

[ECGTestCFG]
#TestItem, SampleNum, SkipNum, IOStatus, SignalType, Freq, ACVol, ACOffset, DCOffset, FIFO_LINE,
LEADOFF,   1000,      100,     0x001C,   NONE_MODE,  0,    0,     0,        0,        150,
NOISE,     1000,      500,     0x0006,   NONE_MODE,  0,    0,     0,        0,        25,
SIGNAL0,   500,       300,     0x0401,   AC_MODE,    5,    57,    57,       15,       25,    
#SIGNAL1,  3500,      300,     0x0401,   AC_MODE,    0.67, 57,    57,       15,       25,    
SIGNAL1,   500,       300,     0x0401,   AC_MODE,    40,   57,    57,       15,       25,    
HPF0,      200,       0,       0x0401,   NONE_MODE,  0,    0,     0,        10,       25,    
HPF1,      200,       0,       0x0401,   NONE_MODE,  0,    0,     285,      10,       25,    
CMRR0,     500,       300,     0x070B,   AC_MODE,    50,   1000,  10,       510,      25,    
CMRR1,     500,       300,     0x070B,   AC_MODE,    50,   1000,  10,       210,      25,    
CMRR2,     500,       300,     0x030B,   AC_MODE,    50,   1000,  10,       210,      25,    
LIN0,      500,       300,     0x0401,   AC_MODE,    5,    285,   57,       35,       25,    
LIN1,      500,       300,     0x0421,   AC_MODE,    5,    285,   57,       35,       25,    
DCRANGE0,  500,       500,     0x0401,   ANG_MODE,   10.4, 57,    57,       15,       25,    
DCRANGE1,  500,       500,     0x0001,   ANG_MODE,   10.4, 57,    57,       510,      25,    
DCRANGE2,  500,       500,     0x0401,   ANG_MODE,   10.4, 57,    57,       510,      25,    