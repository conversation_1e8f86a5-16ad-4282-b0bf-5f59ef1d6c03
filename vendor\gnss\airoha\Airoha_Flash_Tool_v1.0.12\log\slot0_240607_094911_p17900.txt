time=06/07 09:49:11.023, level=INFO, class = LogService, tool_version = 0.2.12/air_bta_ic_premium_g3
time=06/07 09:49:11.020, level=INFO, class = Controller, SetLogLevel = 3
time=06/07 09:49:11.020, level=DEBUG, class = ToolLogLevel, log_level = 3
time=06/07 09:49:11.020, level=DEBUG, class = ToolLogLevel, task_time = 0.000
time=06/07 09:49:11.020, level=DEBUG, class = ConnectDUT, state = init data, result = ok
time=06/07 09:49:11.020, level=DEBUG, class = SerialHost, phy_name = UART, trans_name = h4
time=06/07 09:49:11.020, level=DEBUG, class = Physical, type = 1, state = create
time=06/07 09:49:11.020, level=DEBUG, class = Transport, type = 4, state = create
time=06/07 09:49:11.021, level=INFO, class = UartPhy, desc = USB Serial Port (COM4)
time=06/07 09:49:11.021, level=INFO, class = UartPhy, instance_id = FTDIBUS\VID_0403+PID_6015+DQ014GZWA\0000
time=06/07 09:49:11.021, level=INFO, class = UartPhy, port = 4, baud = 3000000, handshake = 0, read/timeout = (3, 0)
time=06/07 09:49:11.060, level=DEBUG, class = SerialHost, state = open
time=06/07 09:49:11.060, level=DEBUG, class = UartDev, state = connect, ret = 0
time=06/07 09:49:11.060, level=DEBUG, class = ConnectDUT, state = connect, device_name = DUT, device_type = UART, result = ok
time=06/07 09:49:11.060, level=DEBUG, class = ConnectDUT, task_time = 0.040
time=06/07 09:49:11.060, level=DEBUG, class = CallbackManager, register = DUT, filter = 0, cb = 0x00EE2C00
time=06/07 09:49:11.060, level=DEBUG, class = UartDev, SetBaudrate = 115200
time=06/07 09:49:11.061, level=DEBUG, class = Host, SwitchTransport = bypass
time=06/07 09:49:11.061, level=DEBUG, class = Transport, type = 1, state = create
time=06/07 09:49:11.061, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=06/07 09:49:11.062, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.062, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.080, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:49:11.097, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:49:11.097, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=06/07 09:49:11.098, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.098, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.116, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=06/07 09:49:11.133, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:49:11.133, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=06/07 09:49:11.133, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.133, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.151, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:49:11.169, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=06/07 09:49:11.169, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=06/07 09:49:11.169, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.169, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.187, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:49:11.204, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:49:11.204, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=06/07 09:49:11.204, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.204, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.221, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:49:11.247, level=WARN, class = Host, msg = Match timeout 25 ms(0)
time=06/07 09:49:11.247, level=WARN, class = BtromHandshake, timeout = 42, message = response timeout
time=06/07 09:49:11.247, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.247, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.278, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:11.278, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:11.278, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.278, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.309, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:11.309, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:11.309, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.309, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.340, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:11.340, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:11.341, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.341, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.356, level=DEBUG, [COM4] Read(2)
time=06/07 09:49:11.356, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=06/07 09:49:11.356, level=INFO, [RxP 2] 2450
time=06/07 09:49:11.372, level=DEBUG, [COM4] Read(30)
time=06/07 09:49:11.372, level=INFO, [RxP 30] 4149523338322C312A32450D0A24504149523130302C312C302A33410D0A
time=06/07 09:49:11.372, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:11.372, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.372, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.387, level=DEBUG, [COM4] Read(32)
time=06/07 09:49:11.387, level=INFO, [RxP 32] 24504149523036322C312C302A33460D0A24504149523036322C352C302A3342
time=06/07 09:49:11.403, level=DEBUG, [COM4] Read(24)
time=06/07 09:49:11.403, level=INFO, [RxP 24] 0D0A24504149523036322C332C302A33440D0A2450414952
time=06/07 09:49:11.403, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:11.403, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.403, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.418, level=DEBUG, [COM4] Read(30)
time=06/07 09:49:11.419, level=INFO, [RxP 30] 3038302C302A32450D0A24504149523036362C312C312C312C312C312C30
time=06/07 09:49:11.434, level=DEBUG, [COM4] Read(30)
time=06/07 09:49:11.434, level=INFO, [RxP 30] 2A33420D0A24504149523038312A33330D0A24504149523439302C312A32
time=06/07 09:49:11.434, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:11.434, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.434, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.450, level=DEBUG, [COM4] Read(3)
time=06/07 09:49:11.450, level=INFO, [RxP 3] 410D0A
time=06/07 09:49:11.481, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:11.481, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:49:11.481, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.481, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.512, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:11.512, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:11.512, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.512, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.543, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:11.543, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:11.543, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.543, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.575, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:11.575, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:11.575, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.575, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.606, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:11.606, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:11.606, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.606, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.637, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:11.637, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:11.637, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.637, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.668, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:11.668, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:11.669, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.669, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.710, level=WARN, class = Host, msg = Match timeout 41 ms(0)
time=06/07 09:49:11.710, level=WARN, class = BtromHandshake, timeout = 41, message = response timeout
time=06/07 09:49:11.711, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.711, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.742, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:11.742, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:11.742, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.742, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.773, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:11.773, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:11.773, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.773, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.804, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:11.804, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:11.805, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.805, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.835, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:11.835, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:11.836, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.836, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.867, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:11.867, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:11.867, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.867, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.898, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:11.898, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:11.898, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.898, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.929, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:11.929, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:11.930, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.930, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.960, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:11.960, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:11.961, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.961, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:11.992, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:11.992, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:11.992, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:11.992, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.023, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:12.023, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:12.023, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.023, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.054, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:12.054, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:12.054, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.054, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.085, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:12.085, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:12.085, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.085, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.117, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:12.117, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:12.117, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.117, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.148, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:12.148, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:12.148, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.148, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.179, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:12.179, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:12.179, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.179, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.210, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:12.210, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:12.211, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.211, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.245, level=WARN, class = Host, msg = Match timeout 34 ms(0)
time=06/07 09:49:12.245, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=06/07 09:49:12.245, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.245, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.276, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:12.276, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:12.277, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.277, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.308, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:12.308, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:12.308, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.308, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.339, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:12.339, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:12.339, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.339, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.370, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:12.370, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:12.370, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.370, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.401, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:12.401, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:12.401, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.401, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.432, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:12.432, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:12.433, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.433, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.464, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:12.464, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:12.464, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.464, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.495, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:12.495, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:12.495, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.495, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.526, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:12.526, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:12.527, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.527, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.557, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:12.558, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:12.558, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.558, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.589, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:12.589, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:12.589, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.589, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.620, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:12.620, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:12.620, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.620, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.651, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:12.651, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:12.651, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.651, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.694, level=WARN, class = Host, msg = Match timeout 42 ms(0)
time=06/07 09:49:12.694, level=WARN, class = BtromHandshake, timeout = 42, message = response timeout
time=06/07 09:49:12.694, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.694, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.725, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:12.725, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:12.725, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.725, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.762, level=WARN, class = Host, msg = Match timeout 36 ms(0)
time=06/07 09:49:12.762, level=WARN, class = BtromHandshake, timeout = 36, message = response timeout
time=06/07 09:49:12.762, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.762, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.793, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:12.793, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:12.793, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.793, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.824, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:12.824, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:12.824, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.824, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.855, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:12.855, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:12.856, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.856, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.887, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:12.887, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:12.887, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.887, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.918, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:12.918, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:12.918, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.918, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.949, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:12.949, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:12.949, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.949, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:12.980, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:12.980, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:12.980, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:12.980, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.011, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:13.011, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:13.012, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.012, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.043, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:13.043, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:13.043, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.043, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.074, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:13.074, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:13.074, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.074, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.105, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:13.105, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:13.106, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.106, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.136, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:13.136, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:13.137, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.137, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.168, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:13.168, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:13.168, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.168, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.199, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:13.199, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:13.199, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.199, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.230, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:13.230, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:13.230, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.230, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.261, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:13.261, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:13.262, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.262, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.297, level=WARN, class = Host, msg = Match timeout 35 ms(0)
time=06/07 09:49:13.297, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=06/07 09:49:13.297, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.297, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.328, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:13.328, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:13.328, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.328, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.359, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:13.359, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:13.360, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.360, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.391, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:13.391, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:13.391, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.391, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.422, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:13.422, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:13.422, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.422, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.453, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:13.453, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:13.453, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.453, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.484, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:13.484, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:13.485, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.485, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.516, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:13.516, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:13.516, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.516, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.547, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:13.547, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:13.547, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.547, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.578, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:13.578, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:13.578, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.578, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.609, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:13.609, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:13.610, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.610, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.641, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:13.641, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:13.641, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.641, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.672, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:13.672, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:13.672, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.672, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.702, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:13.702, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:13.702, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.702, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.734, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:13.734, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:13.735, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.735, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.765, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:13.765, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:13.766, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.766, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.797, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:13.797, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:13.797, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.797, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.828, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:13.828, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:13.828, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.828, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.859, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:13.859, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:13.859, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.859, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.890, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:13.890, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:13.890, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.890, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.921, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:13.921, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:13.921, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.921, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.952, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:13.952, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:13.953, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.953, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:13.983, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:13.983, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:13.984, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:13.984, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.015, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:14.015, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:14.015, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.015, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.045, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:14.046, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:14.046, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.046, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.077, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:14.077, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:14.077, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.077, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.108, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:14.108, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:14.109, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.109, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.139, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:14.140, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:14.140, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.140, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.171, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:14.171, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:14.171, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.171, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.202, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:14.202, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:14.202, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.202, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.233, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:14.233, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:14.234, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.234, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.264, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:14.264, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:14.265, level=DEBUG, [COM4] Read(26)
time=06/07 09:49:14.265, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 51.23 B/s
time=06/07 09:49:14.265, level=INFO, [RxP 26] 24504149523338322C312A32450D0A24504149523130302C312C
time=06/07 09:49:14.265, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.265, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.280, level=DEBUG, [COM4] Read(32)
time=06/07 09:49:14.280, level=INFO, [RxP 32] 302A33410D0A24504149523036322C312C302A33460D0A24504149523036322C
time=06/07 09:49:14.296, level=DEBUG, [COM4] Read(24)
time=06/07 09:49:14.296, level=INFO, [RxP 24] 352C302A33420D0A24504149523036322C332C302A33440D
time=06/07 09:49:14.296, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:14.296, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.296, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.311, level=DEBUG, [COM4] Read(30)
time=06/07 09:49:14.311, level=INFO, [RxP 30] 0A24504149523038302C302A32450D0A24504149523036362C312C312C31
time=06/07 09:49:14.347, level=WARN, class = Host, msg = Match timeout 35 ms(0)
time=06/07 09:49:14.347, level=WARN, class = BtromHandshake, timeout = 51, message = response timeout
time=06/07 09:49:14.347, level=DEBUG, [COM4] Read(39)
time=06/07 09:49:14.347, level=INFO, [RxP 39] 2C312C312C302A33420D0A24504149523038312A33330D0A24504149523439302C312A32410D0A
time=06/07 09:49:14.347, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.347, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.378, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:14.378, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:14.379, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.379, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.394, level=DEBUG, [COM4] Read(17)
time=06/07 09:49:14.394, level=INFO, [RxP 17] 24504149523036322C332C312A33430D0A
time=06/07 09:49:14.425, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:14.425, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:49:14.425, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.425, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.456, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:14.456, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:14.457, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.457, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.488, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:14.488, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:14.488, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.488, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.519, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:14.519, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:14.519, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.519, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.550, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:14.550, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:14.550, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.550, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.581, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:14.581, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:14.581, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.581, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.597, level=DEBUG, [COM4] Read(18)
time=06/07 09:49:14.597, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 427.28 B/s
time=06/07 09:49:14.597, level=INFO, [RxP 18] 24504149523030322A33380D0A2450414952
time=06/07 09:49:14.613, level=DEBUG, [COM4] Read(8)
time=06/07 09:49:14.613, level=INFO, [RxP 8] 3439312A33360D0A
time=06/07 09:49:14.613, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:14.613, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.613, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.644, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:14.644, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:14.644, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.644, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.675, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:14.675, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:14.676, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.676, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.706, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:14.706, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:14.707, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.707, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.738, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:14.738, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:14.738, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.738, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.769, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:14.769, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:14.769, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.769, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.800, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:14.800, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:14.800, level=DEBUG, [COM4] Read(26)
time=06/07 09:49:14.800, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 39.35 B/s
time=06/07 09:49:14.800, level=INFO, [RxP 26] 24504149523030322A33380D0A24504149523439312A33360D0A
time=06/07 09:49:14.801, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.801, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.831, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:14.831, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:14.831, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.831, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.863, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:14.863, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:14.863, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.863, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.897, level=WARN, class = Host, msg = Match timeout 34 ms(0)
time=06/07 09:49:14.897, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=06/07 09:49:14.898, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.898, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.928, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:14.928, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:14.929, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.929, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.960, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:14.960, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:14.960, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.960, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:14.991, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:14.991, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:14.991, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:14.991, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.007, level=DEBUG, [COM4] Read(6)
time=06/07 09:49:15.007, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=06/07 09:49:15.007, level=INFO, [RxP 6] 245041495230
time=06/07 09:49:15.022, level=DEBUG, [COM4] Read(20)
time=06/07 09:49:15.022, level=INFO, [RxP 20] 30322A33380D0A24504149523439312A33360D0A
time=06/07 09:49:15.023, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:15.023, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.023, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.054, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:15.054, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:15.054, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.054, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.085, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:15.085, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:15.085, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.085, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.116, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:15.116, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:15.116, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.116, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.147, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:15.147, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:15.148, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.148, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.179, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:15.179, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:15.179, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.179, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.210, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:15.210, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:15.210, level=DEBUG, [COM4] Read(20)
time=06/07 09:49:15.210, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 98.49 B/s
time=06/07 09:49:15.210, level=INFO, [RxP 20] 24504149523030322A33380D0A24504149523439
time=06/07 09:49:15.210, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.210, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.226, level=DEBUG, [COM4] Read(6)
time=06/07 09:49:15.226, level=INFO, [RxP 6] 312A33360D0A
time=06/07 09:49:15.257, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:15.257, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:49:15.257, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.257, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.288, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:15.288, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:15.288, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.288, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.319, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:15.319, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:15.319, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.319, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.350, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:15.350, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:15.351, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.351, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.382, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:15.382, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:15.382, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.382, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.397, level=DEBUG, [COM4] Read(2)
time=06/07 09:49:15.397, level=INFO, [RxP 2] 2450
time=06/07 09:49:15.432, level=WARN, class = Host, msg = Match timeout 34 ms(0)
time=06/07 09:49:15.432, level=WARN, class = BtromHandshake, timeout = 49, message = response timeout
time=06/07 09:49:15.432, level=DEBUG, [COM4] Read(24)
time=06/07 09:49:15.432, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 36.07 B/s
time=06/07 09:49:15.432, level=INFO, [RxP 24] 4149523030322A33380D0A24504149523439312A33360D0A
time=06/07 09:49:15.432, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.432, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.463, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:15.463, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:15.463, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.463, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.502, level=WARN, class = Host, msg = Match timeout 38 ms(0)
time=06/07 09:49:15.502, level=WARN, class = BtromHandshake, timeout = 38, message = response timeout
time=06/07 09:49:15.502, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.502, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.533, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:15.533, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:15.533, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.533, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.564, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:15.564, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:15.564, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.564, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.595, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:15.595, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:15.596, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.596, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.611, level=DEBUG, [COM4] Read(14)
time=06/07 09:49:15.611, level=INFO, [RxP 14] 24504149523030322A33380D0A24
time=06/07 09:49:15.627, level=DEBUG, [COM4] Read(12)
time=06/07 09:49:15.627, level=INFO, [RxP 12] 504149523439312A33360D0A
time=06/07 09:49:15.627, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:15.627, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.627, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.658, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:15.658, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:15.658, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.658, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.689, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:15.689, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:15.689, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.689, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.720, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:15.720, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:15.721, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.721, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.752, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:15.752, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:15.752, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.752, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.783, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:15.783, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:15.783, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.783, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.814, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:15.814, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:15.814, level=DEBUG, [COM4] Read(26)
time=06/07 09:49:15.814, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 68.00 B/s
time=06/07 09:49:15.814, level=INFO, [RxP 26] 24504149523030322A33380D0A24504149523439312A33360D0A
time=06/07 09:49:15.814, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.814, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.845, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:15.845, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:15.846, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.846, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.877, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:15.877, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:15.877, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.877, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.908, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:15.908, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:15.908, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.908, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.948, level=WARN, class = Host, msg = Match timeout 40 ms(0)
time=06/07 09:49:15.948, level=WARN, class = BtromHandshake, timeout = 40, message = response timeout
time=06/07 09:49:15.949, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.949, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:15.980, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:15.980, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:15.980, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:15.980, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.011, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:16.011, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:16.011, level=DEBUG, [COM4] Read(10)
time=06/07 09:49:16.011, level=INFO, [RxP 10] 24504149523030322A33
time=06/07 09:49:16.011, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.011, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.027, level=DEBUG, [COM4] Read(16)
time=06/07 09:49:16.027, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 47.04 B/s
time=06/07 09:49:16.027, level=INFO, [RxP 16] 380D0A24504149523439312A33360D0A
time=06/07 09:49:16.058, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:16.058, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:49:16.058, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.058, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.089, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:16.089, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:16.089, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.089, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.120, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:16.120, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:16.120, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.120, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.151, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:16.151, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:16.152, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.152, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.182, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:16.182, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:16.183, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.183, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.214, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:16.214, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:16.214, level=DEBUG, [COM4] Read(13)
time=06/07 09:49:16.214, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:49:16.214, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.214, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.245, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:16.245, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:16.245, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.245, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.276, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:16.276, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:16.277, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.277, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.308, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:16.308, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:16.308, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.308, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.339, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:16.339, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:16.339, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.339, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.370, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:16.370, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:16.370, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.370, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.401, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:16.401, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:16.401, level=DEBUG, [COM4] Read(4)
time=06/07 09:49:16.401, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 34.70 B/s
time=06/07 09:49:16.401, level=INFO, [RxP 4] 24504149
time=06/07 09:49:16.402, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.402, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.417, level=DEBUG, [COM4] Read(9)
time=06/07 09:49:16.417, level=INFO, [RxP 9] 523439312A33360D0A
time=06/07 09:49:16.448, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:16.448, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:49:16.449, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.449, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.483, level=WARN, class = Host, msg = Match timeout 34 ms(0)
time=06/07 09:49:16.483, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=06/07 09:49:16.483, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.483, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.514, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:16.514, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:16.515, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.515, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.546, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:16.546, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:16.546, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.546, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.577, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:16.577, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:16.577, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.577, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.608, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:16.608, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:16.608, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.608, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.624, level=DEBUG, [COM4] Read(13)
time=06/07 09:49:16.624, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 40.44 B/s
time=06/07 09:49:16.624, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:49:16.655, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:16.655, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:49:16.655, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.655, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.686, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:16.686, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:16.687, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.687, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.717, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:16.717, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:16.718, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.718, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.749, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:16.749, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:16.749, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.749, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.780, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:16.780, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:16.780, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.780, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.811, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:16.811, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:16.811, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.811, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.827, level=DEBUG, [COM4] Read(13)
time=06/07 09:49:16.827, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=06/07 09:49:16.827, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:49:16.858, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:16.858, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:49:16.858, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.858, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.889, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:16.889, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:16.890, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.890, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.921, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:16.921, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:16.921, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.921, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.952, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:16.952, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:16.952, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.952, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:16.983, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:16.983, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:16.983, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:16.983, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.016, level=WARN, class = Host, msg = Match timeout 33 ms(0)
time=06/07 09:49:17.016, level=WARN, class = BtromHandshake, timeout = 33, message = response timeout
time=06/07 09:49:17.016, level=DEBUG, [COM4] Read(12)
time=06/07 09:49:17.016, level=INFO, [RxP 12] 24504149523439312A33360D
time=06/07 09:49:17.016, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.016, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.032, level=DEBUG, [COM4] Read(1)
time=06/07 09:49:17.032, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 58.53 B/s
time=06/07 09:49:17.032, level=INFO, [RxP 1] 0A
time=06/07 09:49:17.063, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:17.063, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:49:17.063, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.063, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.094, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:17.094, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:17.094, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.094, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.125, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:17.125, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:17.126, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.126, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.157, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:17.157, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:17.157, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.157, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.188, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:17.188, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:17.188, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.188, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.219, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:17.219, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:17.219, level=DEBUG, [COM4] Read(13)
time=06/07 09:49:17.219, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:49:17.219, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.220, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.250, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:17.250, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:17.251, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.251, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.281, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:17.281, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:17.282, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.282, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.313, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:17.313, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:17.313, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.313, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.344, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:17.344, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:17.344, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.344, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.375, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:17.375, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:17.376, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.376, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.407, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:17.407, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:17.407, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.407, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.422, level=DEBUG, [COM4] Read(8)
time=06/07 09:49:17.422, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 33.28 B/s
time=06/07 09:49:17.422, level=INFO, [RxP 8] 2450414952343931
time=06/07 09:49:17.438, level=DEBUG, [COM4] Read(5)
time=06/07 09:49:17.438, level=INFO, [RxP 5] 2A33360D0A
time=06/07 09:49:17.438, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:17.438, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.438, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.469, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:17.469, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:17.469, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.469, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.500, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:17.500, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:17.500, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.500, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.531, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:17.531, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:17.531, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.531, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.551, level=WARN, class = Host, msg = Match timeout 19 ms(0)
time=06/07 09:49:17.582, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:17.582, level=WARN, class = BtromHandshake, timeout = 50, message = response timeout
time=06/07 09:49:17.582, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.582, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.613, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:17.613, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:17.614, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.614, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.629, level=DEBUG, [COM4] Read(13)
time=06/07 09:49:17.629, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 24.20 B/s
time=06/07 09:49:17.629, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:49:17.649, level=WARN, class = Host, msg = Match timeout 19 ms(0)
time=06/07 09:49:17.649, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=06/07 09:49:17.649, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.649, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.680, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:17.680, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:17.681, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.681, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.698, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:49:17.730, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:17.730, level=WARN, class = BtromHandshake, timeout = 48, message = response timeout
time=06/07 09:49:17.730, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.730, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.761, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:17.761, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:17.761, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.761, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.792, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:17.792, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:17.793, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.793, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.824, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:17.824, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:17.824, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.824, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.824, level=DEBUG, [COM4] Read(2)
time=06/07 09:49:17.824, level=INFO, [RxP 2] 2450
time=06/07 09:49:17.839, level=DEBUG, [COM4] Read(11)
time=06/07 09:49:17.839, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 9.51 B/s
time=06/07 09:49:17.839, level=INFO, [RxP 11] 4149523439312A33360D0A
time=06/07 09:49:17.870, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:17.870, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:49:17.871, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.871, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.902, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:17.902, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:17.902, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.902, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.933, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:17.933, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:17.933, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.933, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.964, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:17.964, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:17.964, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.964, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:17.995, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:17.995, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:17.996, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:17.996, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.027, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:18.027, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:18.027, level=DEBUG, [COM4] Read(13)
time=06/07 09:49:18.027, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.027, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.027, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:49:18.067, level=WARN, class = Host, msg = Match timeout 40 ms(0)
time=06/07 09:49:18.067, level=WARN, class = BtromHandshake, timeout = 40, message = response timeout
time=06/07 09:49:18.068, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.068, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.099, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:18.099, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:18.099, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.099, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.130, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:18.130, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:18.130, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.130, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.161, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:18.161, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:18.161, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.161, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.192, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:18.192, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:18.193, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.193, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.224, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:18.224, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:18.224, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.224, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.239, level=DEBUG, [COM4] Read(13)
time=06/07 09:49:18.239, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 32.50 B/s
time=06/07 09:49:18.239, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:49:18.271, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:18.271, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:49:18.271, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.271, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.302, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:18.302, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:18.302, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.302, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.333, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:18.333, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:18.333, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.333, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.364, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:18.364, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:18.364, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.364, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.396, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:18.396, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:18.396, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.396, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.427, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:18.427, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:18.427, level=DEBUG, [COM4] Read(10)
time=06/07 09:49:18.427, level=INFO, [RxP 10] 24504149523439312A33
time=06/07 09:49:18.427, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.427, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.442, level=DEBUG, [COM4] Read(3)
time=06/07 09:49:18.442, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 49.24 B/s
time=06/07 09:49:18.443, level=INFO, [RxP 3] 360D0A
time=06/07 09:49:18.474, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:18.474, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:49:18.474, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.474, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.505, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:18.505, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:18.505, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.505, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.536, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:18.536, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:18.536, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.536, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.567, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:18.567, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:18.567, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.567, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.602, level=WARN, class = Host, msg = Match timeout 34 ms(0)
time=06/07 09:49:18.602, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=06/07 09:49:18.602, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.602, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.633, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:18.633, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:18.633, level=DEBUG, [COM4] Read(13)
time=06/07 09:49:18.633, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:49:18.633, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.633, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.665, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:18.665, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:18.665, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.665, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.696, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:18.696, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:18.697, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.697, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.728, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:18.728, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:18.728, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.728, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.758, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:18.758, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:18.759, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.759, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.789, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:18.789, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:18.789, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.789, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.820, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:18.820, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:18.820, level=DEBUG, [COM4] Read(6)
time=06/07 09:49:18.820, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 34.47 B/s
time=06/07 09:49:18.820, level=INFO, [RxP 6] 245041495234
time=06/07 09:49:18.820, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.820, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.835, level=DEBUG, [COM4] Read(7)
time=06/07 09:49:18.835, level=INFO, [RxP 7] 39312A33360D0A
time=06/07 09:49:18.866, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:18.866, level=WARN, class = BtromHandshake, timeout = 45, message = response timeout
time=06/07 09:49:18.866, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.866, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.896, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=06/07 09:49:18.927, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:18.927, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=06/07 09:49:18.927, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.927, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.959, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:18.959, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:18.959, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.959, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:18.990, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:18.990, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:18.990, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:18.990, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.021, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:19.021, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:19.021, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.021, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.037, level=DEBUG, [COM4] Read(13)
time=06/07 09:49:19.037, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 32.19 B/s
time=06/07 09:49:19.037, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:49:19.066, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=06/07 09:49:19.067, level=WARN, class = BtromHandshake, timeout = 45, message = response timeout
time=06/07 09:49:19.067, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.067, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.098, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:19.098, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:19.098, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.098, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.119, level=WARN, class = Host, msg = Match timeout 20 ms(0)
time=06/07 09:49:19.150, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:19.150, level=WARN, class = BtromHandshake, timeout = 51, message = response timeout
time=06/07 09:49:19.150, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.150, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.181, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:19.181, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:19.181, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.181, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.213, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:19.213, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:19.213, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.213, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.231, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:49:19.246, level=DEBUG, [COM4] Read(13)
time=06/07 09:49:19.246, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=06/07 09:49:19.246, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:49:19.246, level=WARN, class = BtromHandshake, timeout = 33, message = response timeout
time=06/07 09:49:19.246, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.246, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.277, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:19.277, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:19.278, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.278, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.309, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:19.309, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:19.309, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.309, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.340, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:19.340, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:19.340, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.340, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.372, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:19.372, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:19.372, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.372, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.404, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:19.404, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:19.404, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.404, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.435, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:19.435, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:19.435, level=DEBUG, [COM4] Read(12)
time=06/07 09:49:19.435, level=INFO, [RxP 12] 24504149523439312A33360D
time=06/07 09:49:19.435, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.435, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.451, level=DEBUG, [COM4] Read(1)
time=06/07 09:49:19.451, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 58.68 B/s
time=06/07 09:49:19.451, level=INFO, [RxP 1] 0A
time=06/07 09:49:19.482, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:19.482, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:49:19.482, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.482, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.513, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:19.513, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:19.513, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.513, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.544, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:19.544, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:19.544, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.544, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.576, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:19.576, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:19.576, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.576, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.607, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:19.607, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:19.607, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.607, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.638, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:19.638, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:19.638, level=DEBUG, [COM4] Read(13)
time=06/07 09:49:19.638, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:49:19.638, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.638, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.669, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:19.669, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:19.669, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.669, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.700, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:19.700, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:19.700, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.700, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.731, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:19.731, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:19.731, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.731, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.762, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:19.762, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:19.763, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.763, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.794, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:19.794, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:19.794, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.794, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.825, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:19.825, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:19.825, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.825, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.841, level=DEBUG, [COM4] Read(8)
time=06/07 09:49:19.841, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 33.33 B/s
time=06/07 09:49:19.841, level=INFO, [RxP 8] 2450414952343931
time=06/07 09:49:19.856, level=DEBUG, [COM4] Read(5)
time=06/07 09:49:19.856, level=INFO, [RxP 5] 2A33360D0A
time=06/07 09:49:19.856, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:19.857, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.857, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.888, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:19.888, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:19.888, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.888, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.919, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:19.919, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:19.919, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.919, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.950, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:19.950, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:19.950, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.950, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:19.981, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:19.981, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:19.981, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:19.981, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.012, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:20.012, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:20.013, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.013, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.044, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:20.044, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:20.044, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.044, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.044, level=DEBUG, [COM4] Read(13)
time=06/07 09:49:20.044, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 24.61 B/s
time=06/07 09:49:20.044, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:49:20.075, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:20.075, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:20.075, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.075, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.106, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:20.106, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:20.106, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.106, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.137, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:20.137, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:20.138, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.138, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.168, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:20.168, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:20.169, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.169, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.203, level=WARN, class = Host, msg = Match timeout 34 ms(0)
time=06/07 09:49:20.203, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=06/07 09:49:20.204, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.204, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.235, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:20.235, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:20.235, level=DEBUG, [COM4] Read(2)
time=06/07 09:49:20.235, level=INFO, [RxP 2] 2450
time=06/07 09:49:20.235, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.235, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.250, level=DEBUG, [COM4] Read(11)
time=06/07 09:49:20.250, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 9.68 B/s
time=06/07 09:49:20.250, level=INFO, [RxP 11] 4149523439312A33360D0A
time=06/07 09:49:20.282, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:20.282, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:49:20.282, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.282, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.313, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:20.313, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:20.313, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.313, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.344, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:20.344, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:20.344, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.344, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.375, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:20.375, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:20.375, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.375, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.407, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:20.407, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:20.407, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.407, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.438, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:20.438, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:20.438, level=DEBUG, [COM4] Read(13)
time=06/07 09:49:20.438, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:49:20.438, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.438, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.469, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:20.469, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:20.469, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.469, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.500, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:20.500, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:20.500, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.500, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.531, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:20.531, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:20.532, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.532, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.563, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:20.563, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:20.563, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.563, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.594, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:20.594, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:20.594, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.594, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.625, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:20.625, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:20.625, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.625, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.656, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:20.656, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:20.657, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.657, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.657, level=DEBUG, [COM4] Read(13)
time=06/07 09:49:20.657, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 31.99 B/s
time=06/07 09:49:20.657, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:49:20.687, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:20.687, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:20.688, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.688, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.720, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=06/07 09:49:20.720, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=06/07 09:49:20.721, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.721, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.752, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:20.752, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:20.752, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.752, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.783, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:20.783, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:20.783, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.783, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.814, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:20.814, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:20.814, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.814, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.845, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:20.845, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:20.845, level=DEBUG, [COM4] Read(10)
time=06/07 09:49:20.845, level=INFO, [RxP 10] 24504149523439312A33
time=06/07 09:49:20.846, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.846, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.861, level=DEBUG, [COM4] Read(3)
time=06/07 09:49:20.861, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 48.97 B/s
time=06/07 09:49:20.861, level=INFO, [RxP 3] 360D0A
time=06/07 09:49:20.892, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:20.892, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:49:20.892, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.892, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.923, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:20.923, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:20.924, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.924, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.954, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:20.954, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:20.955, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.955, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:20.986, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:20.986, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:20.986, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:20.986, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:21.017, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:21.017, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:49:21.017, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:21.017, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:21.048, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:49:21.048, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:49:21.049, level=DEBUG, [COM4] Write(4)
time=06/07 09:49:21.049, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:49:21.064, level=DEBUG, [COM4] Read(13)
time=06/07 09:49:21.064, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=06/07 09:49:21.064, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:49:21.095, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:49:21.095, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:49:21.095, level=ERROR, class = BtromHandshake, result = fail, timeout = 10033, error_message = btrom handshake timeout
time=06/07 09:49:21.095, level=ERROR, D:\project\crossover\src\task\flash\download_da_uart.cc:141(-1): class = DownloadDa_UART, error_message = btrom handshake fail
time=06/07 09:49:21.095, level=ERROR, class = Task, task_name = DownloadDa_UART, error_message = Fail to execute RunTask()
time=06/07 09:49:21.095, level=DEBUG, class = DownloadDa_UART, task_time = 10.035
time=06/07 09:49:21.095, level=DEBUG, class = Controller, RemoveObserver = callback
time=06/07 09:49:21.095, level=DEBUG, class = CallbackManager, deregister = callback
time=06/07 09:49:21.095, level=DEBUG, class = DisconnectDUT, state = init data, result = ok
time=06/07 09:49:21.219, level=DEBUG, class = SerialHost, state = close
time=06/07 09:49:21.219, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=06/07 09:49:21.219, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=06/07 09:49:21.219, level=DEBUG, class = UartDev, state = disconnect
time=06/07 09:49:21.219, level=DEBUG, class = DisconnectDUT, state = disconnect, device_name = DUT, device_type = UART
time=06/07 09:49:21.219, level=DEBUG, class = DisconnectDUT, task_time = 0.124
time=06/07 09:49:21.219, level=DEBUG, class = LogService, state = CloseLogFile, pass_fail = 1, postfix = 
