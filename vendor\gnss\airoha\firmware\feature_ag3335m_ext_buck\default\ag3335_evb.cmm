SYStem.RESet
SYStem.CPU CORTEXM4
SYStem.CONFIG SWDP ON

system.option enreset off
system.option trst off
SYStem.JtagClock 1MHz
sys.o cflush on
SYStem.Up

setup.IMASKHLL ON
setup.IMASKASM ON

Break.Select Program OnChip
Break.Select Hll OnChip
Break.Select Spot OnChip
Break.Select Read OnChip
Break.Select Write OnChip

;PND all on
;D.S SD:0xA2270320        %LE %LONG 0xFFFFFFFF
;D.S SD:0xA2270350        %LE %LONG 0xFFFFFFFF
;D.S SD:0xA2030B20        %LE %LONG 0xFFFFFFFF

;disable watch dog
D.S SD:0xA2090000 %LE %LONG 0x11
;disable boot slave
D.S SD:0xA2280008 %LE %LONG 0x0
;disable cache 
D.S SD:0xE0180000 %LE %LONG 0x0
;disable mpu 
D.S SD:0xE000ED94 %LE %LONG 0x0
Local &start &end
&start=clock.seconds()

&end=clock.seconds()
Print "Elapsed time is " &end-&start    " seconds"

; ===================================================================
; Download the binary image to PSRAM directly
; ===================================================================
D.LOAD.ELF .\Build\template.elf  /quad

R.S R13 0x04028000		; set stack pointer to the end of CM4 TCM
R.S PC Reset_Handler	; real target bypass BROM effect

; set the path for source level debugging
y.spath.reset
y.spath .\..\..\..\driver\chip\ag3335\src
y.spath .\..\..\..\driver\board\ag3335\

;display source listing around the current PC
winclear
D.L

v.watch %Hex (SCB_Type *)0xE000ED00
v.watch %Hex (NVIC_Type *)0xE000E100
v.watch %Hex (MPU_Type *)0xE000ED90

