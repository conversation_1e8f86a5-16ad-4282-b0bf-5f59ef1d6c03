time=05/29 12:09:46.354, level=INFO, class = LogService, tool_version = 0.2.12/air_bta_ic_premium_g3
time=05/29 12:09:46.350, level=INFO, class = Controller, SetLogLevel = 3
time=05/29 12:09:46.350, level=DEBUG, class = ToolLogLevel, log_level = 3
time=05/29 12:09:46.351, level=DEBUG, class = ToolLogLevel, task_time = 0.000
time=05/29 12:09:46.351, level=DEBUG, class = ConnectDUT, state = init data, result = ok
time=05/29 12:09:46.351, level=DEBUG, class = SerialHost, phy_name = UART, trans_name = h4
time=05/29 12:09:46.351, level=DEBUG, class = Physical, type = 1, state = create
time=05/29 12:09:46.351, level=DEBUG, class = Transport, type = 4, state = create
time=05/29 12:09:46.352, level=INFO, class = UartPhy, desc = USB Serial Port (COM4)
time=05/29 12:09:46.352, level=INFO, class = UartPhy, instance_id = FTDIBUS\VID_0403+PID_6015+DQ014GZWA\0000
time=05/29 12:09:46.352, level=INFO, class = UartPhy, port = 4, baud = 3000000, handshake = 0, read/timeout = (3, 0)
time=05/29 12:09:46.391, level=DEBUG, class = SerialHost, state = open
time=05/29 12:09:46.391, level=DEBUG, class = UartDev, state = connect, ret = 0
time=05/29 12:09:46.391, level=DEBUG, class = ConnectDUT, state = connect, device_name = DUT, device_type = UART, result = ok
time=05/29 12:09:46.391, level=DEBUG, class = ConnectDUT, task_time = 0.040
time=05/29 12:09:46.391, level=DEBUG, class = CallbackManager, register = DUT, filter = 0, cb = 0x005A2C00
time=05/29 12:09:46.391, level=DEBUG, class = UartDev, SetBaudrate = 115200
time=05/29 12:09:46.391, level=DEBUG, class = Host, SwitchTransport = bypass
time=05/29 12:09:46.391, level=DEBUG, class = Transport, type = 1, state = create
time=05/29 12:09:46.391, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=05/29 12:09:46.393, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:46.393, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:46.411, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/29 12:09:46.428, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/29 12:09:46.428, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=05/29 12:09:46.428, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:46.428, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:46.446, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/29 12:09:46.463, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/29 12:09:46.463, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=05/29 12:09:46.463, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:46.463, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:46.481, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/29 12:09:46.499, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/29 12:09:46.499, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=05/29 12:09:46.499, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:46.499, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:46.518, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=05/29 12:09:46.535, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/29 12:09:46.535, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=05/29 12:09:46.535, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:46.535, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:46.553, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=05/29 12:09:46.570, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/29 12:09:46.570, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=05/29 12:09:46.571, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:46.571, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:46.588, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/29 12:09:46.608, level=WARN, class = Host, msg = Match timeout 20 ms(0)
time=05/29 12:09:46.608, level=WARN, class = BtromHandshake, timeout = 37, message = response timeout
time=05/29 12:09:46.609, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:46.609, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:46.639, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:09:46.670, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:46.670, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/29 12:09:46.670, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:46.670, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:46.701, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:46.701, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:46.701, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:46.701, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:46.732, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:46.732, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:46.733, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:46.733, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:46.764, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:46.764, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:46.764, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:46.764, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:46.794, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:46.794, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:46.795, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:46.795, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:46.827, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=05/29 12:09:46.827, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=05/29 12:09:46.827, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:46.827, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:46.857, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:46.857, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:46.858, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:46.858, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:46.889, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:46.889, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:46.889, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:46.889, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:46.920, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:46.920, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:46.920, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:46.920, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:46.951, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:46.951, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:46.951, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:46.951, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:46.982, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:46.982, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:46.982, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:46.982, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.013, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:47.013, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:47.013, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.013, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.045, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:47.045, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:47.045, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.045, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.076, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:47.076, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:47.076, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.076, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.107, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:47.107, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:47.107, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.107, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.138, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:47.138, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:47.139, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.139, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.169, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:09:47.200, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:47.200, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/29 12:09:47.200, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.200, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.231, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:47.231, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:47.232, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.232, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.262, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:47.262, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:47.262, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.262, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.293, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:47.293, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:47.294, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.294, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.324, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:09:47.324, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:47.324, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.324, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.355, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:47.355, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:47.355, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.355, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.386, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:47.386, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:47.386, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.386, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.418, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:47.418, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:47.418, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.418, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.449, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:47.449, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:47.449, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.449, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.480, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:47.480, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:47.480, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.480, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.512, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:47.512, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:47.512, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.512, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.543, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:47.543, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:47.543, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.543, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.574, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:47.574, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:47.574, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.574, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.605, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:47.605, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:47.605, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.605, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.636, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:47.636, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:47.636, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.636, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.667, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:47.667, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:47.667, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.667, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.697, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:09:47.728, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:47.728, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/29 12:09:47.728, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.728, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.759, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:47.759, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:47.760, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.760, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.790, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:47.790, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:47.790, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.790, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.821, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:47.821, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:47.821, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.821, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.852, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:47.852, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:47.852, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.852, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.883, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:47.883, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:47.884, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.884, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.915, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:47.915, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:47.915, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.915, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.945, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:47.945, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:47.945, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.945, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:47.976, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:47.976, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:47.976, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:47.976, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.007, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:48.007, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:48.007, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.007, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.038, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:48.038, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:48.038, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.038, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.069, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:48.069, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:48.069, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.069, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.100, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:48.100, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:48.100, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.100, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.132, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:48.132, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:48.132, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.132, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.162, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:09:48.193, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:48.193, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/29 12:09:48.193, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.193, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.224, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:48.224, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:48.224, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.225, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.255, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:48.255, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:48.255, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.255, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.286, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:48.286, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:48.286, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.286, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.317, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:48.317, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:48.317, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.317, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.348, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:48.348, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:48.348, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.348, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.379, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:48.379, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:48.380, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.380, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.411, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:48.411, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:48.412, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.412, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.444, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:48.444, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:48.444, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.444, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.474, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:48.474, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:48.474, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.474, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.504, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:09:48.535, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:48.535, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/29 12:09:48.535, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.535, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.567, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:48.567, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:48.567, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.567, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.598, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:48.598, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:48.598, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.598, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.629, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:48.629, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:48.630, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.630, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.662, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:48.662, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:48.662, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.662, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.693, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:48.693, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:48.693, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.693, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.724, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:48.724, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:48.724, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.724, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.755, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:48.755, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:48.756, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.756, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.787, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:48.787, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:48.788, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.788, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.818, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:48.818, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:48.819, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.819, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.849, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:48.849, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:48.850, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.850, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.881, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:48.881, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:48.881, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.881, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.912, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:48.912, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:48.912, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.912, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.945, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=05/29 12:09:48.945, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=05/29 12:09:48.945, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.945, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:48.975, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:48.975, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:48.975, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:48.975, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.006, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:49.006, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:49.007, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.007, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.037, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:49.037, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:49.038, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.038, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.068, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:49.068, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:49.068, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.068, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.100, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:49.100, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:49.100, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.100, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.130, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:49.130, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:49.131, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.131, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.162, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:49.162, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:49.162, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.162, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.192, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:49.192, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:49.193, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.193, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.223, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:49.223, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:49.224, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.224, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.254, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:49.254, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:49.254, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.254, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.285, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:49.285, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:49.286, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.286, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.317, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:49.317, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:49.317, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.317, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.348, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:49.348, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:49.348, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.348, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.380, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:49.380, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:49.380, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.380, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.412, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:49.412, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:49.412, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.412, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.444, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:49.444, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:49.444, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.444, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.475, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:49.475, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:49.475, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.475, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.507, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:49.507, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:49.507, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.507, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.538, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:49.538, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:49.538, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.538, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.569, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:49.569, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:49.569, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.569, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.600, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:49.600, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:49.600, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.600, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.631, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:49.631, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:49.632, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.632, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.663, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:49.663, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:49.663, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.663, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.694, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:49.694, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:49.694, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.694, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.725, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:49.725, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:49.725, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.725, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.756, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:49.756, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:49.757, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.757, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.787, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:49.787, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:49.787, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.787, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.817, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:09:49.848, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:49.848, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/29 12:09:49.848, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.848, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.879, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:49.879, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:49.880, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.880, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.910, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:49.910, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:49.911, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.911, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.942, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:49.942, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:49.942, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.942, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:49.973, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:49.973, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:49.974, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:49.974, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.004, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:50.004, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:50.005, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.005, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.036, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:50.036, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:50.036, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.036, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.067, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:50.067, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:50.067, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.067, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.098, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:50.098, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:50.098, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.098, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.129, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:50.129, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:50.130, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.130, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.161, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:50.161, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:50.161, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.161, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.192, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:50.192, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:50.192, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.192, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.223, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:50.223, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:50.223, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.223, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.254, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:50.254, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:50.254, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.254, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.285, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:50.285, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:50.285, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.285, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.316, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:50.316, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:50.316, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.316, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.347, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:50.347, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:50.347, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.347, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.378, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:50.378, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:50.378, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.378, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.409, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:50.409, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:50.409, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.409, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.440, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:50.440, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:50.440, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.440, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.472, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:50.472, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:50.472, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.472, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.503, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:50.503, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:50.503, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.503, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.534, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:50.534, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:50.534, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.534, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.565, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:50.565, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:50.565, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.565, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.596, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:50.596, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:50.596, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.596, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.627, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:50.627, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:50.627, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.627, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.658, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:50.658, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:50.658, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.658, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.689, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:50.689, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:50.689, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.689, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.720, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:50.720, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:50.720, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.720, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.751, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:50.751, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:50.751, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.751, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.782, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:50.782, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:50.783, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.783, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.813, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:50.813, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:50.813, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.813, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.845, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:50.845, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:50.845, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.845, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.876, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:50.876, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:50.877, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.877, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.907, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:50.907, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:50.907, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.907, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.938, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:50.938, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:50.938, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.938, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.969, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:50.969, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:50.969, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:50.969, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:50.999, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:50.999, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:51.000, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.000, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.031, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:51.031, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:51.031, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.031, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.062, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:51.062, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:51.062, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.062, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.093, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:51.093, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:51.093, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.093, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.124, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:51.124, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:51.124, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.124, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.155, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:51.155, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:51.155, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.155, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.186, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:51.186, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:51.186, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.186, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.216, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:51.216, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:51.216, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.216, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.247, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:51.247, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:51.247, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.247, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.278, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:51.278, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:51.278, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.278, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.308, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:51.308, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:51.309, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.309, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.339, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:51.339, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:51.339, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.339, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.370, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:51.370, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:51.371, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.371, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.400, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:09:51.432, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:51.432, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/29 12:09:51.432, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.432, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.463, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:51.463, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:51.463, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.463, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.494, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:51.494, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:51.494, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.494, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.525, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:51.525, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:51.525, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.525, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.555, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:51.555, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:51.555, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.555, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.586, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:51.586, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:51.586, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.586, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.618, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:51.618, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:51.618, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.618, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.648, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:51.648, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:51.648, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.648, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.679, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:51.679, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:51.679, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.679, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.711, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:51.711, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:51.711, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.711, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.742, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:51.742, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:51.742, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.742, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.773, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:51.773, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:51.773, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.773, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.804, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:51.804, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:51.804, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.804, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.835, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:51.835, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:51.835, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.835, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.867, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:51.867, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:51.867, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.867, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.898, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:51.898, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:51.898, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.898, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.930, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:51.930, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:51.930, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.930, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.962, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:51.962, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:51.962, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.962, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:51.993, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:51.993, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:51.993, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:51.993, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.024, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:52.024, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:52.024, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.024, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.055, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:52.055, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:52.055, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.055, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.086, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:52.086, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:52.087, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.087, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.117, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:52.117, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:52.117, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.117, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.149, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:52.149, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:52.149, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.149, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.181, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:52.181, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:52.181, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.181, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.212, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:52.212, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:52.212, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.212, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.244, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:52.244, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:52.244, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.244, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.274, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:52.274, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:52.274, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.274, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.306, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:52.306, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:52.306, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.306, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.338, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:52.338, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:52.338, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.338, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.369, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:52.369, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:52.369, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.369, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.400, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:52.400, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:52.401, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.401, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.432, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:52.432, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:52.432, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.432, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.463, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:52.463, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:52.463, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.463, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.494, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:52.494, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:52.494, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.494, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.524, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:09:52.556, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:52.556, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/29 12:09:52.556, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.556, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.586, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:52.586, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:52.587, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.587, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.617, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:52.617, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:52.618, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.618, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.648, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:52.648, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:52.649, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.649, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.679, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:52.679, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:52.680, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.680, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.710, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:52.710, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:52.711, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.711, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.741, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:52.741, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:52.741, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.741, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.772, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:52.772, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:52.772, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.772, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.804, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:52.804, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:52.804, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.804, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.836, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:52.836, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:52.836, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.836, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.866, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:52.866, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:52.867, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.867, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.898, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:52.898, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:52.898, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.898, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.928, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:52.928, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:52.928, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.928, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.960, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:52.960, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:52.960, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.960, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:52.992, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:52.992, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:52.992, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:52.992, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.023, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:53.023, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:53.023, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.023, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.054, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:53.054, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:53.054, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.054, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.084, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:53.084, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:53.085, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.085, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.116, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:53.116, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:53.116, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.116, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.147, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:53.147, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:53.147, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.147, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.178, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:53.178, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:53.178, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.178, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.210, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:53.210, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:53.210, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.210, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.241, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:53.241, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:53.242, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.242, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.273, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:53.273, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:53.273, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.273, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.304, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:53.304, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:53.304, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.304, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.335, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:53.335, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:53.335, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.335, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.366, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:53.366, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:53.367, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.367, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.397, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:53.397, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:53.398, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.398, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.428, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:53.428, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:53.429, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.429, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.458, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:09:53.489, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:53.489, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/29 12:09:53.489, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.489, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.520, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:53.520, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:53.520, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.520, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.550, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:53.550, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:53.551, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.551, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.582, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:53.582, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:53.582, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.582, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.612, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:53.612, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:53.612, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.612, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.643, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:53.643, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:53.644, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.644, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.675, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:53.675, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:53.675, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.675, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.706, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:53.706, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:53.706, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.706, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.737, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:53.737, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:53.737, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.737, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.769, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:53.769, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:53.769, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.769, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.800, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:53.800, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:53.800, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.800, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.831, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:53.831, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:53.831, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.831, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.862, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:53.862, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:53.862, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.862, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.893, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:53.893, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:53.893, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.893, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.924, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:53.924, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:53.924, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.924, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.955, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:53.955, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:53.956, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.956, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:53.986, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:53.986, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:53.986, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:53.986, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.017, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:54.017, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:54.018, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.018, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.049, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:54.049, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:54.049, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.049, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.080, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:54.080, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:54.080, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.080, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.112, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:54.112, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:54.112, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.112, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.143, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:54.143, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:54.143, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.143, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.174, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:54.174, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:54.174, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.174, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.205, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:54.205, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:54.205, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.205, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.235, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:09:54.267, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:54.267, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/29 12:09:54.267, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.267, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.297, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:09:54.328, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:54.328, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/29 12:09:54.329, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.329, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.359, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:54.359, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:54.359, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.359, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.390, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:54.390, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:54.391, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.391, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.421, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:54.421, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:54.422, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.422, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.452, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:54.452, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:54.453, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.453, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.484, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:54.484, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:54.485, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.485, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.515, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:54.515, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:54.515, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.515, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.545, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:09:54.577, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:54.577, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/29 12:09:54.577, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.577, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.608, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:54.608, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:54.608, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.608, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.638, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:54.638, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:54.638, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.638, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.669, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:54.669, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:54.669, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.669, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.700, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:54.700, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:54.700, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.700, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.730, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:54.730, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:54.731, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.731, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.761, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:54.761, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:54.761, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.761, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.793, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:54.793, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:54.794, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.794, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.824, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:54.824, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:54.825, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.825, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.856, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:54.856, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:54.856, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.856, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.887, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:54.887, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:54.888, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.888, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.918, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:54.918, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:54.919, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.919, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.949, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:54.949, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:54.950, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.950, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:54.980, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:54.980, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:54.981, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:54.981, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.012, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:55.012, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:55.012, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.012, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.043, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:55.043, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:55.043, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.043, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.074, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:55.074, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:55.074, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.074, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.106, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:55.106, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:55.106, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.106, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.137, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:55.137, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:55.138, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.138, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.168, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:09:55.199, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:55.199, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/29 12:09:55.200, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.200, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.230, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:55.230, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:55.231, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.231, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.262, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:55.262, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:55.262, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.262, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.293, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:55.293, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:55.294, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.294, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.324, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:55.324, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:55.324, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.324, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.355, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:55.355, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:55.356, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.356, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.386, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:55.386, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:55.386, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.386, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.417, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:55.417, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:55.417, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.417, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.449, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:55.449, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:55.449, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.449, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.479, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:55.479, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:55.480, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.480, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.511, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:55.511, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:55.511, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.511, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.542, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:55.542, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:55.542, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.542, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.572, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:55.572, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:55.573, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.573, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.603, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:55.603, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:55.604, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.604, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.635, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:55.635, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:55.635, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.635, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.666, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:55.666, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:55.667, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.667, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.697, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:55.697, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:55.697, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.697, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.728, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:55.728, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:55.729, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.729, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.760, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:55.760, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:55.760, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.760, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.791, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:55.791, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:55.791, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.791, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.823, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:55.823, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:55.823, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.823, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.854, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:55.854, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:55.854, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.854, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.885, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:55.885, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:55.885, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.885, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.917, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:55.917, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:55.917, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.917, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.947, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:55.947, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:55.947, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.947, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:55.978, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:55.978, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:55.978, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:55.978, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:56.008, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:56.008, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:56.009, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:56.009, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:56.040, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:56.040, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:56.040, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:56.040, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:56.071, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:56.071, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:56.071, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:56.071, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:56.102, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:56.102, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:56.102, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:56.102, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:56.132, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:56.132, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:56.133, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:56.133, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:56.163, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:56.163, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:56.164, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:56.164, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:56.195, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:56.195, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:56.195, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:56.195, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:56.226, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:56.226, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:56.226, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:56.226, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:56.257, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:56.257, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:56.258, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:56.258, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:56.288, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:56.288, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:56.288, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:56.288, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:56.319, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:09:56.319, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:09:56.319, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:56.319, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:56.350, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:56.350, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:56.350, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:56.350, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:56.380, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:56.380, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:56.381, level=DEBUG, [COM4] Write(4)
time=05/29 12:09:56.381, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:09:56.411, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:09:56.411, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:09:56.411, level=ERROR, class = BtromHandshake, result = fail, timeout = 10018, error_message = btrom handshake timeout
time=05/29 12:09:56.412, level=ERROR, D:\project\crossover\src\task\flash\download_da_uart.cc:141(-1): class = DownloadDa_UART, error_message = btrom handshake fail
time=05/29 12:09:56.412, level=ERROR, class = Task, task_name = DownloadDa_UART, error_message = Fail to execute RunTask()
time=05/29 12:09:56.412, level=DEBUG, class = DownloadDa_UART, task_time = 10.021
time=05/29 12:09:56.412, level=DEBUG, class = Controller, RemoveObserver = callback
time=05/29 12:09:56.412, level=DEBUG, class = CallbackManager, deregister = callback
time=05/29 12:09:56.412, level=DEBUG, class = DisconnectDUT, state = init data, result = ok
time=05/29 12:09:56.521, level=DEBUG, class = SerialHost, state = close
time=05/29 12:09:56.521, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=05/29 12:09:56.521, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=05/29 12:09:56.521, level=DEBUG, class = UartDev, state = disconnect
time=05/29 12:09:56.521, level=DEBUG, class = DisconnectDUT, state = disconnect, device_name = DUT, device_type = UART
time=05/29 12:09:56.521, level=DEBUG, class = DisconnectDUT, task_time = 0.110
time=05/29 12:09:56.521, level=DEBUG, class = LogService, state = CloseLogFile, pass_fail = 1, postfix = 
