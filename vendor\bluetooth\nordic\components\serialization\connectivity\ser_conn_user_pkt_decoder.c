#include "ser_conn_user_pkt_decoder.h"
#include "nordic_common.h"
#include "app_error.h"
#include "ble_serialization.h"
#include "ser_config.h"
#include "nrf_sdh.h"
#include "ser_hal_transport.h"
#include "ser_conn_cmd_decoder.h"
#include "ser_conn_handlers.h"
#include "app_timer.h"
#include "nrf_nvic.h"
#include "boards.h"
#include "ser_phy.h"
#include "nrf_sdm.h"
#include "app_scheduler.h"
#include "app_scheduler_ant.h"
#include "ser_phy_config_conn.h"

/**@brief Connectivity middleware handler type. */
typedef uint32_t (*user_conn_mw_handler_t)(uint8_t const * const p_rx_buf,
                                      uint32_t              rx_buf_len,
                                      uint8_t * const       p_tx_buf,
                                      uint32_t * const      p_tx_buf_len);

/**@brief Connectivity middleware item. */
typedef struct
{
    uint8_t           opcode;     /**< Opcode by which specific codec is identified */
    user_conn_mw_handler_t fp_handler; /**< Function pointer to handler associated with given opcode */
} user_conn_mw_item_t;

static uint32_t conn_user_data_write(uint8_t const * const p_rx_buf,
                             uint32_t              rx_buf_len,
                             uint8_t * const       p_tx_buf,
                             uint32_t * const      p_tx_buf_len);
static uint32_t conn_user_data_read(uint8_t const * const p_rx_buf,
                             uint32_t              rx_buf_len,
                             uint8_t * const       p_tx_buf,
                             uint32_t * const      p_tx_buf_len);
static void ser_user_settings_parser(void);

static const user_conn_mw_item_t user_conn_mw_item[] = {
  {SER_BLE_USER_DATA_WRITE,conn_user_data_write},
  {SER_BLE_USER_DATA_READ,conn_user_data_read},
};

/**@brief Number of registered connectivity middleware handlers. */
static const uint32_t user_conn_mw_item_len = sizeof (user_conn_mw_item) / sizeof (user_conn_mw_item[0]);

static ser_user_data user_data = {0,0,0,0,0,0,0,0,\
                                  0,0,0,0,0,0,0,BLE_VERSION};

static ser_user_settings *const user_settings = (ser_user_settings *)&user_data;
static bool m_settings_timer_created = false;
APP_TIMER_DEF(settings_timer);

ser_user_data* ser_conn_get_master_settings(void)
{
    return &user_data;
}

/**@brief Local function for finding connectivity middleware handler in the table.. */
static user_conn_mw_handler_t user_conn_mw_handler_get(uint8_t opcode)
{
    user_conn_mw_handler_t fp_handler = NULL;
    uint32_t          i;

    for (i = 0; i < user_conn_mw_item_len; i++)
    {
        if (opcode == user_conn_mw_item[i].opcode)
        {
            fp_handler = user_conn_mw_item[i].fp_handler;
            break;
        }
    }

    return fp_handler;
}

static uint32_t user_conn_mw_handler(uint8_t const * const p_rx_buf,
                         uint32_t              rx_buf_len,
                         uint8_t * const       p_tx_buf,
                         uint32_t * const      p_tx_buf_len)
{
    SER_ASSERT_NOT_NULL(p_rx_buf);
    SER_ASSERT_NOT_NULL(p_tx_buf);
    SER_ASSERT_NOT_NULL(p_tx_buf_len);

    user_conn_mw_handler_t fp_handler;
    uint32_t          err_code = NRF_SUCCESS;
    uint8_t           opcode   = p_rx_buf[SER_CMD_OP_CODE_POS];

    fp_handler = user_conn_mw_handler_get(opcode);

    if (fp_handler)
    {
        err_code = fp_handler(p_rx_buf, rx_buf_len, p_tx_buf, p_tx_buf_len);
    }
    else
    {
        err_code = NRF_ERROR_NOT_SUPPORTED;
    }

    return err_code;
}

uint32_t ser_conn_user_command_process(uint8_t * p_command, uint16_t command_len)
{
    SER_ASSERT_NOT_NULL(p_command);
    SER_ASSERT_LENGTH_LEQ(SER_OP_CODE_SIZE, command_len);

    uint32_t  err_code   = NRF_SUCCESS;
    uint8_t * p_tx_buf   = NULL;
    uint32_t  tx_buf_len = 0;
    uint8_t   opcode     = p_command[SER_CMD_OP_CODE_POS];
    uint32_t  index      = 0;

    do
    {
        err_code = ser_hal_transport_tx_pkt_alloc(&p_tx_buf, (uint16_t *)&tx_buf_len);
        if (err_code == NRF_ERROR_NO_MEM)
        {
            ser_conn_on_no_mem_handler();
        }
    }
    while (NRF_ERROR_NO_MEM == err_code);

    if (NRF_SUCCESS == err_code)
    {
        /* Create a new response packet. */
        p_tx_buf[SER_PKT_TYPE_POS] = SER_PKT_USER_RESP;
        tx_buf_len                -= SER_PKT_TYPE_SIZE;

        /* Decode a request, pass a memory for a response command (opcode + data) and encode it. */
        err_code = user_conn_mw_handler
                       (p_command, command_len, &p_tx_buf[SER_PKT_OP_CODE_POS], &tx_buf_len);


        /* Command decoder not found. */
        if (NRF_ERROR_NOT_SUPPORTED == err_code)
        {
            err_code = op_status_enc
                           (opcode, NRF_ERROR_NOT_SUPPORTED,
                           &p_tx_buf[SER_PKT_OP_CODE_POS], &tx_buf_len, &index);
            if (NRF_SUCCESS == err_code)
            {
                tx_buf_len += SER_PKT_TYPE_SIZE;
                err_code   = ser_hal_transport_tx_pkt_send(p_tx_buf, (uint16_t)tx_buf_len);
                /* TX buffer is going to be freed automatically in the HAL Transport layer. */
                if (NRF_SUCCESS != err_code)
                {
                    err_code = NRF_ERROR_INTERNAL;
                }
            }
            else
            {
                err_code = NRF_ERROR_INTERNAL;
            }
        }
        else if (NRF_SUCCESS == err_code) /* Send a response. */
        {
            tx_buf_len += SER_PKT_TYPE_SIZE;
            err_code    = ser_hal_transport_tx_pkt_send(p_tx_buf, (uint16_t)tx_buf_len);

            /* TX buffer is going to be freed automatically in the HAL Transport layer. */
            if (NRF_SUCCESS != err_code)
            {
                err_code = NRF_ERROR_INTERNAL;
            }
        }
        else
        {
            err_code = NRF_ERROR_INTERNAL;
        }
    }
    else
    {
        err_code = NRF_ERROR_INTERNAL;
    }

    return err_code;
}
/*****************************************************************************************/
static uint32_t _conn_user_data_write_dec(uint8_t const * const    p_buf,
                                  uint32_t                 packet_len)
{
    SER_REQ_DEC_BEGIN(SER_BLE_USER_DATA_WRITE);
    if(!user_settings->test_communication_mode){
        memcpy(&user_data,&p_buf[index],(packet_len - index));
        index += SER_USER_DATA_SIZE;
    }else{
        index = packet_len;
    }
    SER_REQ_DEC_END;
}

static uint32_t conn_user_data_write_rsp_enc(uint32_t                return_code,
                             uint8_t * const         p_buf,
                             uint32_t * const        p_buf_len)
{
    SER_RSP_ENC_RESULT_ONLY(SER_BLE_USER_DATA_WRITE);
}

static uint32_t conn_user_data_write(uint8_t const * const p_rx_buf,
                             uint32_t              rx_buf_len,
                             uint8_t * const       p_tx_buf,
                             uint32_t * const      p_tx_buf_len)
{
    uint32_t  err_code   = NRF_SUCCESS;
    SER_ASSERT_NOT_NULL(p_rx_buf);
    SER_ASSERT_NOT_NULL(p_tx_buf);
    SER_ASSERT_NOT_NULL(p_tx_buf_len);
    
    err_code = _conn_user_data_write_dec(p_rx_buf, rx_buf_len);
    SER_ASSERT(err_code == NRF_SUCCESS, err_code);

    if(err_code == NRF_SUCCESS){
        ser_user_settings_parser();
    }

    uint32_t   sd_err_code = 0;
    err_code = conn_user_data_write_rsp_enc(sd_err_code, p_tx_buf, p_tx_buf_len);
    SER_ASSERT(err_code == NRF_SUCCESS, err_code);
    
    return err_code;
}

static uint64_t GetDeviceUUID(void)
{
    uint64_t uuid;
    uuid = NRF_FICR->DEVICEID[1];
    uuid = uuid << 32;
    uuid += NRF_FICR->DEVICEID[0];
    return uuid;
}

static uint32_t conn_user_data_read_dec(uint8_t const * const    p_buf,
                                  uint32_t                 packet_len)
{
    SER_REQ_DEC_BEGIN(SER_BLE_USER_DATA_READ);
    SER_REQ_DEC_END;
}

static uint32_t conn_user_data_read_rsp_enc(uint8_t * const         p_buf,
                             uint32_t * const        p_buf_len)
{
    SER_REQ_ENC_BEGIN(SER_BLE_USER_DATA_READ);
    uint8_t i = 0;
    user_settings->uuid =  (uint64_t)GetDeviceUUID();
    for(;i < SER_USER_DATA_SIZE;i ++){
    	SER_PUSH_int8(&(user_data.data[i]));
    }
    SER_REQ_ENC_END;
}

static uint32_t conn_user_data_read(uint8_t const * const p_rx_buf,
                             uint32_t              rx_buf_len,
                             uint8_t * const       p_tx_buf,
                             uint32_t * const      p_tx_buf_len)
{
    uint32_t  err_code   = NRF_SUCCESS;
    SER_ASSERT_NOT_NULL(p_rx_buf);
    SER_ASSERT_NOT_NULL(p_tx_buf);
    SER_ASSERT_NOT_NULL(p_tx_buf_len);
    
    err_code = conn_user_data_read_dec(p_rx_buf, rx_buf_len);
    SER_ASSERT(err_code == NRF_SUCCESS, err_code);

    err_code = conn_user_data_read_rsp_enc(p_tx_buf, p_tx_buf_len);
    SER_ASSERT(err_code == NRF_SUCCESS, err_code);

    return err_code;
}

/*****************************************************************************************/
static void settings_timer_handler(void * p_ctx)
{
    if(user_settings->enter_dfu){
        NRF_POWER->GPREGRET = 0xB1;
        user_settings->master_sleep = true;
        nrf_sdh_suspend();
        ser_phy_close();
        __sd_nvic_irq_disable();
        sd_nvic_SystemReset();
        user_settings->enter_dfu = false;
    }else if(user_settings->sys_poweroff){
        nrf_sdh_suspend();
        sd_softdevice_disable();
        ser_phy_close();
        nrf_gpio_cfg_default(SLAVE_IDLE_STATE_PIN);
        user_settings->sys_poweroff = false;
        NRF_POWER->SYSTEMOFF = 0x01;
    }
}

static void ser_user_settings_parser(void)
{
    if(!user_settings->test_communication_mode){
        if(user_settings->enter_dfu == true){
            if(!m_settings_timer_created){
                app_timer_create(&settings_timer, APP_TIMER_MODE_SINGLE_SHOT, settings_timer_handler);
                m_settings_timer_created = true;
            }
            app_timer_start(settings_timer, APP_TIMER_TICKS(100), NULL);
            
        }else if(user_settings->master_sleep == true){
			app_sched_ant_queue_clear();
            app_sched_queue_clear();
		}else if(user_settings->softdevice_disable == true){
            nrf_sdh_disable_request();
            user_settings->softdevice_disable = false;
        }else if(user_settings->sdh_suspend == true){
            nrf_sdh_suspend();
            user_settings->sdh_suspend = false;
        }else if(user_settings->sdh_resume == true){
            nrf_sdh_resume();
            user_settings->sdh_resume = false;
        }else if(user_settings->sys_poweroff == true){
            if(!m_settings_timer_created){
                app_timer_create(&settings_timer, APP_TIMER_MODE_SINGLE_SHOT, settings_timer_handler);
                m_settings_timer_created = true;
            }
            app_timer_start(settings_timer, APP_TIMER_TICKS(20), NULL);
        }
    }
}

bool master_is_sleep(void)
{
    return user_settings->master_sleep;
}
