TARGETNAME=dev_inst64
TARGETTYPE=PROGRAM
TARGET_DESTINATION=dbg\layout
USE_MSVCRT=1

#MSC_WARNING_LEVEL=/W4 /WX
MSC_WARNING_LEVEL=/WX

MUI=0
MUI_COMMENT=TO_COMMENT, old disabled, owners need to contact MUICore to get new comment and comment code

UMTYPE=windows
UMENTRY=winmain
UMBASE=0x1000000

!if $(_NT_TARGET_VERSION) != $(_NT_TARGET_VERSION_WS03)  
# Use current headers (don't set _NT_TARGET_VERSION)
# Run downlevel to XP
SUBSYSTEM_VERSION=$(SUBSYSTEM_501)
!endif

#C_DEFINES = $(C_DEFINES) -DUNICODE -D_UNICODE
C_DEFINES = $(C_DEFINES)

TARGETLIBS=\
        $(SDK_LIB_PATH)\kernel32.lib    \
        $(SDK_LIB_PATH)\user32.lib      \
        $(SDK_LIB_PATH)\gdi32.lib       \
        $(SDK_LIB_PATH)\comctl32.lib    \
        $(SDK_LIB_PATH)\cfgmgr32.lib    \
        $(SDK_LIB_PATH)\setupapi.lib	\
		$(SDK_LIB_PATH)\newdev.lib

#INCLUDES=..\inc;..\..\inc

SOURCES=dev_inst.c   




