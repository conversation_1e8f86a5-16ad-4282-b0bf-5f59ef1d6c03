time=05/29 12:23:11.641, level=INFO, class = LogService, tool_version = 0.2.12/air_bta_ic_premium_g3
time=05/29 12:23:11.638, level=INFO, class = Controller, SetLogLevel = 3
time=05/29 12:23:11.638, level=DEBUG, class = ToolLogLevel, log_level = 3
time=05/29 12:23:11.638, level=DEBUG, class = ToolLogLevel, task_time = 0.000
time=05/29 12:23:11.638, level=DEBUG, class = ConnectDUT, state = init data, result = ok
time=05/29 12:23:11.638, level=DEBUG, class = SerialHost, phy_name = UART, trans_name = h4
time=05/29 12:23:11.638, level=DEBUG, class = Physical, type = 1, state = create
time=05/29 12:23:11.638, level=DEBUG, class = Transport, type = 4, state = create
time=05/29 12:23:11.639, level=INFO, class = UartPhy, port = 4, baud = 3000000, handshake = 0, read/timeout = (3, 0)
time=05/29 12:23:11.639, level=ERROR, class = UartPhy, error_code = 4, error_message = Unable to open COM2
time=05/29 12:23:11.639, level=DEBUG, class = SerialHost, state = open
time=05/29 12:23:11.639, level=DEBUG, class = UartDev, state = connect, ret = 2
time=05/29 12:23:11.639, level=ERROR, class = UartPhy, error_message = no device to read
time=05/29 12:23:11.640, level=ERROR, D:\project\crossover\src\task\connect_device.cc:199(-1): class = ConnectDUT, device_name = DUT, device_type = UART, error_message = connect fail
time=05/29 12:23:11.640, level=ERROR, class = Task, task_name = ConnectDUT, error_message = Fail to execute RunTask()
time=05/29 12:23:11.640, level=DEBUG, class = ConnectDUT, task_time = 0.002
time=05/29 12:23:11.640, level=DEBUG, class = LogService, state = CloseLogFile, pass_fail = 1, postfix = 
