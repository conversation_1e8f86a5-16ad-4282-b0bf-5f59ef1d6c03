time=05/28 11:37:42.898, level=INFO, class = LogService, tool_version = 0.2.12/air_bta_ic_premium_g3
time=05/28 11:37:42.893, level=INFO, class = Controller, SetLogLevel = 3
time=05/28 11:37:42.893, level=DEBUG, class = ToolLogLevel, log_level = 3
time=05/28 11:37:42.893, level=DEBUG, class = ToolLogLevel, task_time = 0.000
time=05/28 11:37:42.893, level=DEBUG, class = ConnectDUT, state = init data, result = ok
time=05/28 11:37:42.893, level=DEBUG, class = SerialHost, phy_name = UART, trans_name = h4
time=05/28 11:37:42.893, level=DEBUG, class = Physical, type = 1, state = create
time=05/28 11:37:42.893, level=DEBUG, class = Transport, type = 4, state = create
time=05/28 11:37:42.895, level=INFO, class = UartPhy, desc = USB Serial Port (COM4)
time=05/28 11:37:42.895, level=INFO, class = UartPhy, instance_id = FTDIBUS\VID_0403+PID_6015+DQ014GZWA\0000
time=05/28 11:37:42.895, level=INFO, class = UartPhy, port = 4, baud = 3000000, handshake = 0, read/timeout = (3, 0)
time=05/28 11:37:42.935, level=DEBUG, class = SerialHost, state = open
time=05/28 11:37:42.935, level=DEBUG, class = UartDev, state = connect, ret = 0
time=05/28 11:37:42.935, level=DEBUG, class = ConnectDUT, state = connect, device_name = DUT, device_type = UART, result = ok
time=05/28 11:37:42.935, level=DEBUG, class = ConnectDUT, task_time = 0.042
time=05/28 11:37:42.935, level=DEBUG, class = CallbackManager, register = DUT, filter = 0, cb = 0x00D42C00
time=05/28 11:37:42.935, level=DEBUG, class = UartDev, SetBaudrate = 115200
time=05/28 11:37:42.935, level=DEBUG, class = Host, SwitchTransport = bypass
time=05/28 11:37:42.935, level=DEBUG, class = Transport, type = 1, state = create
time=05/28 11:37:42.935, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=05/28 11:37:42.938, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:42.938, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:42.955, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/28 11:37:42.973, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/28 11:37:42.973, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=05/28 11:37:42.973, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:42.973, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:42.991, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=05/28 11:37:43.008, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/28 11:37:43.008, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=05/28 11:37:43.008, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.008, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.026, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/28 11:37:43.044, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/28 11:37:43.044, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=05/28 11:37:43.044, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.044, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.062, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=05/28 11:37:43.079, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/28 11:37:43.079, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=05/28 11:37:43.079, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.079, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.097, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=05/28 11:37:43.114, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/28 11:37:43.114, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=05/28 11:37:43.115, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.115, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.132, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/28 11:37:43.155, level=WARN, class = Host, msg = Match timeout 23 ms(0)
time=05/28 11:37:43.155, level=WARN, class = BtromHandshake, timeout = 40, message = response timeout
time=05/28 11:37:43.156, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.156, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.185, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:37:43.217, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:43.217, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/28 11:37:43.217, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.217, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.248, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:43.248, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:43.248, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.248, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.279, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:43.279, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:43.280, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.280, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.311, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:43.311, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:43.312, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.312, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.342, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:43.342, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:43.343, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.343, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.373, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:43.373, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:43.374, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.374, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.404, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:43.404, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:43.404, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.404, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.436, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:43.436, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:43.437, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.437, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.467, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:43.467, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:43.467, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.467, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.498, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:43.498, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:43.498, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.498, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.529, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:43.529, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:43.530, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.530, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.560, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:43.560, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:43.560, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.560, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.590, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:43.590, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:43.591, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.591, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.622, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:43.622, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:43.623, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.623, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.653, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:43.653, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:43.654, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.654, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.685, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:43.685, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:43.685, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.685, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.717, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:43.717, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:43.717, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.717, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.748, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:43.748, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:43.748, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.748, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.780, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:43.780, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:43.780, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.780, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.809, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:37:43.841, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:43.841, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/28 11:37:43.842, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.842, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.872, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:43.872, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:43.873, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.873, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.904, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:43.904, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:43.904, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.904, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.935, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:43.935, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:43.935, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.935, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.966, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:43.966, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:43.966, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.966, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:43.997, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:43.997, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:43.998, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:43.998, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.029, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:44.029, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:44.029, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.029, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.060, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:44.060, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:44.061, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.061, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.061, level=DEBUG, [COM4] Read(18)
time=05/28 11:37:44.061, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=05/28 11:37:44.061, level=INFO, [RxP 18] 24504149523338322C312A32450D0A245041
time=05/28 11:37:44.076, level=DEBUG, [COM4] Read(30)
time=05/28 11:37:44.076, level=INFO, [RxP 30] 49523130302C312C302A33410D0A24504149523036322C312C302A33460D
time=05/28 11:37:44.092, level=DEBUG, [COM4] Read(32)
time=05/28 11:37:44.092, level=INFO, [RxP 32] 0A24504149523036322C352C302A33420D0A24504149523036322C332C302A33
time=05/28 11:37:44.092, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:44.092, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.092, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.107, level=DEBUG, [COM4] Read(22)
time=05/28 11:37:44.107, level=INFO, [RxP 22] 440D0A24504149523038302C302A32450D0A24504149
time=05/28 11:37:44.123, level=DEBUG, [COM4] Read(32)
time=05/28 11:37:44.123, level=INFO, [RxP 32] 523036362C312C312C312C312C312C302A33420D0A24504149523038312A3333
time=05/28 11:37:44.123, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:44.123, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.123, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.138, level=DEBUG, [COM4] Read(17)
time=05/28 11:37:44.138, level=INFO, [RxP 17] 0D0A24504149523439302C312A32410D0A
time=05/28 11:37:44.169, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:44.169, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=05/28 11:37:44.169, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.169, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.200, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:44.200, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:44.201, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.201, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.231, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:44.231, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:44.232, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.232, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.263, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:44.263, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:44.263, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.263, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.294, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:44.294, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:44.294, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.294, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.326, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:44.326, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:44.326, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.326, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.356, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:44.356, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:44.357, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.357, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.387, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:44.387, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:44.387, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.387, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.418, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:44.418, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:44.419, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.419, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.449, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:44.449, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:44.449, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.449, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.480, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:44.480, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:44.480, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.480, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.510, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:44.510, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:44.511, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.511, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.542, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:44.542, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:44.543, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.543, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.573, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:44.573, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:44.574, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.574, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.604, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:44.604, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:44.604, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.604, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.636, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:44.636, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:44.636, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.636, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.667, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:44.667, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:44.668, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.668, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.699, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:44.699, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:44.699, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.699, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.730, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:44.730, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:44.730, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.730, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.760, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:44.760, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:44.760, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.760, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.791, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:44.791, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:44.792, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.792, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.823, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:44.823, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:44.824, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.824, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.854, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:44.854, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:44.854, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.854, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.886, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:44.886, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:44.886, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.886, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.917, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:44.917, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:44.917, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.917, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.948, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:44.948, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:44.948, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.948, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:44.979, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:44.979, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:44.979, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:44.979, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.011, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:45.011, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:45.012, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.012, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.043, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:45.043, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:45.043, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.043, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.073, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:45.073, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:45.074, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.074, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.104, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:45.104, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:45.104, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.104, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.136, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:45.136, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:45.136, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.136, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.167, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:45.167, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:45.167, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.167, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.198, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:45.198, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:45.198, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.198, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.229, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:45.229, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:45.230, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.230, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.259, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:37:45.291, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=05/28 11:37:45.291, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/28 11:37:45.291, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.291, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.323, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:45.323, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:45.323, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.323, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.354, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:45.354, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:45.354, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.354, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.384, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:45.384, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:45.384, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.384, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.415, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:45.415, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:45.416, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.416, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.446, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:45.446, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:45.447, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.447, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.478, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:45.478, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:45.479, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.479, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.509, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:45.509, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:45.509, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.509, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.541, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:45.541, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:45.542, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.542, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.573, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:45.573, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:45.574, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.574, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.605, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:45.605, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:45.605, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.605, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.637, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:45.637, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:45.638, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.638, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.668, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:45.668, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:45.668, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.668, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.699, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:45.699, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:45.700, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.700, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.729, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:37:45.760, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:45.760, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/28 11:37:45.760, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.760, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.792, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:45.792, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:45.793, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.793, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.824, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:45.824, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:45.824, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.824, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.854, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:45.854, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:45.854, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.854, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.885, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:45.885, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:45.885, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.885, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.916, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:45.916, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:45.917, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.917, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.947, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:45.947, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:45.948, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.948, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:45.978, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:45.978, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:45.979, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:45.979, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.009, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:46.009, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:46.009, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.009, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.041, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=05/28 11:37:46.041, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=05/28 11:37:46.042, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.042, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.073, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:46.073, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:46.074, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.074, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.104, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:46.104, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:46.104, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.104, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.134, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:46.134, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:46.134, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.134, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.166, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:46.166, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:46.167, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.167, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.197, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:46.197, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:46.197, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.198, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.228, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:46.228, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:46.229, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.229, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.260, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:46.260, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:46.260, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.260, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.291, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:46.291, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:46.291, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.291, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.322, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:46.322, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:46.323, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.323, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.354, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:46.354, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:46.354, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.354, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.385, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:46.385, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:46.386, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.386, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.417, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:46.417, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:46.417, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.417, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.448, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:46.448, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:46.448, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.448, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.478, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:46.478, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:46.479, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.479, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.509, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:46.509, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:46.509, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.509, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.540, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:46.540, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:46.541, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.541, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.572, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:46.572, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:46.573, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.573, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.604, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:46.604, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:46.604, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.604, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.635, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:46.635, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:46.635, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.635, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.666, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:46.666, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:46.667, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.667, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.698, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:46.698, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:46.698, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.698, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.729, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:46.729, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:46.729, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.729, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.759, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:37:46.791, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=05/28 11:37:46.791, level=WARN, class = BtromHandshake, timeout = 62, message = response timeout
time=05/28 11:37:46.792, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.792, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.823, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:46.823, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:46.823, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.823, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.854, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:46.854, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:46.855, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.855, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.886, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:46.886, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:46.886, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.886, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.918, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:46.918, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:46.918, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.918, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.949, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:46.949, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:46.949, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.949, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:46.980, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:46.980, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:46.980, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:46.980, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.010, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:47.010, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:47.011, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.011, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.041, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:47.041, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:47.042, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.042, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.057, level=DEBUG, [COM4] Read(16)
time=05/28 11:37:47.057, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 44.38 B/s
time=05/28 11:37:47.057, level=INFO, [RxP 16] 24504149523338322C312A32450D0A24
time=05/28 11:37:47.072, level=DEBUG, [COM4] Read(32)
time=05/28 11:37:47.072, level=INFO, [RxP 32] 504149523130302C312C302A33410D0A24504149523036322C312C302A33460D
time=05/28 11:37:47.073, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:47.073, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.073, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.103, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:37:47.103, level=DEBUG, [COM4] Read(30)
time=05/28 11:37:47.103, level=INFO, [RxP 30] 0A24504149523036322C352C302A33420D0A24504149523036322C332C30
time=05/28 11:37:47.103, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:47.103, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.103, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.118, level=DEBUG, [COM4] Read(24)
time=05/28 11:37:47.118, level=INFO, [RxP 24] 2A33440D0A24504149523038302C302A32450D0A24504149
time=05/28 11:37:47.134, level=DEBUG, [COM4] Read(30)
time=05/28 11:37:47.134, level=INFO, [RxP 30] 523036362C312C312C312C312C312C302A33420D0A24504149523038312A
time=05/28 11:37:47.134, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:47.134, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.134, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.149, level=DEBUG, [COM4] Read(19)
time=05/28 11:37:47.149, level=INFO, [RxP 19] 33330D0A24504149523439302C312A32410D0A
time=05/28 11:37:47.180, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:47.180, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=05/28 11:37:47.181, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.181, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.211, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:47.211, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:47.212, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.212, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.243, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:47.243, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:47.244, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.244, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.274, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:47.274, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:47.274, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.274, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.305, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:47.305, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:47.305, level=DEBUG, [COM4] Read(17)
time=05/28 11:37:47.305, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 543.86 B/s
time=05/28 11:37:47.305, level=INFO, [RxP 17] 24504149523036322C332C312A33430D0A
time=05/28 11:37:47.306, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.306, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.336, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:47.336, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:47.353, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.353, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.384, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:47.384, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:47.384, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.384, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.415, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:47.415, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:47.416, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.416, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.447, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:47.447, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:47.448, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.448, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.478, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:47.478, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:47.478, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.478, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.509, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:47.509, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:47.509, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.509, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.509, level=DEBUG, [COM4] Read(18)
time=05/28 11:37:47.509, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=05/28 11:37:47.509, level=INFO, [RxP 18] 24504149523030322A33380D0A2450414952
time=05/28 11:37:47.524, level=DEBUG, [COM4] Read(8)
time=05/28 11:37:47.524, level=INFO, [RxP 8] 3439312A33360D0A
time=05/28 11:37:47.556, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:47.556, level=WARN, class = BtromHandshake, timeout = 47, message = response timeout
time=05/28 11:37:47.557, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.557, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.588, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:47.588, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:47.588, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.588, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.619, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:47.619, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:47.620, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.620, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.649, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:37:47.680, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:47.680, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/28 11:37:47.681, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.681, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.711, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:47.711, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:47.712, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.712, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.727, level=DEBUG, [COM4] Read(26)
time=05/28 11:37:47.727, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 36.70 B/s
time=05/28 11:37:47.728, level=INFO, [RxP 26] 24504149523030322A33380D0A24504149523439312A33360D0A
time=05/28 11:37:47.759, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:47.759, level=WARN, class = BtromHandshake, timeout = 47, message = response timeout
time=05/28 11:37:47.759, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.759, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.790, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:47.790, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:47.791, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.791, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.822, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:47.822, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:47.822, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.822, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.854, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:47.854, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:47.854, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.854, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.884, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:37:47.915, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:47.915, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/28 11:37:47.915, level=DEBUG, [COM4] Read(6)
time=05/28 11:37:47.915, level=INFO, [RxP 6] 245041495230
time=05/28 11:37:47.916, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.916, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.931, level=DEBUG, [COM4] Read(20)
time=05/28 11:37:47.931, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 29.48 B/s
time=05/28 11:37:47.931, level=INFO, [RxP 20] 30322A33380D0A24504149523439312A33360D0A
time=05/28 11:37:47.962, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:47.962, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=05/28 11:37:47.963, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.963, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:47.993, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:47.993, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:47.993, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:47.993, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.024, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:48.024, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:48.024, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.024, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.056, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=05/28 11:37:48.056, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=05/28 11:37:48.057, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.057, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.087, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:48.087, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:48.088, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.088, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.118, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:48.118, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:48.118, level=DEBUG, [COM4] Read(19)
time=05/28 11:37:48.118, level=INFO, [RxP 19] 24504149523030322A33380D0A245041495234
time=05/28 11:37:48.119, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.119, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.134, level=DEBUG, [COM4] Read(7)
time=05/28 11:37:48.134, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 93.65 B/s
time=05/28 11:37:48.134, level=INFO, [RxP 7] 39312A33360D0A
time=05/28 11:37:48.165, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:48.165, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=05/28 11:37:48.165, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.165, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.195, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:48.195, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:48.196, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.196, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.226, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:48.226, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:48.227, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.227, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.258, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:48.259, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:48.259, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.259, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.289, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:37:48.304, level=DEBUG, [COM4] Read(2)
time=05/28 11:37:48.304, level=INFO, [RxP 2] 2450
time=05/28 11:37:48.304, level=WARN, class = BtromHandshake, timeout = 45, message = response timeout
time=05/28 11:37:48.305, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.305, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.320, level=DEBUG, [COM4] Read(24)
time=05/28 11:37:48.320, level=INFO, [RxP 24] 4149523030322A33380D0A24504149523439312A33360D0A
time=05/28 11:37:48.352, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:48.352, level=WARN, class = BtromHandshake, timeout = 47, message = response timeout
time=05/28 11:37:48.353, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.353, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.383, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:48.383, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:48.383, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.383, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.414, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:48.414, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:48.414, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.414, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.445, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:48.445, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:48.462, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.462, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.493, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:48.493, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:48.493, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.493, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.524, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:48.524, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:48.524, level=DEBUG, [COM4] Read(14)
time=05/28 11:37:48.524, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 66.68 B/s
time=05/28 11:37:48.524, level=INFO, [RxP 14] 24504149523030322A33380D0A24
time=05/28 11:37:48.524, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.524, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.539, level=DEBUG, [COM4] Read(12)
time=05/28 11:37:48.539, level=INFO, [RxP 12] 504149523439312A33360D0A
time=05/28 11:37:48.570, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:48.570, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=05/28 11:37:48.571, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.571, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.601, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:48.601, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:48.602, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.602, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.633, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:48.633, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:48.633, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.633, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.664, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:48.664, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:48.664, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.664, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.695, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:48.695, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:48.696, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.696, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.727, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:48.727, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:48.727, level=DEBUG, [COM4] Read(26)
time=05/28 11:37:48.727, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 58.92 B/s
time=05/28 11:37:48.727, level=INFO, [RxP 26] 24504149523030322A33380D0A24504149523439312A33360D0A
time=05/28 11:37:48.728, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.728, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.759, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:48.759, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:48.760, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.760, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.790, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:48.790, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:48.791, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.791, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.821, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:48.821, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:48.821, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.821, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.852, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:48.852, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:48.853, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.853, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.884, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:48.884, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:48.884, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.884, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.914, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:37:48.914, level=DEBUG, [COM4] Read(8)
time=05/28 11:37:48.914, level=INFO, [RxP 8] 2450414952303032
time=05/28 11:37:48.930, level=DEBUG, [COM4] Read(18)
time=05/28 11:37:48.930, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 39.43 B/s
time=05/28 11:37:48.930, level=INFO, [RxP 18] 2A33380D0A24504149523439312A33360D0A
time=05/28 11:37:48.930, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=05/28 11:37:48.931, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.931, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.962, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:48.962, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:48.963, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:48.963, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:48.992, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:37:49.024, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:49.024, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/28 11:37:49.024, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.024, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.054, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:49.054, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:49.055, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.055, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.086, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:49.086, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:49.086, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.086, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.117, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:49.117, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:49.117, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.117, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.132, level=DEBUG, [COM4] Read(20)
time=05/28 11:37:49.132, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=05/28 11:37:49.132, level=INFO, [RxP 20] 24504149523030322A33380D0A24504149523439
time=05/28 11:37:49.148, level=DEBUG, [COM4] Read(6)
time=05/28 11:37:49.148, level=INFO, [RxP 6] 312A33360D0A
time=05/28 11:37:49.148, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:49.148, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.148, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.179, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:49.179, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:49.179, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.179, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.210, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:49.210, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:49.210, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.210, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.242, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=05/28 11:37:49.242, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=05/28 11:37:49.243, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.243, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.274, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:49.274, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:49.274, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.274, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.305, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:49.305, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:49.306, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.306, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.321, level=DEBUG, [COM4] Read(2)
time=05/28 11:37:49.321, level=INFO, [RxP 2] 2450
time=05/28 11:37:49.337, level=DEBUG, [COM4] Read(24)
time=05/28 11:37:49.337, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 39.14 B/s
time=05/28 11:37:49.337, level=INFO, [RxP 24] 4149523030322A33380D0A24504149523439312A33360D0A
time=05/28 11:37:49.337, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:49.337, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.337, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.368, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:49.368, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:49.368, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.368, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.400, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=05/28 11:37:49.400, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=05/28 11:37:49.400, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.400, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.431, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:49.431, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:49.432, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.432, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.462, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:49.462, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:49.463, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.463, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.494, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:49.494, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:49.494, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.494, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.524, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:49.524, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:49.524, level=DEBUG, [COM4] Read(16)
time=05/28 11:37:49.524, level=INFO, [RxP 16] 24504149523030322A33380D0A245041
time=05/28 11:37:49.524, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.524, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.539, level=DEBUG, [COM4] Read(10)
time=05/28 11:37:49.539, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 79.04 B/s
time=05/28 11:37:49.539, level=INFO, [RxP 10] 49523439312A33360D0A
time=05/28 11:37:49.569, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:49.569, level=WARN, class = BtromHandshake, timeout = 45, message = response timeout
time=05/28 11:37:49.570, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.570, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.601, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:49.601, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:49.602, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.602, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.631, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:37:49.663, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:49.663, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/28 11:37:49.664, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.664, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.694, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:49.694, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:49.694, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.694, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.726, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:49.726, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:49.726, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.726, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.741, level=DEBUG, [COM4] Read(26)
time=05/28 11:37:49.741, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=05/28 11:37:49.742, level=INFO, [RxP 26] 24504149523030322A33380D0A24504149523439312A33360D0A
time=05/28 11:37:49.773, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:49.773, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=05/28 11:37:49.773, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.773, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.804, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:49.804, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:49.805, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.805, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.835, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:49.835, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:49.835, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.835, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.866, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:49.866, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:49.867, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.867, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.897, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:49.897, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:49.898, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.898, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.929, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:49.929, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:49.929, level=DEBUG, [COM4] Read(10)
time=05/28 11:37:49.929, level=INFO, [RxP 10] 24504149523030322A33
time=05/28 11:37:49.929, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.929, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:49.944, level=DEBUG, [COM4] Read(16)
time=05/28 11:37:49.944, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 49.33 B/s
time=05/28 11:37:49.944, level=INFO, [RxP 16] 380D0A24504149523439312A33360D0A
time=05/28 11:37:49.976, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:49.976, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=05/28 11:37:49.976, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:49.976, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.007, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:50.007, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:50.007, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.007, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.039, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:50.039, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:50.039, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.039, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.069, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:50.069, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:50.069, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.069, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.101, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:50.101, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:50.102, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.102, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.132, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:50.132, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:50.132, level=DEBUG, [COM4] Read(22)
time=05/28 11:37:50.132, level=INFO, [RxP 22] 24504149523030322A33380D0A24504149523439312A
time=05/28 11:37:50.133, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.133, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.148, level=DEBUG, [COM4] Read(4)
time=05/28 11:37:50.148, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 108.18 B/s
time=05/28 11:37:50.148, level=INFO, [RxP 4] 33360D0A
time=05/28 11:37:50.178, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:50.178, level=WARN, class = BtromHandshake, timeout = 45, message = response timeout
time=05/28 11:37:50.179, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.179, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.209, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:50.209, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:50.209, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.209, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.241, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:50.241, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:50.242, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.242, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.273, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:50.273, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:50.274, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.274, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.304, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:50.304, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:50.304, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.304, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.320, level=DEBUG, [COM4] Read(5)
time=05/28 11:37:50.320, level=INFO, [RxP 5] 2450414952
time=05/28 11:37:50.336, level=DEBUG, [COM4] Read(21)
time=05/28 11:37:50.336, level=INFO, [RxP 21] 3030322A33380D0A24504149523439312A33360D0A
time=05/28 11:37:50.336, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:50.337, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.337, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.366, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:37:50.396, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:50.396, level=WARN, class = BtromHandshake, timeout = 59, message = response timeout
time=05/28 11:37:50.397, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.397, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.428, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:50.428, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:50.428, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.428, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.459, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:50.459, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:50.459, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.459, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.490, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:50.490, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:50.491, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.491, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.522, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:50.522, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:50.522, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.522, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.554, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:50.554, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:50.554, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.554, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.584, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:37:50.615, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:50.615, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/28 11:37:50.615, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.615, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.646, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:50.646, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:50.646, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.647, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.678, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:50.678, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:50.678, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.678, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.709, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:50.709, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:50.709, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.709, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.740, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:50.740, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:50.740, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.740, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.771, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:50.771, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:50.772, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.772, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.802, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:50.802, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:50.803, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.803, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.834, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:50.834, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:50.834, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.834, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.865, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:50.865, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:50.866, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.866, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.897, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:50.897, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:50.897, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.897, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.928, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:50.928, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:50.928, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.928, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.959, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:50.959, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:50.959, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.959, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:50.990, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:50.990, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:50.990, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:50.990, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.021, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:51.021, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:51.022, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.022, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.052, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:51.052, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:51.053, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.053, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.085, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:51.085, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:51.085, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.085, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.115, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:51.115, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:51.116, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.116, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.147, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:51.147, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:51.147, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.147, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.178, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:51.178, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:51.178, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.178, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.209, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:51.209, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:51.209, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.209, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.241, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:51.241, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:51.241, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.241, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.271, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:51.271, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:51.272, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.272, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.302, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:51.302, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:51.303, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.303, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.333, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:51.333, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:51.334, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.334, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.364, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:51.364, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:51.364, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.364, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.395, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:51.395, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:51.396, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.396, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.427, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:51.427, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:51.427, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.427, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.458, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:51.458, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:51.458, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.458, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.489, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:51.489, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:51.489, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.489, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.521, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:51.521, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:51.522, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.522, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.552, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:51.552, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:51.553, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.553, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.584, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:51.584, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:51.584, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.584, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.615, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:51.615, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:51.616, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.616, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.647, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:51.647, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:51.648, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.648, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.678, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:51.678, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:51.678, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.678, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.709, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:51.709, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:51.725, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.725, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.757, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:51.757, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:51.757, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.757, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.788, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:51.788, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:51.788, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.788, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.819, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:51.819, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:51.819, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.820, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.849, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:37:51.879, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:51.879, level=WARN, class = BtromHandshake, timeout = 59, message = response timeout
time=05/28 11:37:51.880, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.880, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.910, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:51.910, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:51.911, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.911, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.941, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:51.941, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:51.942, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.942, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:51.973, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:51.973, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:51.973, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:51.973, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.004, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:52.004, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:52.004, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.004, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.035, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:52.035, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:52.035, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.035, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.066, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:52.066, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:52.066, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.066, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.097, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:52.097, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:52.098, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.098, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.128, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:52.128, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:52.128, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.128, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.159, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:52.159, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:52.159, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.159, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.190, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:52.190, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:52.191, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.191, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.221, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:52.221, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:52.222, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.222, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.253, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:52.253, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:52.253, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.253, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.283, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:37:52.315, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:52.315, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/28 11:37:52.316, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.316, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.346, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:52.346, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:52.347, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.347, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.378, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:52.378, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:52.378, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.378, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.409, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:52.409, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:52.409, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.409, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.440, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:52.440, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:52.440, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.440, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.470, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:52.470, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:52.471, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.471, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.502, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:52.502, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:52.503, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.503, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.533, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:52.533, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:52.534, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.534, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.564, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:52.564, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:52.564, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.564, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.595, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:52.595, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:52.596, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.596, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.627, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:52.627, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:52.628, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.628, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.659, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:52.659, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:52.659, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.659, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.690, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:52.690, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:52.690, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.690, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.722, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:52.722, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:52.722, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.722, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.754, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:52.754, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:52.754, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.754, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.785, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:52.785, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:52.785, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.785, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.818, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=05/28 11:37:52.818, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=05/28 11:37:52.818, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.818, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.848, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:52.848, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:52.848, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.848, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.879, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:52.879, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:52.879, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.879, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.910, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:37:52.910, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:37:52.911, level=DEBUG, [COM4] Write(4)
time=05/28 11:37:52.911, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:37:52.941, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:52.941, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:37:52.941, level=ERROR, class = BtromHandshake, result = fail, timeout = 10003, error_message = btrom handshake timeout
time=05/28 11:37:52.941, level=ERROR, D:\project\crossover\src\task\flash\download_da_uart.cc:141(-1): class = DownloadDa_UART, error_message = btrom handshake fail
time=05/28 11:37:52.941, level=ERROR, class = Task, task_name = DownloadDa_UART, error_message = Fail to execute RunTask()
time=05/28 11:37:52.941, level=DEBUG, class = DownloadDa_UART, task_time = 10.007
time=05/28 11:37:52.942, level=DEBUG, class = Controller, RemoveObserver = callback
time=05/28 11:37:52.942, level=DEBUG, class = CallbackManager, deregister = callback
time=05/28 11:37:52.942, level=DEBUG, class = DisconnectDUT, state = init data, result = ok
time=05/28 11:37:53.050, level=DEBUG, class = SerialHost, state = close
time=05/28 11:37:53.050, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=05/28 11:37:53.050, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=05/28 11:37:53.050, level=DEBUG, class = UartDev, state = disconnect
time=05/28 11:37:53.050, level=DEBUG, class = DisconnectDUT, state = disconnect, device_name = DUT, device_type = UART
time=05/28 11:37:53.050, level=DEBUG, class = DisconnectDUT, task_time = 0.109
time=05/28 11:37:53.050, level=DEBUG, class = LogService, state = CloseLogFile, pass_fail = 1, postfix = 
