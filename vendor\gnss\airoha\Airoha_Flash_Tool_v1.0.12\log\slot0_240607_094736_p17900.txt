time=06/07 09:47:36.350, level=INFO, class = LogService, tool_version = 0.2.12/air_bta_ic_premium_g3
time=06/07 09:47:36.347, level=INFO, class = Controller, SetLogLevel = 3
time=06/07 09:47:36.347, level=DEBUG, class = ToolLogLevel, log_level = 3
time=06/07 09:47:36.347, level=DEBUG, class = ToolLogLevel, task_time = 0.000
time=06/07 09:47:36.347, level=DEBUG, class = ConnectDUT, state = init data, result = ok
time=06/07 09:47:36.347, level=DEBUG, class = SerialHost, phy_name = UART, trans_name = h4
time=06/07 09:47:36.347, level=DEBUG, class = Physical, type = 1, state = create
time=06/07 09:47:36.347, level=DEBUG, class = Transport, type = 4, state = create
time=06/07 09:47:36.348, level=INFO, class = UartPhy, desc = USB Serial Port (COM4)
time=06/07 09:47:36.348, level=INFO, class = UartPhy, instance_id = FTDIBUS\VID_0403+PID_6015+DQ014GZWA\0000
time=06/07 09:47:36.348, level=INFO, class = UartPhy, port = 4, baud = 3000000, handshake = 0, read/timeout = (3, 0)
time=06/07 09:47:36.388, level=DEBUG, class = SerialHost, state = open
time=06/07 09:47:36.388, level=DEBUG, class = UartDev, state = connect, ret = 0
time=06/07 09:47:36.388, level=DEBUG, class = ConnectDUT, state = connect, device_name = DUT, device_type = UART, result = ok
time=06/07 09:47:36.388, level=DEBUG, class = ConnectDUT, task_time = 0.041
time=06/07 09:47:36.388, level=DEBUG, class = CallbackManager, register = DUT, filter = 0, cb = 0x00EE2C00
time=06/07 09:47:36.388, level=DEBUG, class = UartDev, SetBaudrate = 115200
time=06/07 09:47:36.389, level=DEBUG, class = Host, SwitchTransport = bypass
time=06/07 09:47:36.389, level=DEBUG, class = Transport, type = 1, state = create
time=06/07 09:47:36.389, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=06/07 09:47:36.390, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:36.390, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:36.409, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=06/07 09:47:36.427, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:47:36.427, level=WARN, class = BtromHandshake, timeout = 36, message = response timeout
time=06/07 09:47:36.427, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:36.427, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:36.445, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=06/07 09:47:36.463, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:47:36.463, level=WARN, class = BtromHandshake, timeout = 36, message = response timeout
time=06/07 09:47:36.463, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:36.463, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:36.480, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:47:36.498, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=06/07 09:47:36.498, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=06/07 09:47:36.499, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:36.499, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:36.517, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=06/07 09:47:36.534, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:47:36.534, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=06/07 09:47:36.535, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:36.535, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:36.552, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:47:36.584, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=06/07 09:47:36.584, level=WARN, class = BtromHandshake, timeout = 49, message = response timeout
time=06/07 09:47:36.585, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:36.585, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:36.616, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:36.616, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:36.616, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:36.616, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:36.647, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:36.647, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:36.647, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:36.647, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:36.678, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:36.678, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:36.679, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:36.679, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:36.710, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:36.710, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:36.710, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:36.710, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:36.741, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:36.741, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:36.741, level=DEBUG, [COM4] Read(6)
time=06/07 09:47:36.741, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=06/07 09:47:36.741, level=INFO, [RxP 6] 245041495233
time=06/07 09:47:36.741, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:36.741, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:36.757, level=DEBUG, [COM4] Read(32)
time=06/07 09:47:36.757, level=INFO, [RxP 32] 38322C312A32450D0A24504149523130302C312C302A33410D0A245041495230
time=06/07 09:47:36.772, level=DEBUG, [COM4] Read(30)
time=06/07 09:47:36.772, level=INFO, [RxP 30] 36322C312C302A33460D0A24504149523036322C352C302A33420D0A2450
time=06/07 09:47:36.772, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:36.772, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:36.772, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:36.788, level=DEBUG, [COM4] Read(24)
time=06/07 09:47:36.788, level=INFO, [RxP 24] 4149523036322C332C302A33440D0A24504149523038302C
time=06/07 09:47:36.805, level=DEBUG, [COM4] Read(30)
time=06/07 09:47:36.805, level=INFO, [RxP 30] 302A32450D0A24504149523036362C312C312C312C312C312C302A33420D
time=06/07 09:47:36.805, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=06/07 09:47:36.805, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:36.805, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:36.823, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=06/07 09:47:36.823, level=DEBUG, [COM4] Read(29)
time=06/07 09:47:36.823, level=INFO, [RxP 29] 0A24504149523038312A33330D0A24504149523439302C312A32410D0A
time=06/07 09:47:36.855, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:36.855, level=WARN, class = BtromHandshake, timeout = 49, message = response timeout
time=06/07 09:47:36.855, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:36.855, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:36.886, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:36.886, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:36.886, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:36.886, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:36.917, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:36.917, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:36.917, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:36.917, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:36.948, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:36.948, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:36.948, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:36.948, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:36.979, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:36.979, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:36.980, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:36.980, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.011, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:37.011, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:37.011, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.011, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.042, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:37.042, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:37.042, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.042, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.073, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:37.073, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:37.073, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.073, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.104, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:37.104, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:37.105, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.105, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.136, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:37.136, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:37.136, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.136, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.167, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:37.167, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:37.167, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.167, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.198, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:37.198, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:37.198, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.198, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.229, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:37.229, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:37.230, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.230, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.261, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:37.261, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:37.261, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.261, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.292, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:37.292, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:37.292, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.292, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.323, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:37.323, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:37.323, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.323, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.356, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=06/07 09:47:37.356, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=06/07 09:47:37.356, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.356, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.389, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=06/07 09:47:37.389, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=06/07 09:47:37.389, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.389, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.420, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:37.420, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:37.420, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.420, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.451, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:37.451, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:37.451, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.451, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.482, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:37.482, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:37.482, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.482, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.514, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:37.514, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:37.514, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.514, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.545, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:37.545, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:37.545, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.545, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.576, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:37.576, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:37.576, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.576, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.607, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:37.607, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:37.608, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.608, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.639, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:37.639, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:37.639, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.639, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.670, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:37.670, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:37.671, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.671, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.699, level=WARN, class = Host, msg = Match timeout 28 ms(0)
time=06/07 09:47:37.730, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:37.730, level=WARN, class = BtromHandshake, timeout = 59, message = response timeout
time=06/07 09:47:37.731, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.731, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.762, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:37.762, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:37.762, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.762, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.793, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:37.793, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:37.793, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.793, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.824, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:37.824, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:37.824, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.824, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.855, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:37.855, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:37.856, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.856, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.891, level=WARN, class = Host, msg = Match timeout 35 ms(0)
time=06/07 09:47:37.891, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=06/07 09:47:37.891, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.891, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.922, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:37.922, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:37.922, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.922, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.953, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:37.953, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:37.954, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.954, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:37.985, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:37.985, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:37.985, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:37.985, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.016, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:38.016, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:38.016, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.016, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.047, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:38.047, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:38.047, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.047, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.078, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:38.078, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:38.079, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.079, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.110, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:38.110, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:38.110, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.110, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.141, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:38.141, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:38.141, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.141, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.172, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:38.172, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:38.172, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.172, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.203, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:38.203, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:38.203, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.203, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.234, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:38.234, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:38.235, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.235, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.266, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:38.266, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:38.266, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.266, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.297, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:38.297, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:38.297, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.297, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.328, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:38.328, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:38.328, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.328, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.359, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:38.359, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:38.360, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.360, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.391, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:38.391, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:38.391, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.391, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.422, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:38.422, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:38.423, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.423, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.441, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=06/07 09:47:38.472, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:38.472, level=WARN, class = BtromHandshake, timeout = 49, message = response timeout
time=06/07 09:47:38.473, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.473, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.503, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:38.503, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:38.504, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.504, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.535, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:38.535, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:38.535, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.535, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.566, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:38.566, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:38.566, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.566, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.597, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:38.597, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:38.597, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.597, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.628, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:38.628, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:38.629, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.629, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.659, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:38.659, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:38.660, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.660, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.691, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:38.691, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:38.691, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.691, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.721, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:38.721, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:38.722, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.722, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.753, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:38.753, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:38.753, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.753, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.784, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:38.784, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:38.785, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.785, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.816, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:38.816, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:38.816, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.816, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.847, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:38.847, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:38.847, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.847, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.878, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:38.878, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:38.878, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.878, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.908, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:38.908, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:38.909, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.909, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.939, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:38.939, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:38.940, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.940, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:38.971, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:38.971, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:38.972, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:38.972, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.002, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:39.002, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:39.002, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.002, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.033, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:39.033, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:39.033, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.033, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.051, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:47:39.082, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:39.082, level=WARN, class = BtromHandshake, timeout = 48, message = response timeout
time=06/07 09:47:39.082, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.082, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.113, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:39.113, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:39.114, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.114, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.144, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:39.144, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:39.145, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.145, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.176, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:39.176, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:39.176, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.176, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.200, level=WARN, class = Host, msg = Match timeout 24 ms(0)
time=06/07 09:47:39.234, level=WARN, class = Host, msg = Match timeout 33 ms(0)
time=06/07 09:47:39.234, level=WARN, class = BtromHandshake, timeout = 57, message = response timeout
time=06/07 09:47:39.234, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.234, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.265, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:39.265, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:39.265, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.265, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.296, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:39.296, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:39.297, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.297, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.328, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:39.328, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:39.328, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.328, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.359, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:39.359, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:39.359, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.359, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.390, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:39.390, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:39.391, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.391, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.422, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:39.422, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:39.422, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.422, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.453, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:39.453, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:39.453, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.453, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.492, level=WARN, class = Host, msg = Match timeout 39 ms(0)
time=06/07 09:47:39.492, level=WARN, class = BtromHandshake, timeout = 39, message = response timeout
time=06/07 09:47:39.493, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.493, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.524, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:39.524, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:39.524, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.524, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.555, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:39.555, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:39.555, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.555, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.586, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:39.586, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:39.586, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.586, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.617, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:39.617, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:39.618, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.618, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.649, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:39.649, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:39.649, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.649, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.680, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:39.680, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:39.680, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.680, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.711, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:39.711, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:39.711, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.711, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.742, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:39.742, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:39.743, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.743, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.773, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:39.773, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:39.774, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.774, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.805, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:39.805, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:39.805, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.805, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.836, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:39.836, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:39.836, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.836, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.867, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:39.867, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:39.868, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.868, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.899, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:39.899, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:39.899, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.899, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.930, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:39.930, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:39.930, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.930, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.961, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:39.961, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:39.961, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.961, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:39.993, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:39.993, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:39.993, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:39.993, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.026, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=06/07 09:47:40.026, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=06/07 09:47:40.026, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.026, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.057, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:40.057, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:40.057, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.057, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.073, level=DEBUG, [COM4] Read(32)
time=06/07 09:47:40.073, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 43.52 B/s
time=06/07 09:47:40.073, level=INFO, [RxP 32] 24504149523338322C312A32450D0A24504149523130302C312C302A33410D0A
time=06/07 09:47:40.088, level=DEBUG, [COM4] Read(30)
time=06/07 09:47:40.088, level=INFO, [RxP 30] 24504149523036322C312C302A33460D0A24504149523036322C352C302A
time=06/07 09:47:40.089, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:40.089, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.089, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.104, level=DEBUG, [COM4] Read(24)
time=06/07 09:47:40.104, level=INFO, [RxP 24] 33420D0A24504149523036322C332C302A33440D0A245041
time=06/07 09:47:40.120, level=DEBUG, [COM4] Read(30)
time=06/07 09:47:40.120, level=INFO, [RxP 30] 49523038302C302A32450D0A24504149523036362C312C312C312C312C31
time=06/07 09:47:40.120, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:40.120, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.120, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.135, level=DEBUG, [COM4] Read(32)
time=06/07 09:47:40.135, level=INFO, [RxP 32] 2C302A33420D0A24504149523038312A33330D0A24504149523439302C312A32
time=06/07 09:47:40.151, level=DEBUG, [COM4] Read(3)
time=06/07 09:47:40.151, level=INFO, [RxP 3] 410D0A
time=06/07 09:47:40.151, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:40.151, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.151, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.182, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:40.182, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:40.182, level=DEBUG, [COM4] Read(2)
time=06/07 09:47:40.182, level=INFO, [RxP 2] 2450
time=06/07 09:47:40.183, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.183, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.198, level=DEBUG, [COM4] Read(15)
time=06/07 09:47:40.198, level=INFO, [RxP 15] 4149523036322C332C312A33430D0A
time=06/07 09:47:40.229, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:40.229, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:40.229, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.229, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.260, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:40.260, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:40.260, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.260, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.291, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:40.291, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:40.292, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.292, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.323, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:40.323, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:40.323, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.323, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.354, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:40.354, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:40.354, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.354, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.385, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:40.385, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:40.385, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.385, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.401, level=DEBUG, [COM4] Read(25)
time=06/07 09:47:40.401, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 414.33 B/s
time=06/07 09:47:40.401, level=INFO, [RxP 25] 24504149523030322A33380D0A24504149523439312A33360D
time=06/07 09:47:40.416, level=DEBUG, [COM4] Read(1)
time=06/07 09:47:40.416, level=INFO, [RxP 1] 0A
time=06/07 09:47:40.416, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:40.417, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.417, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.448, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:40.448, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:40.448, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.448, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.479, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:40.479, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:40.479, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.479, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.510, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:40.510, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:40.510, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.510, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.541, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:40.541, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:40.541, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.541, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.561, level=WARN, class = Host, msg = Match timeout 19 ms(0)
time=06/07 09:47:40.592, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:40.592, level=WARN, class = BtromHandshake, timeout = 50, message = response timeout
time=06/07 09:47:40.592, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.592, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.608, level=DEBUG, [COM4] Read(2)
time=06/07 09:47:40.608, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 4.84 B/s
time=06/07 09:47:40.608, level=INFO, [RxP 2] 2450
time=06/07 09:47:40.623, level=DEBUG, [COM4] Read(24)
time=06/07 09:47:40.623, level=INFO, [RxP 24] 4149523030322A33380D0A24504149523439312A33360D0A
time=06/07 09:47:40.623, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:40.623, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.623, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.654, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:40.654, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:40.655, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.655, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.685, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:40.685, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:40.686, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.686, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.717, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:40.717, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:40.717, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.717, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.748, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:40.748, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:40.748, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.748, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.779, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:40.779, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:40.780, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.780, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.811, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:40.811, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:40.811, level=DEBUG, [COM4] Read(10)
time=06/07 09:47:40.811, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 118.21 B/s
time=06/07 09:47:40.811, level=INFO, [RxP 10] 24504149523030322A33
time=06/07 09:47:40.811, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.811, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.826, level=DEBUG, [COM4] Read(16)
time=06/07 09:47:40.826, level=INFO, [RxP 16] 380D0A24504149523439312A33360D0A
time=06/07 09:47:40.857, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:40.857, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:40.858, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.858, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.889, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:40.889, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:40.889, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.889, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.920, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:40.920, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:40.920, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.920, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.951, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:40.951, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:40.951, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.951, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:40.982, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:40.982, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:40.983, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:40.983, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.014, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:41.014, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:41.014, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.014, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.014, level=DEBUG, [COM4] Read(22)
time=06/07 09:47:41.014, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 78.75 B/s
time=06/07 09:47:41.014, level=INFO, [RxP 22] 24504149523030322A33380D0A24504149523439312A
time=06/07 09:47:41.029, level=DEBUG, [COM4] Read(4)
time=06/07 09:47:41.029, level=INFO, [RxP 4] 33360D0A
time=06/07 09:47:41.061, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:41.061, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:41.061, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.061, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.093, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=06/07 09:47:41.093, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=06/07 09:47:41.093, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.093, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.125, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:41.125, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:41.125, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.125, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.156, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:41.156, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:41.156, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.156, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.187, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:41.187, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:41.187, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.187, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.203, level=DEBUG, [COM4] Read(6)
time=06/07 09:47:41.203, level=INFO, [RxP 6] 245041495230
time=06/07 09:47:41.234, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:41.234, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:41.234, level=DEBUG, [COM4] Read(20)
time=06/07 09:47:41.234, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 45.44 B/s
time=06/07 09:47:41.234, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.234, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.234, level=INFO, [RxP 20] 30322A33380D0A24504149523439312A33360D0A
time=06/07 09:47:41.265, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:41.265, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:41.265, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.265, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.296, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:41.296, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:41.297, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.297, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.328, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:41.328, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:41.328, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.328, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.359, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:41.359, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:41.375, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.375, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.406, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:41.406, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:41.406, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.406, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.421, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:41.421, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:41.453, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:41.453, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:41.453, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.453, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.484, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:41.484, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:41.484, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.484, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.515, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:41.515, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:41.515, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.515, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.546, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:41.546, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:41.547, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.547, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.578, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:41.578, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:41.578, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.578, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.612, level=WARN, class = Host, msg = Match timeout 34 ms(0)
time=06/07 09:47:41.612, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=06/07 09:47:41.612, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.612, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.628, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:41.628, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 33.00 B/s
time=06/07 09:47:41.628, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:41.659, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:41.659, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:41.659, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.659, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.690, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:41.690, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:41.690, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.690, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.721, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:41.721, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:41.722, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.722, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.753, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:41.753, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:41.753, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.753, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.784, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:41.784, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:41.784, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.784, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.815, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:41.815, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:41.815, level=DEBUG, [COM4] Read(12)
time=06/07 09:47:41.815, level=INFO, [RxP 12] 24504149523439312A33360D
time=06/07 09:47:41.816, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.816, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.831, level=DEBUG, [COM4] Read(1)
time=06/07 09:47:41.831, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 59.09 B/s
time=06/07 09:47:41.831, level=INFO, [RxP 1] 0A
time=06/07 09:47:41.862, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:41.862, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:41.862, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.862, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.893, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:41.893, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:41.893, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.893, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.925, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:41.925, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:41.925, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.925, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.956, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:41.956, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:41.956, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.956, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:41.987, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:41.987, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:41.987, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:41.987, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.018, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:42.018, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:42.018, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:42.018, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.018, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.018, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:42.049, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:42.049, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:42.050, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.050, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.081, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:42.081, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:42.081, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.081, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.112, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:42.112, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:42.112, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.112, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.145, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=06/07 09:47:42.145, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=06/07 09:47:42.145, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.145, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.177, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:42.177, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:42.177, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.177, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.208, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:42.208, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:42.208, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.208, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.239, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:42.239, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:42.239, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:42.239, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 31.84 B/s
time=06/07 09:47:42.239, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:42.239, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.239, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.270, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:42.270, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:42.270, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.270, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.302, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:42.302, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:42.302, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.302, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.333, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:42.333, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:42.333, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.333, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.364, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:42.364, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:42.364, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.364, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.395, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:42.395, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:42.396, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.396, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.427, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:42.427, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:42.427, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:42.427, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:42.427, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.427, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.458, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:42.458, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:42.458, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.458, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.489, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:42.489, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:42.489, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.489, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.520, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:42.520, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:42.520, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.520, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.552, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:42.552, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:42.552, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.552, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.583, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:42.583, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:42.583, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.583, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.614, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:42.614, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:42.614, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.614, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.614, level=DEBUG, [COM4] Read(2)
time=06/07 09:47:42.614, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 34.66 B/s
time=06/07 09:47:42.614, level=INFO, [RxP 2] 2450
time=06/07 09:47:42.630, level=DEBUG, [COM4] Read(11)
time=06/07 09:47:42.630, level=INFO, [RxP 11] 4149523439312A33360D0A
time=06/07 09:47:42.661, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:42.661, level=WARN, class = BtromHandshake, timeout = 47, message = response timeout
time=06/07 09:47:42.662, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.662, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.687, level=WARN, class = Host, msg = Match timeout 25 ms(0)
time=06/07 09:47:42.719, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:42.719, level=WARN, class = BtromHandshake, timeout = 57, message = response timeout
time=06/07 09:47:42.719, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.719, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.750, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:42.750, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:42.750, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.750, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.781, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:42.781, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:42.782, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.782, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.813, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:42.813, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:42.813, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.813, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.828, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:42.828, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 51.39 B/s
time=06/07 09:47:42.828, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:42.859, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:42.859, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:42.860, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.860, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.891, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:42.891, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:42.891, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.891, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.922, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:42.922, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:42.922, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.922, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.953, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:42.953, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:42.953, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.953, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:42.984, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:42.984, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:42.985, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:42.985, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.016, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:43.016, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:43.016, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.016, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.031, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:43.031, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=06/07 09:47:43.031, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:43.063, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:43.063, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:43.063, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.063, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.094, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:43.094, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:43.094, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.094, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.125, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:43.125, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:43.125, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.125, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.156, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:43.156, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:43.157, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.157, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.196, level=WARN, class = Host, msg = Match timeout 39 ms(0)
time=06/07 09:47:43.196, level=WARN, class = BtromHandshake, timeout = 39, message = response timeout
time=06/07 09:47:43.197, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.197, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.228, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:43.228, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:43.228, level=DEBUG, [COM4] Read(11)
time=06/07 09:47:43.228, level=INFO, [RxP 11] 24504149523439312A3336
time=06/07 09:47:43.228, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.228, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.243, level=DEBUG, [COM4] Read(2)
time=06/07 09:47:43.243, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 51.94 B/s
time=06/07 09:47:43.243, level=INFO, [RxP 2] 0D0A
time=06/07 09:47:43.274, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:43.274, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:43.275, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.275, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.306, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:43.306, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:43.306, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.306, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.337, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:43.337, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:43.337, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.337, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.368, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:43.368, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:43.368, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.368, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.399, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:43.399, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:43.400, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.400, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.430, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:43.430, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:43.431, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:43.431, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.431, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.431, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:43.462, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:43.462, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:43.462, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.462, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.493, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:43.493, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:43.493, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.493, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.524, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:43.524, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:43.525, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.525, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.556, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:43.556, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:43.556, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.556, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.587, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:43.587, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:43.587, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.587, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.618, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:43.618, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:43.618, level=DEBUG, [COM4] Read(6)
time=06/07 09:47:43.618, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 34.69 B/s
time=06/07 09:47:43.618, level=INFO, [RxP 6] 245041495234
time=06/07 09:47:43.618, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.618, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.634, level=DEBUG, [COM4] Read(7)
time=06/07 09:47:43.634, level=INFO, [RxP 7] 39312A33360D0A
time=06/07 09:47:43.665, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:43.665, level=WARN, class = BtromHandshake, timeout = 47, message = response timeout
time=06/07 09:47:43.666, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.666, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.697, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:43.697, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:43.698, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.698, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.730, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:43.730, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:43.730, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.730, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.761, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:43.761, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:43.761, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.761, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.792, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:43.792, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:43.792, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.792, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.824, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:43.824, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:43.824, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.824, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.840, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:43.840, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 31.59 B/s
time=06/07 09:47:43.840, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:43.870, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:43.870, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:43.870, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.870, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.901, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:43.901, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:43.901, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.901, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.931, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:43.931, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:43.932, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.932, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.962, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:43.962, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:43.963, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.963, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:43.994, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:43.994, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:43.995, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:43.995, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.025, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:44.025, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:44.025, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.025, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.055, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=06/07 09:47:44.055, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:44.055, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=06/07 09:47:44.055, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:44.086, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:44.086, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=06/07 09:47:44.086, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.086, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.117, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:44.117, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:44.117, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.117, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.148, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:44.148, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:44.149, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.149, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.180, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:44.180, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:44.180, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.180, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.211, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:44.211, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:44.211, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.211, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.228, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:44.228, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:44.248, level=WARN, class = Host, msg = Match timeout 19 ms(0)
time=06/07 09:47:44.248, level=WARN, class = BtromHandshake, timeout = 36, message = response timeout
time=06/07 09:47:44.248, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.248, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.279, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:44.279, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:44.279, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.279, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.310, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:44.310, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:44.310, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.310, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.341, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:44.341, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:44.342, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.342, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.372, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:44.372, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:44.373, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.373, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.404, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:44.404, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:44.404, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.404, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.435, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:44.435, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:44.435, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.435, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.435, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:44.435, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 34.16 B/s
time=06/07 09:47:44.435, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:44.466, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:44.466, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:44.466, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.466, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.498, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:44.498, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:44.498, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.498, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.529, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:44.529, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:44.529, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.529, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.560, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:44.560, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:44.560, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.560, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.591, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:44.591, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:44.592, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.592, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.623, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:44.623, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:44.623, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.623, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.638, level=DEBUG, [COM4] Read(8)
time=06/07 09:47:44.638, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=06/07 09:47:44.638, level=INFO, [RxP 8] 2450414952343931
time=06/07 09:47:44.654, level=DEBUG, [COM4] Read(5)
time=06/07 09:47:44.654, level=INFO, [RxP 5] 2A33360D0A
time=06/07 09:47:44.654, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:44.654, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.654, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.685, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:44.685, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:44.685, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.685, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.716, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:44.716, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:44.717, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.717, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.748, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:44.748, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:44.748, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.748, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.782, level=WARN, class = Host, msg = Match timeout 34 ms(0)
time=06/07 09:47:44.782, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=06/07 09:47:44.782, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.782, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.813, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:44.813, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:44.814, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.814, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.845, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:44.845, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:44.845, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:44.845, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 24.22 B/s
time=06/07 09:47:44.845, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.845, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.845, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:44.876, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:44.876, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:44.876, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.876, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.907, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:44.907, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:44.907, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.907, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.938, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:44.938, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:44.939, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.939, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:44.970, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:44.970, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:44.970, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:44.970, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.001, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:45.001, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:45.001, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.001, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.032, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:45.032, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:45.032, level=DEBUG, [COM4] Read(4)
time=06/07 09:47:45.032, level=INFO, [RxP 4] 24504149
time=06/07 09:47:45.032, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.032, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.048, level=DEBUG, [COM4] Read(9)
time=06/07 09:47:45.048, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 19.69 B/s
time=06/07 09:47:45.048, level=INFO, [RxP 9] 523439312A33360D0A
time=06/07 09:47:45.079, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:45.079, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:45.079, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.079, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.110, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:45.110, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:45.110, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.110, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.141, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:45.141, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:45.142, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.142, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.173, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:45.173, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:45.173, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.173, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.204, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:45.204, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:45.204, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.204, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.235, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:45.235, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:45.235, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:45.235, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:45.235, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.235, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.266, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:45.266, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:45.267, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.267, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.297, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:45.297, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:45.298, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.298, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.333, level=WARN, class = Host, msg = Match timeout 35 ms(0)
time=06/07 09:47:45.333, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=06/07 09:47:45.333, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.333, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.364, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:45.364, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:45.364, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.364, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.395, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:45.395, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:45.395, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.395, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.426, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:45.426, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:45.426, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.426, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.458, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:45.458, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:45.458, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:45.458, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 31.72 B/s
time=06/07 09:47:45.458, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.458, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.458, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:45.489, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:45.489, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:45.489, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.489, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.520, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:45.520, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:45.520, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.520, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.551, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:45.551, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:45.552, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.552, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.583, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:45.583, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:45.583, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.583, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.614, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:45.614, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:45.614, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.614, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.645, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:45.645, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:45.645, level=DEBUG, [COM4] Read(12)
time=06/07 09:47:45.645, level=INFO, [RxP 12] 24504149523439312A33360D
time=06/07 09:47:45.645, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.645, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.661, level=DEBUG, [COM4] Read(1)
time=06/07 09:47:45.661, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 59.11 B/s
time=06/07 09:47:45.661, level=INFO, [RxP 1] 0A
time=06/07 09:47:45.692, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:45.692, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:45.692, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.692, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.723, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:45.723, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:45.724, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.724, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.754, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:45.754, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:45.755, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.755, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.786, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:45.786, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:45.786, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.786, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.816, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:45.816, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:45.817, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.817, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.850, level=WARN, class = Host, msg = Match timeout 33 ms(0)
time=06/07 09:47:45.850, level=WARN, class = BtromHandshake, timeout = 33, message = response timeout
time=06/07 09:47:45.850, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:45.850, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:45.850, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.850, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.881, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:45.881, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:45.881, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.881, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.912, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:45.912, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:45.913, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.913, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.944, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:45.944, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:45.944, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.944, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:45.975, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:45.975, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:45.975, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:45.975, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:46.006, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:46.006, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:46.007, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:46.007, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:46.037, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:46.037, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:46.037, level=DEBUG, [COM4] Read(6)
time=06/07 09:47:46.037, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 34.50 B/s
time=06/07 09:47:46.038, level=INFO, [RxP 6] 245041495234
time=06/07 09:47:46.038, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:46.038, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:46.053, level=DEBUG, [COM4] Read(7)
time=06/07 09:47:46.053, level=INFO, [RxP 7] 39312A33360D0A
time=06/07 09:47:46.084, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:46.084, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:46.085, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:46.085, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:46.115, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:46.115, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:46.116, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:46.116, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:46.147, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:46.147, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:46.147, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:46.147, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:46.178, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:46.178, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:46.178, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:46.178, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:46.209, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:46.209, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:46.210, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:46.210, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:46.240, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:46.240, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:46.241, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:46.241, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:46.256, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:46.256, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 32.01 B/s
time=06/07 09:47:46.256, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:46.287, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:46.287, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:46.288, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:46.288, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:46.319, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:46.319, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:46.319, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:46.319, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:46.350, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:46.350, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:46.350, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:46.350, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:46.383, level=WARN, class = Host, msg = Match timeout 33 ms(0)
time=06/07 09:47:46.383, level=WARN, class = BtromHandshake, timeout = 33, message = response timeout
time=06/07 09:47:46.383, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:46.383, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:46.414, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:46.414, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:46.414, level=ERROR, class = BtromHandshake, result = fail, timeout = 10024, error_message = btrom handshake timeout
time=06/07 09:47:46.414, level=ERROR, D:\project\crossover\src\task\flash\download_da_uart.cc:141(-1): class = DownloadDa_UART, error_message = btrom handshake fail
time=06/07 09:47:46.414, level=ERROR, class = Task, task_name = DownloadDa_UART, error_message = Fail to execute RunTask()
time=06/07 09:47:46.414, level=DEBUG, class = DownloadDa_UART, task_time = 10.026
time=06/07 09:47:46.414, level=DEBUG, class = Controller, RemoveObserver = callback
time=06/07 09:47:46.414, level=DEBUG, class = CallbackManager, deregister = callback
time=06/07 09:47:46.414, level=DEBUG, class = DisconnectDUT, state = init data, result = ok
time=06/07 09:47:46.523, level=DEBUG, class = SerialHost, state = close
time=06/07 09:47:46.523, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=06/07 09:47:46.523, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=06/07 09:47:46.524, level=DEBUG, class = UartDev, state = disconnect
time=06/07 09:47:46.524, level=DEBUG, class = DisconnectDUT, state = disconnect, device_name = DUT, device_type = UART
time=06/07 09:47:46.524, level=DEBUG, class = DisconnectDUT, task_time = 0.109
time=06/07 09:47:46.524, level=DEBUG, class = LogService, state = CloseLogFile, pass_fail = 1, postfix = 
