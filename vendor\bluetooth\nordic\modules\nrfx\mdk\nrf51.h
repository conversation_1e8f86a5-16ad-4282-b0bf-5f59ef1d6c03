/*
 * Copyright (c) 2010 - 2018, Nordic Semiconductor ASA
 * 
 * All rights reserved.
 * 
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 * 
 * 1. Redistributions of source code must retain the above copyright notice, this
 * list of conditions and the following disclaimer.
 * 
 * 2. Redistributions in binary form, except as embedded into a Nordic
 * Semiconductor ASA integrated circuit in a product or a software update for
 * such product, must reproduce the above copyright notice, this list of
 * conditions and the following disclaimer in the documentation and/or other
 * materials provided with the distribution.
 * 
 * 3. Neither the name of Nordic Semiconductor ASA nor the names of its
 * contributors may be used to endorse or promote products derived from this
 * software without specific prior written permission.
 * 
 * 4. This software, with or without modification, must only be used with a
 * Nordic Semiconductor ASA integrated circuit.
 * 
 * 5. Any software provided in binary form under this license must not be reverse
 * engineered, decompiled, modified and/or disassembled.
 * 
 * THIS SOFTWARE IS PROVIDED BY NORDIC SEMICONDUCTOR ASA "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL NORDIC SEMICONDUCTOR ASA OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * @file     nrf51.h
 * @brief    CMSIS HeaderFile
 * @version  522
 * @date     23. August 2019
 * @note     Generated by SVDConv V3.3.25 on Friday, 23.08.2019 12:17:03
 *           from File 'nrf51.svd',
 *           last modified on Friday, 23.08.2019 10:16:59
 */



/** @addtogroup Nordic Semiconductor
  * @{
  */


/** @addtogroup nrf51
  * @{
  */


#ifndef NRF51_H
#define NRF51_H

#ifdef __cplusplus
extern "C" {
#endif


/** @addtogroup Configuration_of_CMSIS
  * @{
  */



/* =========================================================================================================================== */
/* ================                                Interrupt Number Definition                                ================ */
/* =========================================================================================================================== */

typedef enum {
/* =======================================  ARM Cortex-M0 Specific Interrupt Numbers  ======================================== */
  Reset_IRQn                = -15,              /*!< -15  Reset Vector, invoked on Power up and warm reset                     */
  NonMaskableInt_IRQn       = -14,              /*!< -14  Non maskable Interrupt, cannot be stopped or preempted               */
  HardFault_IRQn            = -13,              /*!< -13  Hard Fault, all classes of Fault                                     */
  SVCall_IRQn               =  -5,              /*!< -5 System Service Call via SVC instruction                                */
  PendSV_IRQn               =  -2,              /*!< -2 Pendable request for system service                                    */
  SysTick_IRQn              =  -1,              /*!< -1 System Tick Timer                                                      */
/* ===========================================  nrf51 Specific Interrupt Numbers  ============================================ */
  POWER_CLOCK_IRQn          =   0,              /*!< 0  POWER_CLOCK                                                            */
  RADIO_IRQn                =   1,              /*!< 1  RADIO                                                                  */
  UART0_IRQn                =   2,              /*!< 2  UART0                                                                  */
  SPI0_TWI0_IRQn            =   3,              /*!< 3  SPI0_TWI0                                                              */
  SPI1_TWI1_IRQn            =   4,              /*!< 4  SPI1_TWI1                                                              */
  GPIOTE_IRQn               =   6,              /*!< 6  GPIOTE                                                                 */
  ADC_IRQn                  =   7,              /*!< 7  ADC                                                                    */
  TIMER0_IRQn               =   8,              /*!< 8  TIMER0                                                                 */
  TIMER1_IRQn               =   9,              /*!< 9  TIMER1                                                                 */
  TIMER2_IRQn               =  10,              /*!< 10 TIMER2                                                                 */
  RTC0_IRQn                 =  11,              /*!< 11 RTC0                                                                   */
  TEMP_IRQn                 =  12,              /*!< 12 TEMP                                                                   */
  RNG_IRQn                  =  13,              /*!< 13 RNG                                                                    */
  ECB_IRQn                  =  14,              /*!< 14 ECB                                                                    */
  CCM_AAR_IRQn              =  15,              /*!< 15 CCM_AAR                                                                */
  WDT_IRQn                  =  16,              /*!< 16 WDT                                                                    */
  RTC1_IRQn                 =  17,              /*!< 17 RTC1                                                                   */
  QDEC_IRQn                 =  18,              /*!< 18 QDEC                                                                   */
  LPCOMP_IRQn               =  19,              /*!< 19 LPCOMP                                                                 */
  SWI0_IRQn                 =  20,              /*!< 20 SWI0                                                                   */
  SWI1_IRQn                 =  21,              /*!< 21 SWI1                                                                   */
  SWI2_IRQn                 =  22,              /*!< 22 SWI2                                                                   */
  SWI3_IRQn                 =  23,              /*!< 23 SWI3                                                                   */
  SWI4_IRQn                 =  24,              /*!< 24 SWI4                                                                   */
  SWI5_IRQn                 =  25               /*!< 25 SWI5                                                                   */
} IRQn_Type;



/* =========================================================================================================================== */
/* ================                           Processor and Core Peripheral Section                           ================ */
/* =========================================================================================================================== */

/* ===========================  Configuration of the ARM Cortex-M0 Processor and Core Peripherals  =========================== */
#define __CM0_REV                 0x0301U       /*!< CM0 Core Revision                                                         */
#define __DSP_PRESENT                  0        /*!< DSP present or not                                                        */
#define __VTOR_PRESENT                 0        /*!< Set to 1 if CPU supports Vector Table Offset Register                     */
#define __NVIC_PRIO_BITS               2        /*!< Number of Bits used for Priority Levels                                   */
#define __Vendor_SysTickConfig         0        /*!< Set to 1 if different SysTick Config is used                              */
#define __MPU_PRESENT                  0        /*!< MPU present                                                               */
#define __FPU_PRESENT                  0        /*!< FPU present                                                               */


/** @} */ /* End of group Configuration_of_CMSIS */

#include "core_cm0.h"                           /*!< ARM Cortex-M0 processor and core peripherals                              */
#include "system_nrf51.h"                       /*!< nrf51 System                                                              */

#ifndef __IM                                    /*!< Fallback for older CMSIS versions                                         */
  #define __IM   __I
#endif
#ifndef __OM                                    /*!< Fallback for older CMSIS versions                                         */
  #define __OM   __O
#endif
#ifndef __IOM                                   /*!< Fallback for older CMSIS versions                                         */
  #define __IOM  __IO
#endif


/* ========================================  Start of section using anonymous unions  ======================================== */
#if defined (__CC_ARM)
  #pragma push
  #pragma anon_unions
#elif defined (__ICCARM__)
  #pragma language=extended
#elif defined(__ARMCC_VERSION) && (__ARMCC_VERSION >= 6010050)
  #pragma clang diagnostic push
  #pragma clang diagnostic ignored "-Wc11-extensions"
  #pragma clang diagnostic ignored "-Wreserved-id-macro"
  #pragma clang diagnostic ignored "-Wgnu-anonymous-struct"
  #pragma clang diagnostic ignored "-Wnested-anon-types"
#elif defined (__GNUC__)
  /* anonymous unions are enabled by default */
#elif defined (__TMS470__)
  /* anonymous unions are enabled by default */
#elif defined (__TASKING__)
  #pragma warning 586
#elif defined (__CSMC__)
  /* anonymous unions are enabled by default */
#else
  #warning Not supported compiler type
#endif


/* =========================================================================================================================== */
/* ================                              Device Specific Cluster Section                              ================ */
/* =========================================================================================================================== */


/** @addtogroup Device_Peripheral_clusters
  * @{
  */


/**
  * @brief PPI_TASKS_CHG [TASKS_CHG] (Channel group tasks.)
  */
typedef struct {
  __OM  uint32_t  EN;                           /*!< (@ 0x00000000) Enable channel group.                                      */
  __OM  uint32_t  DIS;                          /*!< (@ 0x00000004) Disable channel group.                                     */
} PPI_TASKS_CHG_Type;                           /*!< Size = 8 (0x8)                                                            */


/**
  * @brief PPI_CH [CH] (PPI Channel.)
  */
typedef struct {
  __IOM uint32_t  EEP;                          /*!< (@ 0x00000000) Channel event end-point.                                   */
  __IOM uint32_t  TEP;                          /*!< (@ 0x00000004) Channel task end-point.                                    */
} PPI_CH_Type;                                  /*!< Size = 8 (0x8)                                                            */


/** @} */ /* End of group Device_Peripheral_clusters */


/* =========================================================================================================================== */
/* ================                            Device Specific Peripheral Section                             ================ */
/* =========================================================================================================================== */


/** @addtogroup Device_Peripheral_peripherals
  * @{
  */



/* =========================================================================================================================== */
/* ================                                           POWER                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Power Control. (POWER)
  */

typedef struct {                                /*!< (@ 0x40000000) POWER Structure                                            */
  __IM  uint32_t  RESERVED[30];
  __OM  uint32_t  TASKS_CONSTLAT;               /*!< (@ 0x00000078) Enable constant latency mode.                              */
  __OM  uint32_t  TASKS_LOWPWR;                 /*!< (@ 0x0000007C) Enable low power mode (variable latency).                  */
  __IM  uint32_t  RESERVED1[34];
  __IOM uint32_t  EVENTS_POFWARN;               /*!< (@ 0x00000108) Power failure warning.                                     */
  __IM  uint32_t  RESERVED2[126];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Interrupt enable set register.                             */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Interrupt enable clear register.                           */
  __IM  uint32_t  RESERVED3[61];
  __IOM uint32_t  RESETREAS;                    /*!< (@ 0x00000400) Reset reason.                                              */
  __IM  uint32_t  RESERVED4[9];
  __IM  uint32_t  RAMSTATUS;                    /*!< (@ 0x00000428) Ram status register.                                       */
  __IM  uint32_t  RESERVED5[53];
  __OM  uint32_t  SYSTEMOFF;                    /*!< (@ 0x00000500) System off register.                                       */
  __IM  uint32_t  RESERVED6[3];
  __IOM uint32_t  POFCON;                       /*!< (@ 0x00000510) Power failure configuration.                               */
  __IM  uint32_t  RESERVED7[2];
  __IOM uint32_t  GPREGRET;                     /*!< (@ 0x0000051C) General purpose retention register. This register
                                                                    is a retained register.                                    */
  __IM  uint32_t  RESERVED8;
  __IOM uint32_t  RAMON;                        /*!< (@ 0x00000524) Ram on/off.                                                */
  __IM  uint32_t  RESERVED9[7];
  __IOM uint32_t  RESET;                        /*!< (@ 0x00000544) Pin reset functionality configuration register.
                                                                    This register is a retained register.                      */
  __IM  uint32_t  RESERVED10[3];
  __IOM uint32_t  RAMONB;                       /*!< (@ 0x00000554) Ram on/off.                                                */
  __IM  uint32_t  RESERVED11[8];
  __IOM uint32_t  DCDCEN;                       /*!< (@ 0x00000578) DCDC converter enable configuration register.              */
  __IM  uint32_t  RESERVED12[291];
  __IOM uint32_t  DCDCFORCE;                    /*!< (@ 0x00000A08) DCDC power-up force register.                              */
} NRF_POWER_Type;                               /*!< Size = 2572 (0xa0c)                                                       */



/* =========================================================================================================================== */
/* ================                                           CLOCK                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Clock control. (CLOCK)
  */

typedef struct {                                /*!< (@ 0x40000000) CLOCK Structure                                            */
  __OM  uint32_t  TASKS_HFCLKSTART;             /*!< (@ 0x00000000) Start HFCLK clock source.                                  */
  __OM  uint32_t  TASKS_HFCLKSTOP;              /*!< (@ 0x00000004) Stop HFCLK clock source.                                   */
  __OM  uint32_t  TASKS_LFCLKSTART;             /*!< (@ 0x00000008) Start LFCLK clock source.                                  */
  __OM  uint32_t  TASKS_LFCLKSTOP;              /*!< (@ 0x0000000C) Stop LFCLK clock source.                                   */
  __OM  uint32_t  TASKS_CAL;                    /*!< (@ 0x00000010) Start calibration of LFCLK RC oscillator.                  */
  __OM  uint32_t  TASKS_CTSTART;                /*!< (@ 0x00000014) Start calibration timer.                                   */
  __OM  uint32_t  TASKS_CTSTOP;                 /*!< (@ 0x00000018) Stop calibration timer.                                    */
  __IM  uint32_t  RESERVED[57];
  __IOM uint32_t  EVENTS_HFCLKSTARTED;          /*!< (@ 0x00000100) HFCLK oscillator started.                                  */
  __IOM uint32_t  EVENTS_LFCLKSTARTED;          /*!< (@ 0x00000104) LFCLK oscillator started.                                  */
  __IM  uint32_t  RESERVED1;
  __IOM uint32_t  EVENTS_DONE;                  /*!< (@ 0x0000010C) Calibration of LFCLK RC oscillator completed.              */
  __IOM uint32_t  EVENTS_CTTO;                  /*!< (@ 0x00000110) Calibration timer timeout.                                 */
  __IM  uint32_t  RESERVED2[124];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Interrupt enable set register.                             */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Interrupt enable clear register.                           */
  __IM  uint32_t  RESERVED3[63];
  __IM  uint32_t  HFCLKRUN;                     /*!< (@ 0x00000408) Task HFCLKSTART trigger status.                            */
  __IM  uint32_t  HFCLKSTAT;                    /*!< (@ 0x0000040C) High frequency clock status.                               */
  __IM  uint32_t  RESERVED4;
  __IM  uint32_t  LFCLKRUN;                     /*!< (@ 0x00000414) Task LFCLKSTART triggered status.                          */
  __IM  uint32_t  LFCLKSTAT;                    /*!< (@ 0x00000418) Low frequency clock status.                                */
  __IM  uint32_t  LFCLKSRCCOPY;                 /*!< (@ 0x0000041C) Clock source for the LFCLK clock, set when task
                                                                    LKCLKSTART is triggered.                                   */
  __IM  uint32_t  RESERVED5[62];
  __IOM uint32_t  LFCLKSRC;                     /*!< (@ 0x00000518) Clock source for the LFCLK clock.                          */
  __IM  uint32_t  RESERVED6[7];
  __IOM uint32_t  CTIV;                         /*!< (@ 0x00000538) Calibration timer interval.                                */
  __IM  uint32_t  RESERVED7[5];
  __IOM uint32_t  XTALFREQ;                     /*!< (@ 0x00000550) Crystal frequency.                                         */
} NRF_CLOCK_Type;                               /*!< Size = 1364 (0x554)                                                       */



/* =========================================================================================================================== */
/* ================                                            MPU                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Memory Protection Unit. (MPU)
  */

typedef struct {                                /*!< (@ 0x40000000) MPU Structure                                              */
  __IM  uint32_t  RESERVED[330];
  __IOM uint32_t  PERR0;                        /*!< (@ 0x00000528) Configuration of peripherals in mpu regions.               */
  __IOM uint32_t  RLENR0;                       /*!< (@ 0x0000052C) Length of RAM region 0.                                    */
  __IM  uint32_t  RESERVED1[52];
  __IOM uint32_t  PROTENSET0;                   /*!< (@ 0x00000600) Erase and write protection bit enable set register.        */
  __IOM uint32_t  PROTENSET1;                   /*!< (@ 0x00000604) Erase and write protection bit enable set register.        */
  __IOM uint32_t  DISABLEINDEBUG;               /*!< (@ 0x00000608) Disable erase and write protection mechanism
                                                                    in debug mode.                                             */
  __IOM uint32_t  PROTBLOCKSIZE;                /*!< (@ 0x0000060C) Erase and write protection block size.                     */
} NRF_MPU_Type;                                 /*!< Size = 1552 (0x610)                                                       */



/* =========================================================================================================================== */
/* ================                                           RADIO                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief The radio. (RADIO)
  */

typedef struct {                                /*!< (@ 0x40001000) RADIO Structure                                            */
  __OM  uint32_t  TASKS_TXEN;                   /*!< (@ 0x00000000) Enable radio in TX mode.                                   */
  __OM  uint32_t  TASKS_RXEN;                   /*!< (@ 0x00000004) Enable radio in RX mode.                                   */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000008) Start radio.                                               */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x0000000C) Stop radio.                                                */
  __OM  uint32_t  TASKS_DISABLE;                /*!< (@ 0x00000010) Disable radio.                                             */
  __OM  uint32_t  TASKS_RSSISTART;              /*!< (@ 0x00000014) Start the RSSI and take one sample of the receive
                                                                    signal strength.                                           */
  __OM  uint32_t  TASKS_RSSISTOP;               /*!< (@ 0x00000018) Stop the RSSI measurement.                                 */
  __OM  uint32_t  TASKS_BCSTART;                /*!< (@ 0x0000001C) Start the bit counter.                                     */
  __OM  uint32_t  TASKS_BCSTOP;                 /*!< (@ 0x00000020) Stop the bit counter.                                      */
  __IM  uint32_t  RESERVED[55];
  __IOM uint32_t  EVENTS_READY;                 /*!< (@ 0x00000100) Ready event.                                               */
  __IOM uint32_t  EVENTS_ADDRESS;               /*!< (@ 0x00000104) Address event.                                             */
  __IOM uint32_t  EVENTS_PAYLOAD;               /*!< (@ 0x00000108) Payload event.                                             */
  __IOM uint32_t  EVENTS_END;                   /*!< (@ 0x0000010C) End event.                                                 */
  __IOM uint32_t  EVENTS_DISABLED;              /*!< (@ 0x00000110) Disable event.                                             */
  __IOM uint32_t  EVENTS_DEVMATCH;              /*!< (@ 0x00000114) A device address match occurred on the last received
                                                                    packet.                                                    */
  __IOM uint32_t  EVENTS_DEVMISS;               /*!< (@ 0x00000118) No device address match occurred on the last
                                                                    received packet.                                           */
  __IOM uint32_t  EVENTS_RSSIEND;               /*!< (@ 0x0000011C) Sampling of the receive signal strength complete.
                                                                    A new RSSI sample is ready for readout at
                                                                    the RSSISAMPLE register.                                   */
  __IM  uint32_t  RESERVED1[2];
  __IOM uint32_t  EVENTS_BCMATCH;               /*!< (@ 0x00000128) Bit counter reached bit count value specified
                                                                    in BCC register.                                           */
  __IM  uint32_t  RESERVED2[53];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts for the radio.                                   */
  __IM  uint32_t  RESERVED3[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Interrupt enable set register.                             */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Interrupt enable clear register.                           */
  __IM  uint32_t  RESERVED4[61];
  __IM  uint32_t  CRCSTATUS;                    /*!< (@ 0x00000400) CRC status of received packet.                             */
  __IM  uint32_t  RESERVED5;
  __IM  uint32_t  RXMATCH;                      /*!< (@ 0x00000408) Received address.                                          */
  __IM  uint32_t  RXCRC;                        /*!< (@ 0x0000040C) Received CRC.                                              */
  __IM  uint32_t  DAI;                          /*!< (@ 0x00000410) Device address match index.                                */
  __IM  uint32_t  RESERVED6[60];
  __IOM uint32_t  PACKETPTR;                    /*!< (@ 0x00000504) Packet pointer. Decision point: START task.                */
  __IOM uint32_t  FREQUENCY;                    /*!< (@ 0x00000508) Frequency.                                                 */
  __IOM uint32_t  TXPOWER;                      /*!< (@ 0x0000050C) Output power.                                              */
  __IOM uint32_t  MODE;                         /*!< (@ 0x00000510) Data rate and modulation.                                  */
  __IOM uint32_t  PCNF0;                        /*!< (@ 0x00000514) Packet configuration 0.                                    */
  __IOM uint32_t  PCNF1;                        /*!< (@ 0x00000518) Packet configuration 1.                                    */
  __IOM uint32_t  BASE0;                        /*!< (@ 0x0000051C) Radio base address 0. Decision point: START task.          */
  __IOM uint32_t  BASE1;                        /*!< (@ 0x00000520) Radio base address 1. Decision point: START task.          */
  __IOM uint32_t  PREFIX0;                      /*!< (@ 0x00000524) Prefixes bytes for logical addresses 0 to 3.               */
  __IOM uint32_t  PREFIX1;                      /*!< (@ 0x00000528) Prefixes bytes for logical addresses 4 to 7.               */
  __IOM uint32_t  TXADDRESS;                    /*!< (@ 0x0000052C) Transmit address select.                                   */
  __IOM uint32_t  RXADDRESSES;                  /*!< (@ 0x00000530) Receive address select.                                    */
  __IOM uint32_t  CRCCNF;                       /*!< (@ 0x00000534) CRC configuration.                                         */
  __IOM uint32_t  CRCPOLY;                      /*!< (@ 0x00000538) CRC polynomial.                                            */
  __IOM uint32_t  CRCINIT;                      /*!< (@ 0x0000053C) CRC initial value.                                         */
  __IOM uint32_t  TEST;                         /*!< (@ 0x00000540) Test features enable register.                             */
  __IOM uint32_t  TIFS;                         /*!< (@ 0x00000544) Inter Frame Spacing in microseconds.                       */
  __IM  uint32_t  RSSISAMPLE;                   /*!< (@ 0x00000548) RSSI sample.                                               */
  __IM  uint32_t  RESERVED7;
  __IM  uint32_t  STATE;                        /*!< (@ 0x00000550) Current radio state.                                       */
  __IOM uint32_t  DATAWHITEIV;                  /*!< (@ 0x00000554) Data whitening initial value.                              */
  __IM  uint32_t  RESERVED8[2];
  __IOM uint32_t  BCC;                          /*!< (@ 0x00000560) Bit counter compare.                                       */
  __IM  uint32_t  RESERVED9[39];
  __IOM uint32_t  DAB[8];                       /*!< (@ 0x00000600) Device address base segment.                               */
  __IOM uint32_t  DAP[8];                       /*!< (@ 0x00000620) Device address prefix.                                     */
  __IOM uint32_t  DACNF;                        /*!< (@ 0x00000640) Device address match configuration.                        */
  __IM  uint32_t  RESERVED10[56];
  __IOM uint32_t  OVERRIDE0;                    /*!< (@ 0x00000724) Trim value override register 0.                            */
  __IOM uint32_t  OVERRIDE1;                    /*!< (@ 0x00000728) Trim value override register 1.                            */
  __IOM uint32_t  OVERRIDE2;                    /*!< (@ 0x0000072C) Trim value override register 2.                            */
  __IOM uint32_t  OVERRIDE3;                    /*!< (@ 0x00000730) Trim value override register 3.                            */
  __IOM uint32_t  OVERRIDE4;                    /*!< (@ 0x00000734) Trim value override register 4.                            */
  __IM  uint32_t  RESERVED11[561];
  __IOM uint32_t  POWER;                        /*!< (@ 0x00000FFC) Peripheral power control.                                  */
} NRF_RADIO_Type;                               /*!< Size = 4096 (0x1000)                                                      */



/* =========================================================================================================================== */
/* ================                                           UART0                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Universal Asynchronous Receiver/Transmitter. (UART0)
  */

typedef struct {                                /*!< (@ 0x40002000) UART0 Structure                                            */
  __OM  uint32_t  TASKS_STARTRX;                /*!< (@ 0x00000000) Start UART receiver.                                       */
  __OM  uint32_t  TASKS_STOPRX;                 /*!< (@ 0x00000004) Stop UART receiver.                                        */
  __OM  uint32_t  TASKS_STARTTX;                /*!< (@ 0x00000008) Start UART transmitter.                                    */
  __OM  uint32_t  TASKS_STOPTX;                 /*!< (@ 0x0000000C) Stop UART transmitter.                                     */
  __IM  uint32_t  RESERVED[3];
  __OM  uint32_t  TASKS_SUSPEND;                /*!< (@ 0x0000001C) Suspend UART.                                              */
  __IM  uint32_t  RESERVED1[56];
  __IOM uint32_t  EVENTS_CTS;                   /*!< (@ 0x00000100) CTS activated.                                             */
  __IOM uint32_t  EVENTS_NCTS;                  /*!< (@ 0x00000104) CTS deactivated.                                           */
  __IOM uint32_t  EVENTS_RXDRDY;                /*!< (@ 0x00000108) Data received in RXD.                                      */
  __IM  uint32_t  RESERVED2[4];
  __IOM uint32_t  EVENTS_TXDRDY;                /*!< (@ 0x0000011C) Data sent from TXD.                                        */
  __IM  uint32_t  RESERVED3;
  __IOM uint32_t  EVENTS_ERROR;                 /*!< (@ 0x00000124) Error detected.                                            */
  __IM  uint32_t  RESERVED4[7];
  __IOM uint32_t  EVENTS_RXTO;                  /*!< (@ 0x00000144) Receiver timeout.                                          */
  __IM  uint32_t  RESERVED5[46];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts for UART.                                        */
  __IM  uint32_t  RESERVED6[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Interrupt enable set register.                             */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Interrupt enable clear register.                           */
  __IM  uint32_t  RESERVED7[93];
  __IOM uint32_t  ERRORSRC;                     /*!< (@ 0x00000480) Error source. Write error field to 1 to clear
                                                                    error.                                                     */
  __IM  uint32_t  RESERVED8[31];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable UART and acquire IOs.                               */
  __IM  uint32_t  RESERVED9;
  __IOM uint32_t  PSELRTS;                      /*!< (@ 0x00000508) Pin select for RTS.                                        */
  __IOM uint32_t  PSELTXD;                      /*!< (@ 0x0000050C) Pin select for TXD.                                        */
  __IOM uint32_t  PSELCTS;                      /*!< (@ 0x00000510) Pin select for CTS.                                        */
  __IOM uint32_t  PSELRXD;                      /*!< (@ 0x00000514) Pin select for RXD.                                        */
  __IM  uint32_t  RXD;                          /*!< (@ 0x00000518) RXD register. On read action the buffer pointer
                                                                    is displaced. Once read the character is
                                                                    consumed. If read when no character available,
                                                                    the UART will stop working.                                */
  __OM  uint32_t  TXD;                          /*!< (@ 0x0000051C) TXD register.                                              */
  __IM  uint32_t  RESERVED10;
  __IOM uint32_t  BAUDRATE;                     /*!< (@ 0x00000524) UART Baudrate.                                             */
  __IM  uint32_t  RESERVED11[17];
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x0000056C) Configuration of parity and hardware flow control
                                                                    register.                                                  */
  __IM  uint32_t  RESERVED12[675];
  __IOM uint32_t  POWER;                        /*!< (@ 0x00000FFC) Peripheral power control.                                  */
} NRF_UART_Type;                                /*!< Size = 4096 (0x1000)                                                      */



/* =========================================================================================================================== */
/* ================                                           SPI0                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief SPI master 0. (SPI0)
  */

typedef struct {                                /*!< (@ 0x40003000) SPI0 Structure                                             */
  __IM  uint32_t  RESERVED[66];
  __IOM uint32_t  EVENTS_READY;                 /*!< (@ 0x00000108) TXD byte sent and RXD byte received.                       */
  __IM  uint32_t  RESERVED1[126];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Interrupt enable set register.                             */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Interrupt enable clear register.                           */
  __IM  uint32_t  RESERVED2[125];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable SPI.                                                */
  __IM  uint32_t  RESERVED3;
  __IOM uint32_t  PSELSCK;                      /*!< (@ 0x00000508) Pin select for SCK.                                        */
  __IOM uint32_t  PSELMOSI;                     /*!< (@ 0x0000050C) Pin select for MOSI.                                       */
  __IOM uint32_t  PSELMISO;                     /*!< (@ 0x00000510) Pin select for MISO.                                       */
  __IM  uint32_t  RESERVED4;
  __IM  uint32_t  RXD;                          /*!< (@ 0x00000518) RX data.                                                   */
  __IOM uint32_t  TXD;                          /*!< (@ 0x0000051C) TX data.                                                   */
  __IM  uint32_t  RESERVED5;
  __IOM uint32_t  FREQUENCY;                    /*!< (@ 0x00000524) SPI frequency                                              */
  __IM  uint32_t  RESERVED6[11];
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x00000554) Configuration register.                                    */
  __IM  uint32_t  RESERVED7[681];
  __IOM uint32_t  POWER;                        /*!< (@ 0x00000FFC) Peripheral power control.                                  */
} NRF_SPI_Type;                                 /*!< Size = 4096 (0x1000)                                                      */



/* =========================================================================================================================== */
/* ================                                           TWI0                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Two-wire interface master 0. (TWI0)
  */

typedef struct {                                /*!< (@ 0x40003000) TWI0 Structure                                             */
  __OM  uint32_t  TASKS_STARTRX;                /*!< (@ 0x00000000) Start 2-Wire master receive sequence.                      */
  __IM  uint32_t  RESERVED;
  __OM  uint32_t  TASKS_STARTTX;                /*!< (@ 0x00000008) Start 2-Wire master transmit sequence.                     */
  __IM  uint32_t  RESERVED1[2];
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000014) Stop 2-Wire transaction.                                   */
  __IM  uint32_t  RESERVED2;
  __OM  uint32_t  TASKS_SUSPEND;                /*!< (@ 0x0000001C) Suspend 2-Wire transaction.                                */
  __OM  uint32_t  TASKS_RESUME;                 /*!< (@ 0x00000020) Resume 2-Wire transaction.                                 */
  __IM  uint32_t  RESERVED3[56];
  __IOM uint32_t  EVENTS_STOPPED;               /*!< (@ 0x00000104) Two-wire stopped.                                          */
  __IOM uint32_t  EVENTS_RXDREADY;              /*!< (@ 0x00000108) Two-wire ready to deliver new RXD byte received.           */
  __IM  uint32_t  RESERVED4[4];
  __IOM uint32_t  EVENTS_TXDSENT;               /*!< (@ 0x0000011C) Two-wire finished sending last TXD byte.                   */
  __IM  uint32_t  RESERVED5;
  __IOM uint32_t  EVENTS_ERROR;                 /*!< (@ 0x00000124) Two-wire error detected.                                   */
  __IM  uint32_t  RESERVED6[4];
  __IOM uint32_t  EVENTS_BB;                    /*!< (@ 0x00000138) Two-wire byte boundary.                                    */
  __IM  uint32_t  RESERVED7[3];
  __IOM uint32_t  EVENTS_SUSPENDED;             /*!< (@ 0x00000148) Two-wire suspended.                                        */
  __IM  uint32_t  RESERVED8[45];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts for TWI.                                         */
  __IM  uint32_t  RESERVED9[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Interrupt enable set register.                             */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Interrupt enable clear register.                           */
  __IM  uint32_t  RESERVED10[110];
  __IOM uint32_t  ERRORSRC;                     /*!< (@ 0x000004C4) Two-wire error source. Write error field to 1
                                                                    to clear error.                                            */
  __IM  uint32_t  RESERVED11[14];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable two-wire master.                                    */
  __IM  uint32_t  RESERVED12;
  __IOM uint32_t  PSELSCL;                      /*!< (@ 0x00000508) Pin select for SCL.                                        */
  __IOM uint32_t  PSELSDA;                      /*!< (@ 0x0000050C) Pin select for SDA.                                        */
  __IM  uint32_t  RESERVED13[2];
  __IM  uint32_t  RXD;                          /*!< (@ 0x00000518) RX data register.                                          */
  __IOM uint32_t  TXD;                          /*!< (@ 0x0000051C) TX data register.                                          */
  __IM  uint32_t  RESERVED14;
  __IOM uint32_t  FREQUENCY;                    /*!< (@ 0x00000524) Two-wire frequency.                                        */
  __IM  uint32_t  RESERVED15[24];
  __IOM uint32_t  ADDRESS;                      /*!< (@ 0x00000588) Address used in the two-wire transfer.                     */
  __IM  uint32_t  RESERVED16[668];
  __IOM uint32_t  POWER;                        /*!< (@ 0x00000FFC) Peripheral power control.                                  */
} NRF_TWI_Type;                                 /*!< Size = 4096 (0x1000)                                                      */



/* =========================================================================================================================== */
/* ================                                           SPIS1                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief SPI slave 1. (SPIS1)
  */

typedef struct {                                /*!< (@ 0x40004000) SPIS1 Structure                                            */
  __IM  uint32_t  RESERVED[9];
  __OM  uint32_t  TASKS_ACQUIRE;                /*!< (@ 0x00000024) Acquire SPI semaphore.                                     */
  __OM  uint32_t  TASKS_RELEASE;                /*!< (@ 0x00000028) Release SPI semaphore.                                     */
  __IM  uint32_t  RESERVED1[54];
  __IOM uint32_t  EVENTS_END;                   /*!< (@ 0x00000104) Granted transaction completed.                             */
  __IM  uint32_t  RESERVED2[2];
  __IOM uint32_t  EVENTS_ENDRX;                 /*!< (@ 0x00000110) End of RXD buffer reached                                  */
  __IM  uint32_t  RESERVED3[5];
  __IOM uint32_t  EVENTS_ACQUIRED;              /*!< (@ 0x00000128) Semaphore acquired.                                        */
  __IM  uint32_t  RESERVED4[53];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts for SPIS.                                        */
  __IM  uint32_t  RESERVED5[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Interrupt enable set register.                             */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Interrupt enable clear register.                           */
  __IM  uint32_t  RESERVED6[61];
  __IM  uint32_t  SEMSTAT;                      /*!< (@ 0x00000400) Semaphore status.                                          */
  __IM  uint32_t  RESERVED7[15];
  __IOM uint32_t  STATUS;                       /*!< (@ 0x00000440) Status from last transaction.                              */
  __IM  uint32_t  RESERVED8[47];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable SPIS.                                               */
  __IM  uint32_t  RESERVED9;
  __IOM uint32_t  PSELSCK;                      /*!< (@ 0x00000508) Pin select for SCK.                                        */
  __IOM uint32_t  PSELMISO;                     /*!< (@ 0x0000050C) Pin select for MISO.                                       */
  __IOM uint32_t  PSELMOSI;                     /*!< (@ 0x00000510) Pin select for MOSI.                                       */
  __IOM uint32_t  PSELCSN;                      /*!< (@ 0x00000514) Pin select for CSN.                                        */
  __IM  uint32_t  RESERVED10[7];
  __IOM uint32_t  RXDPTR;                       /*!< (@ 0x00000534) RX data pointer.                                           */
  __IOM uint32_t  MAXRX;                        /*!< (@ 0x00000538) Maximum number of bytes in the receive buffer.             */
  __IM  uint32_t  AMOUNTRX;                     /*!< (@ 0x0000053C) Number of bytes received in last granted transaction.      */
  __IM  uint32_t  RESERVED11;
  __IOM uint32_t  TXDPTR;                       /*!< (@ 0x00000544) TX data pointer.                                           */
  __IOM uint32_t  MAXTX;                        /*!< (@ 0x00000548) Maximum number of bytes in the transmit buffer.            */
  __IM  uint32_t  AMOUNTTX;                     /*!< (@ 0x0000054C) Number of bytes transmitted in last granted transaction.   */
  __IM  uint32_t  RESERVED12;
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x00000554) Configuration register.                                    */
  __IM  uint32_t  RESERVED13;
  __IOM uint32_t  DEF;                          /*!< (@ 0x0000055C) Default character.                                         */
  __IM  uint32_t  RESERVED14[24];
  __IOM uint32_t  ORC;                          /*!< (@ 0x000005C0) Over-read character.                                       */
  __IM  uint32_t  RESERVED15[654];
  __IOM uint32_t  POWER;                        /*!< (@ 0x00000FFC) Peripheral power control.                                  */
} NRF_SPIS_Type;                                /*!< Size = 4096 (0x1000)                                                      */



/* =========================================================================================================================== */
/* ================                                          GPIOTE                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief GPIO tasks and events. (GPIOTE)
  */

typedef struct {                                /*!< (@ 0x40006000) GPIOTE Structure                                           */
  __OM  uint32_t  TASKS_OUT[4];                 /*!< (@ 0x00000000) Tasks asssociated with GPIOTE channels.                    */
  __IM  uint32_t  RESERVED[60];
  __IOM uint32_t  EVENTS_IN[4];                 /*!< (@ 0x00000100) Tasks asssociated with GPIOTE channels.                    */
  __IM  uint32_t  RESERVED1[27];
  __IOM uint32_t  EVENTS_PORT;                  /*!< (@ 0x0000017C) Event generated from multiple pins.                        */
  __IM  uint32_t  RESERVED2[97];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Interrupt enable set register.                             */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Interrupt enable clear register.                           */
  __IM  uint32_t  RESERVED3[129];
  __IOM uint32_t  CONFIG[4];                    /*!< (@ 0x00000510) Channel configuration registers.                           */
  __IM  uint32_t  RESERVED4[695];
  __IOM uint32_t  POWER;                        /*!< (@ 0x00000FFC) Peripheral power control.                                  */
} NRF_GPIOTE_Type;                              /*!< Size = 4096 (0x1000)                                                      */



/* =========================================================================================================================== */
/* ================                                            ADC                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Analog to digital converter. (ADC)
  */

typedef struct {                                /*!< (@ 0x40007000) ADC Structure                                              */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start an ADC conversion.                                   */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stop ADC.                                                  */
  __IM  uint32_t  RESERVED[62];
  __IOM uint32_t  EVENTS_END;                   /*!< (@ 0x00000100) ADC conversion complete.                                   */
  __IM  uint32_t  RESERVED1[128];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Interrupt enable set register.                             */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Interrupt enable clear register.                           */
  __IM  uint32_t  RESERVED2[61];
  __IM  uint32_t  BUSY;                         /*!< (@ 0x00000400) ADC busy register.                                         */
  __IM  uint32_t  RESERVED3[63];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) ADC enable.                                                */
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x00000504) ADC configuration register.                                */
  __IM  uint32_t  RESULT;                       /*!< (@ 0x00000508) Result of ADC conversion.                                  */
  __IM  uint32_t  RESERVED4[700];
  __IOM uint32_t  POWER;                        /*!< (@ 0x00000FFC) Peripheral power control.                                  */
} NRF_ADC_Type;                                 /*!< Size = 4096 (0x1000)                                                      */



/* =========================================================================================================================== */
/* ================                                          TIMER0                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Timer 0. (TIMER0)
  */

typedef struct {                                /*!< (@ 0x40008000) TIMER0 Structure                                           */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start Timer.                                               */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stop Timer.                                                */
  __OM  uint32_t  TASKS_COUNT;                  /*!< (@ 0x00000008) Increment Timer (In counter mode).                         */
  __OM  uint32_t  TASKS_CLEAR;                  /*!< (@ 0x0000000C) Clear timer.                                               */
  __OM  uint32_t  TASKS_SHUTDOWN;               /*!< (@ 0x00000010) Shutdown timer.                                            */
  __IM  uint32_t  RESERVED[11];
  __OM  uint32_t  TASKS_CAPTURE[4];             /*!< (@ 0x00000040) Capture Timer value to CC[n] registers.                    */
  __IM  uint32_t  RESERVED1[60];
  __IOM uint32_t  EVENTS_COMPARE[4];            /*!< (@ 0x00000140) Compare event on CC[n] match.                              */
  __IM  uint32_t  RESERVED2[44];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts for Timer.                                       */
  __IM  uint32_t  RESERVED3[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Interrupt enable set register.                             */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Interrupt enable clear register.                           */
  __IM  uint32_t  RESERVED4[126];
  __IOM uint32_t  MODE;                         /*!< (@ 0x00000504) Timer Mode selection.                                      */
  __IOM uint32_t  BITMODE;                      /*!< (@ 0x00000508) Sets timer behaviour.                                      */
  __IM  uint32_t  RESERVED5;
  __IOM uint32_t  PRESCALER;                    /*!< (@ 0x00000510) 4-bit prescaler to source clock frequency (max
                                                                    value 9). Source clock frequency is divided
                                                                    by 2^SCALE.                                                */
  __IM  uint32_t  RESERVED6[11];
  __IOM uint32_t  CC[4];                        /*!< (@ 0x00000540) Capture/compare registers.                                 */
  __IM  uint32_t  RESERVED7[683];
  __IOM uint32_t  POWER;                        /*!< (@ 0x00000FFC) Peripheral power control.                                  */
} NRF_TIMER_Type;                               /*!< Size = 4096 (0x1000)                                                      */



/* =========================================================================================================================== */
/* ================                                           RTC0                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Real time counter 0. (RTC0)
  */

typedef struct {                                /*!< (@ 0x4000B000) RTC0 Structure                                             */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start RTC Counter.                                         */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stop RTC Counter.                                          */
  __OM  uint32_t  TASKS_CLEAR;                  /*!< (@ 0x00000008) Clear RTC Counter.                                         */
  __OM  uint32_t  TASKS_TRIGOVRFLW;             /*!< (@ 0x0000000C) Set COUNTER to 0xFFFFFFF0.                                 */
  __IM  uint32_t  RESERVED[60];
  __IOM uint32_t  EVENTS_TICK;                  /*!< (@ 0x00000100) Event on COUNTER increment.                                */
  __IOM uint32_t  EVENTS_OVRFLW;                /*!< (@ 0x00000104) Event on COUNTER overflow.                                 */
  __IM  uint32_t  RESERVED1[14];
  __IOM uint32_t  EVENTS_COMPARE[4];            /*!< (@ 0x00000140) Compare event on CC[n] match.                              */
  __IM  uint32_t  RESERVED2[109];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Interrupt enable set register.                             */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Interrupt enable clear register.                           */
  __IM  uint32_t  RESERVED3[13];
  __IOM uint32_t  EVTEN;                        /*!< (@ 0x00000340) Configures event enable routing to PPI for each
                                                                    RTC event.                                                 */
  __IOM uint32_t  EVTENSET;                     /*!< (@ 0x00000344) Enable events routing to PPI. The reading of
                                                                    this register gives the value of EVTEN.                    */
  __IOM uint32_t  EVTENCLR;                     /*!< (@ 0x00000348) Disable events routing to PPI. The reading of
                                                                    this register gives the value of EVTEN.                    */
  __IM  uint32_t  RESERVED4[110];
  __IM  uint32_t  COUNTER;                      /*!< (@ 0x00000504) Current COUNTER value.                                     */
  __IOM uint32_t  PRESCALER;                    /*!< (@ 0x00000508) 12-bit prescaler for COUNTER frequency (32768/(PRESCALER+1)).
                                                                    Must be written when RTC is STOPed.                        */
  __IM  uint32_t  RESERVED5[13];
  __IOM uint32_t  CC[4];                        /*!< (@ 0x00000540) Capture/compare registers.                                 */
  __IM  uint32_t  RESERVED6[683];
  __IOM uint32_t  POWER;                        /*!< (@ 0x00000FFC) Peripheral power control.                                  */
} NRF_RTC_Type;                                 /*!< Size = 4096 (0x1000)                                                      */



/* =========================================================================================================================== */
/* ================                                           TEMP                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Temperature Sensor. (TEMP)
  */

typedef struct {                                /*!< (@ 0x4000C000) TEMP Structure                                             */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start temperature measurement.                             */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stop temperature measurement.                              */
  __IM  uint32_t  RESERVED[62];
  __IOM uint32_t  EVENTS_DATARDY;               /*!< (@ 0x00000100) Temperature measurement complete, data ready
                                                                    event.                                                     */
  __IM  uint32_t  RESERVED1[128];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Interrupt enable set register.                             */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Interrupt enable clear register.                           */
  __IM  uint32_t  RESERVED2[127];
  __IM  int32_t   TEMP;                         /*!< (@ 0x00000508) Die temperature in degC, 2's complement format,
                                                                    0.25 degC pecision.                                        */
  __IM  uint32_t  RESERVED3[700];
  __IOM uint32_t  POWER;                        /*!< (@ 0x00000FFC) Peripheral power control.                                  */
} NRF_TEMP_Type;                                /*!< Size = 4096 (0x1000)                                                      */



/* =========================================================================================================================== */
/* ================                                            RNG                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Random Number Generator. (RNG)
  */

typedef struct {                                /*!< (@ 0x4000D000) RNG Structure                                              */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start the random number generator.                         */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stop the random number generator.                          */
  __IM  uint32_t  RESERVED[62];
  __IOM uint32_t  EVENTS_VALRDY;                /*!< (@ 0x00000100) New random number generated and written to VALUE
                                                                    register.                                                  */
  __IM  uint32_t  RESERVED1[63];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts for the RNG.                                     */
  __IM  uint32_t  RESERVED2[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Interrupt enable set register                              */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Interrupt enable clear register                            */
  __IM  uint32_t  RESERVED3[126];
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x00000504) Configuration register.                                    */
  __IM  uint32_t  VALUE;                        /*!< (@ 0x00000508) RNG random number.                                         */
  __IM  uint32_t  RESERVED4[700];
  __IOM uint32_t  POWER;                        /*!< (@ 0x00000FFC) Peripheral power control.                                  */
} NRF_RNG_Type;                                 /*!< Size = 4096 (0x1000)                                                      */



/* =========================================================================================================================== */
/* ================                                            ECB                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief AES ECB Mode Encryption. (ECB)
  */

typedef struct {                                /*!< (@ 0x4000E000) ECB Structure                                              */
  __OM  uint32_t  TASKS_STARTECB;               /*!< (@ 0x00000000) Start ECB block encrypt. If a crypto operation
                                                                    is running, this will not initiate a new
                                                                    encryption and the ERRORECB event will be
                                                                    triggered.                                                 */
  __OM  uint32_t  TASKS_STOPECB;                /*!< (@ 0x00000004) Stop current ECB encryption. If a crypto operation
                                                                    is running, this will will trigger the ERRORECB
                                                                    event.                                                     */
  __IM  uint32_t  RESERVED[62];
  __IOM uint32_t  EVENTS_ENDECB;                /*!< (@ 0x00000100) ECB block encrypt complete.                                */
  __IOM uint32_t  EVENTS_ERRORECB;              /*!< (@ 0x00000104) ECB block encrypt aborted due to a STOPECB task
                                                                    or due to an error.                                        */
  __IM  uint32_t  RESERVED1[127];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Interrupt enable set register.                             */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Interrupt enable clear register.                           */
  __IM  uint32_t  RESERVED2[126];
  __IOM uint32_t  ECBDATAPTR;                   /*!< (@ 0x00000504) ECB block encrypt memory pointer.                          */
  __IM  uint32_t  RESERVED3[701];
  __IOM uint32_t  POWER;                        /*!< (@ 0x00000FFC) Peripheral power control.                                  */
} NRF_ECB_Type;                                 /*!< Size = 4096 (0x1000)                                                      */



/* =========================================================================================================================== */
/* ================                                            AAR                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Accelerated Address Resolver. (AAR)
  */

typedef struct {                                /*!< (@ 0x4000F000) AAR Structure                                              */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start resolving addresses based on IRKs specified
                                                                    in the IRK data structure.                                 */
  __IM  uint32_t  RESERVED;
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000008) Stop resolving addresses.                                  */
  __IM  uint32_t  RESERVED1[61];
  __IOM uint32_t  EVENTS_END;                   /*!< (@ 0x00000100) Address resolution procedure completed.                    */
  __IOM uint32_t  EVENTS_RESOLVED;              /*!< (@ 0x00000104) Address resolved.                                          */
  __IOM uint32_t  EVENTS_NOTRESOLVED;           /*!< (@ 0x00000108) Address not resolved.                                      */
  __IM  uint32_t  RESERVED2[126];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Interrupt enable set register.                             */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Interrupt enable clear register.                           */
  __IM  uint32_t  RESERVED3[61];
  __IM  uint32_t  STATUS;                       /*!< (@ 0x00000400) Resolution status.                                         */
  __IM  uint32_t  RESERVED4[63];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable AAR.                                                */
  __IOM uint32_t  NIRK;                         /*!< (@ 0x00000504) Number of Identity root Keys in the IRK data
                                                                    structure.                                                 */
  __IOM uint32_t  IRKPTR;                       /*!< (@ 0x00000508) Pointer to the IRK data structure.                         */
  __IM  uint32_t  RESERVED5;
  __IOM uint32_t  ADDRPTR;                      /*!< (@ 0x00000510) Pointer to the resolvable address (6 bytes).               */
  __IOM uint32_t  SCRATCHPTR;                   /*!< (@ 0x00000514) Pointer to a scratch data area used for temporary
                                                                    storage during resolution. A minimum of
                                                                    3 bytes must be reserved.                                  */
  __IM  uint32_t  RESERVED6[697];
  __IOM uint32_t  POWER;                        /*!< (@ 0x00000FFC) Peripheral power control.                                  */
} NRF_AAR_Type;                                 /*!< Size = 4096 (0x1000)                                                      */



/* =========================================================================================================================== */
/* ================                                            CCM                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief AES CCM Mode Encryption. (CCM)
  */

typedef struct {                                /*!< (@ 0x4000F000) CCM Structure                                              */
  __OM  uint32_t  TASKS_KSGEN;                  /*!< (@ 0x00000000) Start generation of key-stream. This operation
                                                                    will stop by itself when completed.                        */
  __OM  uint32_t  TASKS_CRYPT;                  /*!< (@ 0x00000004) Start encrypt/decrypt. This operation will stop
                                                                    by itself when completed.                                  */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000008) Stop encrypt/decrypt.                                      */
  __IM  uint32_t  RESERVED[61];
  __IOM uint32_t  EVENTS_ENDKSGEN;              /*!< (@ 0x00000100) Keystream generation completed.                            */
  __IOM uint32_t  EVENTS_ENDCRYPT;              /*!< (@ 0x00000104) Encrypt/decrypt completed.                                 */
  __IOM uint32_t  EVENTS_ERROR;                 /*!< (@ 0x00000108) Error happened.                                            */
  __IM  uint32_t  RESERVED1[61];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts for the CCM.                                     */
  __IM  uint32_t  RESERVED2[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Interrupt enable set register.                             */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Interrupt enable clear register.                           */
  __IM  uint32_t  RESERVED3[61];
  __IM  uint32_t  MICSTATUS;                    /*!< (@ 0x00000400) CCM RX MIC check result.                                   */
  __IM  uint32_t  RESERVED4[63];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) CCM enable.                                                */
  __IOM uint32_t  MODE;                         /*!< (@ 0x00000504) Operation mode.                                            */
  __IOM uint32_t  CNFPTR;                       /*!< (@ 0x00000508) Pointer to a data structure holding AES key and
                                                                    NONCE vector.                                              */
  __IOM uint32_t  INPTR;                        /*!< (@ 0x0000050C) Pointer to the input packet.                               */
  __IOM uint32_t  OUTPTR;                       /*!< (@ 0x00000510) Pointer to the output packet.                              */
  __IOM uint32_t  SCRATCHPTR;                   /*!< (@ 0x00000514) Pointer to a scratch data area used for temporary
                                                                    storage during resolution. A minimum of
                                                                    43 bytes must be reserved.                                 */
  __IM  uint32_t  RESERVED5[697];
  __IOM uint32_t  POWER;                        /*!< (@ 0x00000FFC) Peripheral power control.                                  */
} NRF_CCM_Type;                                 /*!< Size = 4096 (0x1000)                                                      */



/* =========================================================================================================================== */
/* ================                                            WDT                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Watchdog Timer. (WDT)
  */

typedef struct {                                /*!< (@ 0x40010000) WDT Structure                                              */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start the watchdog.                                        */
  __IM  uint32_t  RESERVED[63];
  __IOM uint32_t  EVENTS_TIMEOUT;               /*!< (@ 0x00000100) Watchdog timeout.                                          */
  __IM  uint32_t  RESERVED1[128];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Interrupt enable set register.                             */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Interrupt enable clear register.                           */
  __IM  uint32_t  RESERVED2[61];
  __IM  uint32_t  RUNSTATUS;                    /*!< (@ 0x00000400) Watchdog running status.                                   */
  __IM  uint32_t  REQSTATUS;                    /*!< (@ 0x00000404) Request status.                                            */
  __IM  uint32_t  RESERVED3[63];
  __IOM uint32_t  CRV;                          /*!< (@ 0x00000504) Counter reload value in number of 32kiHz clock
                                                                    cycles.                                                    */
  __IOM uint32_t  RREN;                         /*!< (@ 0x00000508) Reload request enable.                                     */
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x0000050C) Configuration register.                                    */
  __IM  uint32_t  RESERVED4[60];
  __OM  uint32_t  RR[8];                        /*!< (@ 0x00000600) Reload requests registers.                                 */
  __IM  uint32_t  RESERVED5[631];
  __IOM uint32_t  POWER;                        /*!< (@ 0x00000FFC) Peripheral power control.                                  */
} NRF_WDT_Type;                                 /*!< Size = 4096 (0x1000)                                                      */



/* =========================================================================================================================== */
/* ================                                           QDEC                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Rotary decoder. (QDEC)
  */

typedef struct {                                /*!< (@ 0x40012000) QDEC Structure                                             */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start the quadrature decoder.                              */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stop the quadrature decoder.                               */
  __OM  uint32_t  TASKS_READCLRACC;             /*!< (@ 0x00000008) Transfers the content from ACC registers to ACCREAD
                                                                    registers, and clears the ACC registers.                   */
  __IM  uint32_t  RESERVED[61];
  __IOM uint32_t  EVENTS_SAMPLERDY;             /*!< (@ 0x00000100) A new sample is written to the sample register.            */
  __IOM uint32_t  EVENTS_REPORTRDY;             /*!< (@ 0x00000104) REPORTPER number of samples accumulated in ACC
                                                                    register, and ACC register different than
                                                                    zero.                                                      */
  __IOM uint32_t  EVENTS_ACCOF;                 /*!< (@ 0x00000108) ACC or ACCDBL register overflow.                           */
  __IM  uint32_t  RESERVED1[61];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts for the QDEC.                                    */
  __IM  uint32_t  RESERVED2[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Interrupt enable set register.                             */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Interrupt enable clear register.                           */
  __IM  uint32_t  RESERVED3[125];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable the QDEC.                                           */
  __IOM uint32_t  LEDPOL;                       /*!< (@ 0x00000504) LED output pin polarity.                                   */
  __IOM uint32_t  SAMPLEPER;                    /*!< (@ 0x00000508) Sample period.                                             */
  __IM  int32_t   SAMPLE;                       /*!< (@ 0x0000050C) Motion sample value.                                       */
  __IOM uint32_t  REPORTPER;                    /*!< (@ 0x00000510) Number of samples to generate an EVENT_REPORTRDY.          */
  __IM  int32_t   ACC;                          /*!< (@ 0x00000514) Accumulated valid transitions register.                    */
  __IM  int32_t   ACCREAD;                      /*!< (@ 0x00000518) Snapshot of ACC register. Value generated by
                                                                    the TASKS_READCLEACC task.                                 */
  __IOM uint32_t  PSELLED;                      /*!< (@ 0x0000051C) Pin select for LED output.                                 */
  __IOM uint32_t  PSELA;                        /*!< (@ 0x00000520) Pin select for phase A input.                              */
  __IOM uint32_t  PSELB;                        /*!< (@ 0x00000524) Pin select for phase B input.                              */
  __IOM uint32_t  DBFEN;                        /*!< (@ 0x00000528) Enable debouncer input filters.                            */
  __IM  uint32_t  RESERVED4[5];
  __IOM uint32_t  LEDPRE;                       /*!< (@ 0x00000540) Time LED is switched ON before the sample.                 */
  __IM  uint32_t  ACCDBL;                       /*!< (@ 0x00000544) Accumulated double (error) transitions register.           */
  __IM  uint32_t  ACCDBLREAD;                   /*!< (@ 0x00000548) Snapshot of ACCDBL register. Value generated
                                                                    by the TASKS_READCLEACC task.                              */
  __IM  uint32_t  RESERVED5[684];
  __IOM uint32_t  POWER;                        /*!< (@ 0x00000FFC) Peripheral power control.                                  */
} NRF_QDEC_Type;                                /*!< Size = 4096 (0x1000)                                                      */



/* =========================================================================================================================== */
/* ================                                          LPCOMP                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Low power comparator. (LPCOMP)
  */

typedef struct {                                /*!< (@ 0x40013000) LPCOMP Structure                                           */
  __OM  uint32_t  TASKS_START;                  /*!< (@ 0x00000000) Start the comparator.                                      */
  __OM  uint32_t  TASKS_STOP;                   /*!< (@ 0x00000004) Stop the comparator.                                       */
  __OM  uint32_t  TASKS_SAMPLE;                 /*!< (@ 0x00000008) Sample comparator value.                                   */
  __IM  uint32_t  RESERVED[61];
  __IOM uint32_t  EVENTS_READY;                 /*!< (@ 0x00000100) LPCOMP is ready and output is valid.                       */
  __IOM uint32_t  EVENTS_DOWN;                  /*!< (@ 0x00000104) Input voltage crossed the threshold going down.            */
  __IOM uint32_t  EVENTS_UP;                    /*!< (@ 0x00000108) Input voltage crossed the threshold going up.              */
  __IOM uint32_t  EVENTS_CROSS;                 /*!< (@ 0x0000010C) Input voltage crossed the threshold in any direction.      */
  __IM  uint32_t  RESERVED1[60];
  __IOM uint32_t  SHORTS;                       /*!< (@ 0x00000200) Shortcuts for the LPCOMP.                                  */
  __IM  uint32_t  RESERVED2[64];
  __IOM uint32_t  INTENSET;                     /*!< (@ 0x00000304) Interrupt enable set register.                             */
  __IOM uint32_t  INTENCLR;                     /*!< (@ 0x00000308) Interrupt enable clear register.                           */
  __IM  uint32_t  RESERVED3[61];
  __IM  uint32_t  RESULT;                       /*!< (@ 0x00000400) Result of last compare.                                    */
  __IM  uint32_t  RESERVED4[63];
  __IOM uint32_t  ENABLE;                       /*!< (@ 0x00000500) Enable the LPCOMP.                                         */
  __IOM uint32_t  PSEL;                         /*!< (@ 0x00000504) Input pin select.                                          */
  __IOM uint32_t  REFSEL;                       /*!< (@ 0x00000508) Reference select.                                          */
  __IOM uint32_t  EXTREFSEL;                    /*!< (@ 0x0000050C) External reference select.                                 */
  __IM  uint32_t  RESERVED5[4];
  __IOM uint32_t  ANADETECT;                    /*!< (@ 0x00000520) Analog detect configuration.                               */
  __IM  uint32_t  RESERVED6[694];
  __IOM uint32_t  POWER;                        /*!< (@ 0x00000FFC) Peripheral power control.                                  */
} NRF_LPCOMP_Type;                              /*!< Size = 4096 (0x1000)                                                      */



/* =========================================================================================================================== */
/* ================                                            SWI                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief SW Interrupts. (SWI)
  */

typedef struct {                                /*!< (@ 0x40014000) SWI Structure                                              */
  __IM  uint32_t  UNUSED;                       /*!< (@ 0x00000000) Unused.                                                    */
} NRF_SWI_Type;                                 /*!< Size = 4 (0x4)                                                            */



/* =========================================================================================================================== */
/* ================                                           NVMC                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Non Volatile Memory Controller. (NVMC)
  */

typedef struct {                                /*!< (@ 0x4001E000) NVMC Structure                                             */
  __IM  uint32_t  RESERVED[256];
  __IM  uint32_t  READY;                        /*!< (@ 0x00000400) Ready flag.                                                */
  __IM  uint32_t  RESERVED1[64];
  __IOM uint32_t  CONFIG;                       /*!< (@ 0x00000504) Configuration register.                                    */
  
  union {
    __IOM uint32_t ERASEPAGE;                   /*!< (@ 0x00000508) Register for erasing a non-protected non-volatile
                                                                    memory page.                                               */
    __IOM uint32_t ERASEPCR1;                   /*!< (@ 0x00000508) Register for erasing a non-protected non-volatile
                                                                    memory page.                                               */
  };
  __IOM uint32_t  ERASEALL;                     /*!< (@ 0x0000050C) Register for erasing all non-volatile user memory.         */
  __IOM uint32_t  ERASEPCR0;                    /*!< (@ 0x00000510) Register for erasing a protected non-volatile
                                                                    memory page.                                               */
  __IOM uint32_t  ERASEUICR;                    /*!< (@ 0x00000514) Register for start erasing User Information Congfiguration
                                                                    Registers.                                                 */
} NRF_NVMC_Type;                                /*!< Size = 1304 (0x518)                                                       */



/* =========================================================================================================================== */
/* ================                                            PPI                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief PPI controller. (PPI)
  */

typedef struct {                                /*!< (@ 0x4001F000) PPI Structure                                              */
  __IOM PPI_TASKS_CHG_Type TASKS_CHG[4];        /*!< (@ 0x00000000) Channel group tasks.                                       */
  __IM  uint32_t  RESERVED[312];
  __IOM uint32_t  CHEN;                         /*!< (@ 0x00000500) Channel enable.                                            */
  __IOM uint32_t  CHENSET;                      /*!< (@ 0x00000504) Channel enable set.                                        */
  __IOM uint32_t  CHENCLR;                      /*!< (@ 0x00000508) Channel enable clear.                                      */
  __IM  uint32_t  RESERVED1;
  __IOM PPI_CH_Type CH[16];                     /*!< (@ 0x00000510) PPI Channel.                                               */
  __IM  uint32_t  RESERVED2[156];
  __IOM uint32_t  CHG[4];                       /*!< (@ 0x00000800) Channel group configuration.                               */
} NRF_PPI_Type;                                 /*!< Size = 2064 (0x810)                                                       */



/* =========================================================================================================================== */
/* ================                                           FICR                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Factory Information Configuration. (FICR)
  */

typedef struct {                                /*!< (@ 0x10000000) FICR Structure                                             */
  __IM  uint32_t  RESERVED[4];
  __IM  uint32_t  CODEPAGESIZE;                 /*!< (@ 0x00000010) Code memory page size in bytes.                            */
  __IM  uint32_t  CODESIZE;                     /*!< (@ 0x00000014) Code memory size in pages.                                 */
  __IM  uint32_t  RESERVED1[4];
  __IM  uint32_t  CLENR0;                       /*!< (@ 0x00000028) Length of code region 0 in bytes.                          */
  __IM  uint32_t  PPFC;                         /*!< (@ 0x0000002C) Pre-programmed factory code present.                       */
  __IM  uint32_t  RESERVED2;
  __IM  uint32_t  NUMRAMBLOCK;                  /*!< (@ 0x00000034) Number of individualy controllable RAM blocks.             */
  
  union {
    __IM  uint32_t SIZERAMBLOCKS;               /*!< (@ 0x00000038) Size of RAM blocks in bytes.                               */
    __IM  uint32_t SIZERAMBLOCK[4];             /*!< (@ 0x00000038) Deprecated array of size of RAM block in bytes.
                                                                    This name is kept for backward compatinility
                                                                    purposes. Use SIZERAMBLOCKS instead.                       */
  };
  __IM  uint32_t  RESERVED3[5];
  __IM  uint32_t  CONFIGID;                     /*!< (@ 0x0000005C) Configuration identifier.                                  */
  __IM  uint32_t  DEVICEID[2];                  /*!< (@ 0x00000060) Device identifier.                                         */
  __IM  uint32_t  RESERVED4[6];
  __IM  uint32_t  ER[4];                        /*!< (@ 0x00000080) Encryption root.                                           */
  __IM  uint32_t  IR[4];                        /*!< (@ 0x00000090) Identity root.                                             */
  __IM  uint32_t  DEVICEADDRTYPE;               /*!< (@ 0x000000A0) Device address type.                                       */
  __IM  uint32_t  DEVICEADDR[2];                /*!< (@ 0x000000A4) Device address.                                            */
  __IM  uint32_t  OVERRIDEEN;                   /*!< (@ 0x000000AC) Radio calibration override enable.                         */
  __IM  uint32_t  NRF_1MBIT[5];                 /*!< (@ 0x000000B0) Override values for the OVERRIDEn registers in
                                                                    RADIO for NRF_1Mbit mode.                                  */
  __IM  uint32_t  RESERVED5[10];
  __IM  uint32_t  BLE_1MBIT[5];                 /*!< (@ 0x000000EC) Override values for the OVERRIDEn registers in
                                                                    RADIO for BLE_1Mbit mode.                                  */
} NRF_FICR_Type;                                /*!< Size = 256 (0x100)                                                        */



/* =========================================================================================================================== */
/* ================                                           UICR                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief User Information Configuration. (UICR)
  */

typedef struct {                                /*!< (@ 0x10001000) UICR Structure                                             */
  __IOM uint32_t  CLENR0;                       /*!< (@ 0x00000000) Length of code region 0.                                   */
  __IOM uint32_t  RBPCONF;                      /*!< (@ 0x00000004) Readback protection configuration.                         */
  __IOM uint32_t  XTALFREQ;                     /*!< (@ 0x00000008) Reset value for CLOCK XTALFREQ register.                   */
  __IM  uint32_t  RESERVED;
  __IM  uint32_t  FWID;                         /*!< (@ 0x00000010) Firmware ID.                                               */
  
  union {
    __IOM uint32_t BOOTLOADERADDR;              /*!< (@ 0x00000014) Bootloader start address.                                  */
    __IOM uint32_t NRFFW[15];                   /*!< (@ 0x00000014) Reserved for Nordic firmware design.                       */
  };
  __IOM uint32_t  NRFHW[12];                    /*!< (@ 0x00000050) Reserved for Nordic hardware design.                       */
  __IOM uint32_t  CUSTOMER[32];                 /*!< (@ 0x00000080) Reserved for customer.                                     */
} NRF_UICR_Type;                                /*!< Size = 256 (0x100)                                                        */



/* =========================================================================================================================== */
/* ================                                           GPIO                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief General purpose input and output. (GPIO)
  */

typedef struct {                                /*!< (@ 0x50000000) GPIO Structure                                             */
  __IM  uint32_t  RESERVED[321];
  __IOM uint32_t  OUT;                          /*!< (@ 0x00000504) Write GPIO port.                                           */
  __IOM uint32_t  OUTSET;                       /*!< (@ 0x00000508) Set individual bits in GPIO port.                          */
  __IOM uint32_t  OUTCLR;                       /*!< (@ 0x0000050C) Clear individual bits in GPIO port.                        */
  __IM  uint32_t  IN;                           /*!< (@ 0x00000510) Read GPIO port.                                            */
  __IOM uint32_t  DIR;                          /*!< (@ 0x00000514) Direction of GPIO pins.                                    */
  __IOM uint32_t  DIRSET;                       /*!< (@ 0x00000518) DIR set register.                                          */
  __IOM uint32_t  DIRCLR;                       /*!< (@ 0x0000051C) DIR clear register.                                        */
  __IM  uint32_t  RESERVED1[120];
  __IOM uint32_t  PIN_CNF[32];                  /*!< (@ 0x00000700) Configuration of GPIO pins.                                */
} NRF_GPIO_Type;                                /*!< Size = 1920 (0x780)                                                       */


/** @} */ /* End of group Device_Peripheral_peripherals */


/* =========================================================================================================================== */
/* ================                          Device Specific Peripheral Address Map                           ================ */
/* =========================================================================================================================== */


/** @addtogroup Device_Peripheral_peripheralAddr
  * @{
  */

#define NRF_POWER_BASE              0x40000000UL
#define NRF_CLOCK_BASE              0x40000000UL
#define NRF_MPU_BASE                0x40000000UL
#define NRF_RADIO_BASE              0x40001000UL
#define NRF_UART0_BASE              0x40002000UL
#define NRF_SPI0_BASE               0x40003000UL
#define NRF_TWI0_BASE               0x40003000UL
#define NRF_SPI1_BASE               0x40004000UL
#define NRF_TWI1_BASE               0x40004000UL
#define NRF_SPIS1_BASE              0x40004000UL
#define NRF_GPIOTE_BASE             0x40006000UL
#define NRF_ADC_BASE                0x40007000UL
#define NRF_TIMER0_BASE             0x40008000UL
#define NRF_TIMER1_BASE             0x40009000UL
#define NRF_TIMER2_BASE             0x4000A000UL
#define NRF_RTC0_BASE               0x4000B000UL
#define NRF_TEMP_BASE               0x4000C000UL
#define NRF_RNG_BASE                0x4000D000UL
#define NRF_ECB_BASE                0x4000E000UL
#define NRF_AAR_BASE                0x4000F000UL
#define NRF_CCM_BASE                0x4000F000UL
#define NRF_WDT_BASE                0x40010000UL
#define NRF_RTC1_BASE               0x40011000UL
#define NRF_QDEC_BASE               0x40012000UL
#define NRF_LPCOMP_BASE             0x40013000UL
#define NRF_SWI_BASE                0x40014000UL
#define NRF_NVMC_BASE               0x4001E000UL
#define NRF_PPI_BASE                0x4001F000UL
#define NRF_FICR_BASE               0x10000000UL
#define NRF_UICR_BASE               0x10001000UL
#define NRF_GPIO_BASE               0x50000000UL

/** @} */ /* End of group Device_Peripheral_peripheralAddr */


/* =========================================================================================================================== */
/* ================                                  Peripheral declaration                                   ================ */
/* =========================================================================================================================== */


/** @addtogroup Device_Peripheral_declaration
  * @{
  */

#define NRF_POWER                   ((NRF_POWER_Type*)         NRF_POWER_BASE)
#define NRF_CLOCK                   ((NRF_CLOCK_Type*)         NRF_CLOCK_BASE)
#define NRF_MPU                     ((NRF_MPU_Type*)           NRF_MPU_BASE)
#define NRF_RADIO                   ((NRF_RADIO_Type*)         NRF_RADIO_BASE)
#define NRF_UART0                   ((NRF_UART_Type*)          NRF_UART0_BASE)
#define NRF_SPI0                    ((NRF_SPI_Type*)           NRF_SPI0_BASE)
#define NRF_TWI0                    ((NRF_TWI_Type*)           NRF_TWI0_BASE)
#define NRF_SPI1                    ((NRF_SPI_Type*)           NRF_SPI1_BASE)
#define NRF_TWI1                    ((NRF_TWI_Type*)           NRF_TWI1_BASE)
#define NRF_SPIS1                   ((NRF_SPIS_Type*)          NRF_SPIS1_BASE)
#define NRF_GPIOTE                  ((NRF_GPIOTE_Type*)        NRF_GPIOTE_BASE)
#define NRF_ADC                     ((NRF_ADC_Type*)           NRF_ADC_BASE)
#define NRF_TIMER0                  ((NRF_TIMER_Type*)         NRF_TIMER0_BASE)
#define NRF_TIMER1                  ((NRF_TIMER_Type*)         NRF_TIMER1_BASE)
#define NRF_TIMER2                  ((NRF_TIMER_Type*)         NRF_TIMER2_BASE)
#define NRF_RTC0                    ((NRF_RTC_Type*)           NRF_RTC0_BASE)
#define NRF_TEMP                    ((NRF_TEMP_Type*)          NRF_TEMP_BASE)
#define NRF_RNG                     ((NRF_RNG_Type*)           NRF_RNG_BASE)
#define NRF_ECB                     ((NRF_ECB_Type*)           NRF_ECB_BASE)
#define NRF_AAR                     ((NRF_AAR_Type*)           NRF_AAR_BASE)
#define NRF_CCM                     ((NRF_CCM_Type*)           NRF_CCM_BASE)
#define NRF_WDT                     ((NRF_WDT_Type*)           NRF_WDT_BASE)
#define NRF_RTC1                    ((NRF_RTC_Type*)           NRF_RTC1_BASE)
#define NRF_QDEC                    ((NRF_QDEC_Type*)          NRF_QDEC_BASE)
#define NRF_LPCOMP                  ((NRF_LPCOMP_Type*)        NRF_LPCOMP_BASE)
#define NRF_SWI                     ((NRF_SWI_Type*)           NRF_SWI_BASE)
#define NRF_NVMC                    ((NRF_NVMC_Type*)          NRF_NVMC_BASE)
#define NRF_PPI                     ((NRF_PPI_Type*)           NRF_PPI_BASE)
#define NRF_FICR                    ((NRF_FICR_Type*)          NRF_FICR_BASE)
#define NRF_UICR                    ((NRF_UICR_Type*)          NRF_UICR_BASE)
#define NRF_GPIO                    ((NRF_GPIO_Type*)          NRF_GPIO_BASE)

/** @} */ /* End of group Device_Peripheral_declaration */


/* =========================================  End of section using anonymous unions  ========================================= */
#if defined (__CC_ARM)
  #pragma pop
#elif defined (__ICCARM__)
  /* leave anonymous unions enabled */
#elif defined(__ARMCC_VERSION) && (__ARMCC_VERSION >= 6010050)
  #pragma clang diagnostic pop
#elif defined (__GNUC__)
  /* anonymous unions are enabled by default */
#elif defined (__TMS470__)
  /* anonymous unions are enabled by default */
#elif defined (__TASKING__)
  #pragma warning restore
#elif defined (__CSMC__)
  /* anonymous unions are enabled by default */
#endif


#ifdef __cplusplus
}
#endif

#endif /* NRF51_H */


/** @} */ /* End of group nrf51 */

/** @} */ /* End of group Nordic Semiconductor */
