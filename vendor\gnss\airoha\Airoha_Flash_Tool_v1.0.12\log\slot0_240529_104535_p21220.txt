time=05/29 10:45:35.854, level=INFO, class = LogService, tool_version = 0.2.12/air_bta_ic_premium_g3
time=05/29 10:45:35.851, level=INFO, class = Controller, SetLogLevel = 3
time=05/29 10:45:35.851, level=DEBUG, class = ToolLogLevel, log_level = 3
time=05/29 10:45:35.851, level=DEBUG, class = ToolLogLevel, task_time = 0.000
time=05/29 10:45:35.851, level=DEBUG, class = ConnectDUT, state = init data, result = ok
time=05/29 10:45:35.851, level=DEBUG, class = SerialHost, phy_name = UART, trans_name = h4
time=05/29 10:45:35.851, level=DEBUG, class = Physical, type = 1, state = create
time=05/29 10:45:35.851, level=DEBUG, class = Transport, type = 4, state = create
time=05/29 10:45:35.852, level=INFO, class = UartPhy, desc = USB Serial Port (COM4)
time=05/29 10:45:35.852, level=INFO, class = UartPhy, instance_id = FTDIBUS\VID_0403+PID_6015+DQ014GZWA\0000
time=05/29 10:45:35.852, level=INFO, class = UartPhy, port = 4, baud = 3000000, handshake = 0, read/timeout = (3, 0)
time=05/29 10:45:35.894, level=DEBUG, class = SerialHost, state = open
time=05/29 10:45:35.894, level=DEBUG, class = UartDev, state = connect, ret = 0
time=05/29 10:45:35.894, level=DEBUG, class = ConnectDUT, state = connect, device_name = DUT, device_type = UART, result = ok
time=05/29 10:45:35.894, level=DEBUG, class = ConnectDUT, task_time = 0.042
time=05/29 10:45:35.894, level=DEBUG, class = CallbackManager, register = DUT, filter = 0, cb = 0x005A2C00
time=05/29 10:45:35.894, level=DEBUG, class = UartDev, SetBaudrate = 115200
time=05/29 10:45:35.894, level=DEBUG, class = Host, SwitchTransport = bypass
time=05/29 10:45:35.894, level=DEBUG, class = Transport, type = 1, state = create
time=05/29 10:45:35.894, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=05/29 10:45:35.896, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:35.896, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:35.913, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/29 10:45:35.931, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/29 10:45:35.931, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=05/29 10:45:35.931, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:35.931, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:35.949, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=05/29 10:45:35.966, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/29 10:45:35.966, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=05/29 10:45:35.966, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:35.966, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:35.983, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/29 10:45:36.001, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/29 10:45:36.001, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=05/29 10:45:36.001, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.001, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.019, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/29 10:45:36.036, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/29 10:45:36.036, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=05/29 10:45:36.036, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.036, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.054, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/29 10:45:36.071, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/29 10:45:36.071, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=05/29 10:45:36.072, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.072, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.092, level=WARN, class = Host, msg = Match timeout 20 ms(0)
time=05/29 10:45:36.123, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:36.123, level=WARN, class = BtromHandshake, timeout = 51, message = response timeout
time=05/29 10:45:36.124, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.124, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.155, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:36.155, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:36.155, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.155, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.186, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:36.186, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:36.186, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.186, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.217, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:36.217, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:36.217, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.217, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.248, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:36.248, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:36.248, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.248, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.278, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:36.278, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:36.278, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.278, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.309, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:36.309, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:36.309, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.309, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.340, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:36.340, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:36.340, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.340, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.371, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:36.371, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:36.372, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.372, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.402, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:36.402, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:36.403, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.403, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.434, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:36.434, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:36.435, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.435, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.465, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:36.465, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:36.466, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.466, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.496, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:36.496, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:36.497, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.497, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.527, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:36.527, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:36.528, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.528, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.558, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:36.558, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:36.558, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.558, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.590, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:36.590, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:36.590, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.590, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.622, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:36.622, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:36.622, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.622, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.652, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:36.652, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:36.652, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.652, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.683, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:36.683, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:36.684, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.684, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.714, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 10:45:36.714, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:36.714, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.714, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.745, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:36.745, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:36.745, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.745, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.776, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:36.776, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:36.777, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.777, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.807, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:36.807, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:36.807, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.807, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.839, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:36.839, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:36.839, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.839, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.870, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:36.870, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:36.871, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.871, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.901, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:36.901, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:36.901, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.901, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.933, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:36.933, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:36.933, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.933, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.963, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:36.963, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:36.964, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.964, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:36.995, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:36.995, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:36.995, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:36.995, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.026, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:37.026, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:37.026, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.026, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.058, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:37.058, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:37.058, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.059, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.090, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:37.090, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:37.090, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.090, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.120, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 10:45:37.151, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:37.151, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/29 10:45:37.151, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.151, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.182, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:37.182, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:37.183, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.183, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.214, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:37.214, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:37.214, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.214, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.244, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 10:45:37.274, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:37.275, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/29 10:45:37.275, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.275, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.306, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:37.306, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:37.306, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.306, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.337, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:37.337, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:37.337, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.337, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.368, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:37.368, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:37.369, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.369, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.400, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:37.400, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:37.400, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.400, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.431, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:37.431, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:37.431, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.431, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.462, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:37.462, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:37.463, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.463, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.494, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:37.494, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:37.494, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.494, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.525, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:37.525, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:37.525, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.525, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.556, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:37.556, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:37.556, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.556, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.588, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:37.588, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:37.588, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.588, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.619, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:37.619, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:37.619, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.619, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.650, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:37.650, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:37.650, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.650, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.681, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:37.681, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:37.681, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.681, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.713, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:37.713, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:37.713, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.713, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.744, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:37.744, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:37.744, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.744, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.775, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:37.775, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:37.775, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.775, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.806, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:37.806, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:37.806, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.806, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.837, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:37.837, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:37.837, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.837, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.867, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:37.867, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:37.868, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.868, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.898, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:37.898, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:37.899, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.899, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.930, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:37.930, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:37.930, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.930, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.960, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:37.960, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:37.961, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.961, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:37.992, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:37.992, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:37.992, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:37.992, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.023, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.023, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.024, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.024, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.055, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:38.055, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:38.055, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.055, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.086, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.086, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.086, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.086, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.117, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.117, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.117, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.117, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.148, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.148, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.149, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.149, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.179, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.179, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.180, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.180, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.210, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.210, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.211, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.211, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.242, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:38.242, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:38.243, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.243, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.273, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.273, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.273, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.273, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.304, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.304, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.304, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.304, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.335, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.335, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.335, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.335, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.366, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:38.366, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:38.366, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.366, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.397, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.397, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.398, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.398, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.428, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.428, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.428, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.428, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.460, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:38.460, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:38.460, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.460, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.490, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.490, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.491, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.491, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.521, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.521, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.522, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.522, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.553, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:38.553, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:38.553, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.553, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.584, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.584, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.585, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.585, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.615, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.615, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.616, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.616, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.647, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.647, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.647, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.647, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.678, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.678, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.678, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.678, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.708, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.708, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.708, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.708, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.740, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:38.740, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:38.740, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.740, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.771, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.771, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.771, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.771, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.802, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.802, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.802, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.802, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.833, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.833, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.833, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.833, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.865, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:38.865, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:38.865, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.865, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.895, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.895, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.895, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.895, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.926, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:38.926, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:38.927, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.927, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.958, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.958, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.958, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.958, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:38.988, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:38.988, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:38.989, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:38.989, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.020, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:39.020, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:39.020, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.020, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.051, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:39.051, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:39.051, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.051, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.082, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:39.082, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:39.082, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.082, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.113, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:39.113, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:39.114, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.114, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.144, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:39.144, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:39.144, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.144, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.175, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:39.175, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:39.175, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.175, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.206, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:39.206, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:39.206, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.206, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.237, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:39.237, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:39.237, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.237, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.268, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:39.268, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:39.268, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.268, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.300, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:39.300, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:39.300, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.300, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.330, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:39.330, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:39.330, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.330, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.361, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:39.362, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:39.362, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.362, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.393, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:39.393, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:39.393, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.393, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.425, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:39.425, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=05/29 10:45:39.457, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.457, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.486, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 10:45:39.517, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:39.517, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/29 10:45:39.518, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.518, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.549, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:39.549, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:39.549, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.549, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.579, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:39.579, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:39.580, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.580, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.611, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:39.611, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:39.611, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.611, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.643, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:39.643, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:39.643, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.643, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.674, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:39.674, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:39.674, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.674, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.705, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:39.705, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:39.705, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.705, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.736, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:39.736, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:39.737, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.737, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.768, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:39.768, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:39.768, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.768, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.799, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:39.799, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:39.799, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.799, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.829, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:39.829, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:39.830, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.830, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.861, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:39.861, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:39.861, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.861, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.893, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:39.893, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:39.893, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.893, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.924, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:39.924, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:39.924, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.924, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.955, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:39.955, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:39.956, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:39.956, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:39.985, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 10:45:40.016, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:40.017, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/29 10:45:40.017, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.017, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.047, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:40.047, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:40.047, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.047, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.078, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:40.078, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:40.079, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.079, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.109, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:40.109, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:40.110, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.110, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.140, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:40.140, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:40.141, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.141, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.171, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:40.171, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:40.171, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.171, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.202, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:40.202, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:40.202, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.202, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.233, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:40.233, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:40.233, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.233, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.265, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:40.265, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:40.265, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.265, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.296, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:40.296, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:40.296, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.296, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.327, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:40.327, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:40.327, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.327, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.359, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:40.359, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:40.359, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.359, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.390, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:40.390, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:40.390, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.390, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.420, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:40.420, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:40.420, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.420, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.452, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:40.452, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:40.452, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.452, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.483, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:40.483, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:40.484, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.484, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.515, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:40.515, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:40.515, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.515, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.545, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:40.545, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:40.546, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.546, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.577, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:40.577, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:40.577, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.577, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.608, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:40.608, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:40.608, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.608, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.638, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:40.638, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:40.639, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.639, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.670, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:40.670, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:40.670, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.670, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.701, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:40.701, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:40.701, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.701, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.732, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:40.732, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:40.732, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.732, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.763, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:40.764, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:40.764, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.764, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.795, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:40.795, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:40.795, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.795, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.825, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:40.825, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:40.825, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.825, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.857, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:40.857, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:40.857, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.857, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.888, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:40.888, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:40.888, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.888, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.919, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:40.919, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:40.919, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.919, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.950, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:40.950, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:40.950, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.950, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:40.981, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:40.981, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:40.982, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:40.982, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.013, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:41.013, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:41.013, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.013, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.043, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.043, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.043, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.043, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.074, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.074, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.074, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.074, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.105, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.105, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.106, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.106, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.136, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.136, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.136, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.136, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.167, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.167, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.167, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.167, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.198, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:41.198, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:41.199, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.199, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.229, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.229, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.229, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.229, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.260, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.260, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.260, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.260, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.291, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.291, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.291, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.291, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.323, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=05/29 10:45:41.323, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=05/29 10:45:41.323, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.323, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.353, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.353, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.354, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.354, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.384, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.384, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.384, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.384, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.415, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.415, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.415, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.415, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.446, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.446, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.446, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.446, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.476, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.476, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.477, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.477, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.507, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.507, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.507, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.507, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.538, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.538, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.538, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.538, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.569, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.569, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.569, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.569, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.601, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:41.601, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:41.601, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.601, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.632, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.632, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.632, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.632, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.662, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.662, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.662, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.662, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.693, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.693, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.693, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.693, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.724, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.724, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.724, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.724, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.756, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:41.756, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:41.756, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.756, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.787, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.787, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.787, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.787, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.818, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.818, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.818, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.818, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.850, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:41.850, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:41.850, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.850, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.880, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.880, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.881, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.881, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.911, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.911, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.912, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.912, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.942, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.942, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.942, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.942, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:41.973, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:41.973, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:41.973, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:41.973, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.003, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:42.003, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:42.004, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.004, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.035, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:42.035, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:42.036, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.036, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.067, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:42.067, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:42.067, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.067, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.099, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:42.099, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:42.099, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.099, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.130, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:42.130, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:42.130, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.130, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.163, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=05/29 10:45:42.163, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=05/29 10:45:42.163, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.163, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.193, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:42.193, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:42.194, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.194, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.225, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:42.225, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:42.225, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.225, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.256, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:42.256, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:42.257, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.257, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.287, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:42.287, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:42.288, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.288, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.319, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:42.319, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:42.319, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.319, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.350, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:42.350, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:42.350, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.350, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.381, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:42.381, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:42.381, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.381, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.411, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:42.411, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:42.412, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.412, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.443, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:42.443, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:42.443, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.443, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.474, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:42.474, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:42.474, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.474, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.505, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:42.505, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:42.505, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.505, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.536, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:42.536, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:42.536, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.536, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.567, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:42.567, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:42.567, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.567, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.598, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:42.598, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:42.598, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.598, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.628, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 10:45:42.660, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:42.660, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/29 10:45:42.660, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.660, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.691, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:42.691, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:42.691, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.691, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.722, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:42.722, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:42.722, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.722, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.752, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:42.752, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:42.753, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.753, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.784, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:42.784, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:42.784, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.784, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.814, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:42.814, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:42.814, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.814, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.847, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=05/29 10:45:42.847, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=05/29 10:45:42.847, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.847, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.877, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:42.877, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:42.878, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.878, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.909, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:42.909, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:42.909, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.909, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.940, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:42.940, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:42.940, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.940, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:42.971, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:42.971, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:42.971, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:42.972, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.002, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:43.002, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:43.002, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.002, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.033, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:43.033, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:43.033, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.033, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.064, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:43.064, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:43.064, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.064, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.095, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:43.095, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:43.095, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.095, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.126, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:43.126, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:43.126, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.126, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.157, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:43.157, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:43.157, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.157, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.188, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:43.188, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:43.188, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.188, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.220, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:43.220, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:43.220, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.220, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.251, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:43.251, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:43.251, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.251, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.281, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:43.281, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:43.282, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.282, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.314, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:43.314, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:43.314, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.314, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.345, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:43.345, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:43.346, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.346, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.376, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:43.376, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:43.377, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.377, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.407, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:43.407, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:43.408, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.408, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.438, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:43.438, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:43.439, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.439, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.469, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:43.469, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:43.470, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.470, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.500, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:43.500, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:43.500, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.500, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.531, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:43.531, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:43.531, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.531, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.562, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:43.562, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:43.562, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.562, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.593, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:43.593, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:43.594, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.594, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.624, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:43.624, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:43.624, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.624, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.655, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:43.655, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:43.656, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.656, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.686, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:43.686, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:43.702, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.702, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.733, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:43.733, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:43.733, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.733, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.765, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:43.765, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:43.766, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.766, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.796, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:43.796, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:43.797, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.797, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.828, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:43.828, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:43.828, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.828, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.858, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:43.858, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:43.859, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.859, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.890, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:43.890, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:43.890, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.890, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.921, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:43.921, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:43.921, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.921, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.952, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:43.952, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:43.953, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.953, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:43.984, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:43.984, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:43.984, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:43.984, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.016, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:44.016, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:44.016, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.016, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.047, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:44.047, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:44.048, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.048, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.078, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:44.078, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:44.079, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.079, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.109, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:44.109, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:44.110, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.110, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.141, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:44.141, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:44.141, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.141, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.171, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:44.171, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:44.171, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.171, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.203, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:44.203, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:44.203, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.203, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.234, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:44.234, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:44.234, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.234, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.265, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:44.265, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:44.265, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.265, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.296, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:44.296, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:44.296, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.296, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.327, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:44.327, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:44.328, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.328, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.359, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:44.359, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:44.359, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.359, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.390, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:44.390, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:44.390, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.390, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.421, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:44.421, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:44.422, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.422, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.453, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:44.453, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:44.453, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.453, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.483, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:44.483, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:44.483, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.483, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.514, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:44.514, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:44.514, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.514, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.545, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:44.545, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:44.545, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.545, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.576, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:44.576, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:44.577, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.577, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.607, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:44.607, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:44.608, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.608, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.639, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:44.639, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:44.639, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.639, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.670, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:44.670, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:44.670, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.670, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.700, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:44.700, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:44.701, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.701, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.732, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:44.732, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:44.733, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.733, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.763, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:44.763, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:44.763, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.763, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.794, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:44.794, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:44.794, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.794, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.825, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:44.825, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:44.825, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.825, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.857, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:44.857, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:44.857, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.857, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.887, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:44.887, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:44.888, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.888, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.918, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:44.918, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:44.918, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.918, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.949, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:44.949, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:44.949, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.949, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:44.980, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:44.980, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:44.981, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:44.981, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.012, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:45.012, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:45.012, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.012, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.042, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:45.042, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:45.043, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.043, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.074, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:45.074, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:45.074, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.074, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.105, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:45.105, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:45.105, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.105, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.136, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:45.136, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:45.136, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.136, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.166, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:45.166, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:45.167, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.167, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.198, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:45.198, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:45.198, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.198, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.229, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:45.229, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:45.229, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.229, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.261, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:45.261, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:45.261, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.261, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.291, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:45.291, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:45.292, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.292, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.322, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:45.322, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:45.322, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.322, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.353, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:45.353, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:45.353, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.353, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.384, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:45.384, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:45.384, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.384, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.416, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=05/29 10:45:45.416, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=05/29 10:45:45.417, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.417, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.447, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:45.447, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:45.448, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.448, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.478, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:45.478, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:45.478, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.478, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.509, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:45.509, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:45.509, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.509, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.540, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:45.540, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:45.541, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.541, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.572, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:45.572, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:45.572, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.572, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.603, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:45.603, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:45.603, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.603, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.634, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:45.634, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:45.634, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.634, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.666, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=05/29 10:45:45.666, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=05/29 10:45:45.667, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.667, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.697, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:45.697, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:45.697, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.697, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.728, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:45.728, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:45.729, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.729, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.759, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:45.759, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:45.759, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.759, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.790, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:45.790, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:45.791, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.791, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.821, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:45.821, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:45.822, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.822, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.852, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:45.852, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:45.852, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.852, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.883, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 10:45:45.883, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 10:45:45.884, level=DEBUG, [COM4] Write(4)
time=05/29 10:45:45.884, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 10:45:45.914, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 10:45:45.914, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 10:45:45.914, level=ERROR, class = BtromHandshake, result = fail, timeout = 10018, error_message = btrom handshake timeout
time=05/29 10:45:45.915, level=ERROR, D:\project\crossover\src\task\flash\download_da_uart.cc:141(-1): class = DownloadDa_UART, error_message = btrom handshake fail
time=05/29 10:45:45.915, level=ERROR, class = Task, task_name = DownloadDa_UART, error_message = Fail to execute RunTask()
time=05/29 10:45:45.915, level=DEBUG, class = DownloadDa_UART, task_time = 10.022
time=05/29 10:45:45.915, level=DEBUG, class = Controller, RemoveObserver = callback
time=05/29 10:45:45.915, level=DEBUG, class = CallbackManager, deregister = callback
time=05/29 10:45:45.915, level=DEBUG, class = DisconnectDUT, state = init data, result = ok
time=05/29 10:45:46.039, level=DEBUG, class = SerialHost, state = close
time=05/29 10:45:46.039, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=05/29 10:45:46.039, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=05/29 10:45:46.039, level=DEBUG, class = UartDev, state = disconnect
time=05/29 10:45:46.039, level=DEBUG, class = DisconnectDUT, state = disconnect, device_name = DUT, device_type = UART
time=05/29 10:45:46.039, level=DEBUG, class = DisconnectDUT, task_time = 0.124
time=05/29 10:45:46.039, level=DEBUG, class = LogService, state = CloseLogFile, pass_fail = 1, postfix = 
