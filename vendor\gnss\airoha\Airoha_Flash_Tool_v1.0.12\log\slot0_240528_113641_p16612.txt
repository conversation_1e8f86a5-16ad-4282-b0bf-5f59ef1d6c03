time=05/28 11:36:41.017, level=INFO, class = LogService, tool_version = 0.2.12/air_bta_ic_premium_g3
time=05/28 11:36:41.015, level=INFO, class = Controller, SetLogLevel = 3
time=05/28 11:36:41.015, level=DEBUG, class = ToolLogLevel, log_level = 3
time=05/28 11:36:41.015, level=DEBUG, class = ToolLogLevel, task_time = 0.000
time=05/28 11:36:41.015, level=DEBUG, class = ConnectDUT, state = init data, result = ok
time=05/28 11:36:41.015, level=DEBUG, class = SerialHost, phy_name = UART, trans_name = h4
time=05/28 11:36:41.015, level=DEBUG, class = Physical, type = 1, state = create
time=05/28 11:36:41.015, level=DEBUG, class = Transport, type = 4, state = create
time=05/28 11:36:41.016, level=INFO, class = UartPhy, desc = USB Serial Port (COM4)
time=05/28 11:36:41.016, level=INFO, class = UartPhy, instance_id = FTDIBUS\VID_0403+PID_6015+DQ014GZWA\0000
time=05/28 11:36:41.016, level=INFO, class = UartPhy, port = 4, baud = 3000000, handshake = 0, read/timeout = (3, 0)
time=05/28 11:36:41.061, level=DEBUG, class = SerialHost, state = open
time=05/28 11:36:41.061, level=DEBUG, class = UartDev, state = connect, ret = 0
time=05/28 11:36:41.061, level=DEBUG, class = ConnectDUT, state = connect, device_name = DUT, device_type = UART, result = ok
time=05/28 11:36:41.061, level=DEBUG, class = ConnectDUT, task_time = 0.046
time=05/28 11:36:41.061, level=DEBUG, class = CallbackManager, register = DUT, filter = 0, cb = 0x00D42C00
time=05/28 11:36:41.061, level=DEBUG, class = UartDev, SetBaudrate = 115200
time=05/28 11:36:41.062, level=DEBUG, class = Host, SwitchTransport = bypass
time=05/28 11:36:41.062, level=DEBUG, class = Transport, type = 1, state = create
time=05/28 11:36:41.062, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=05/28 11:36:41.074, level=DEBUG, [COM4] Write(0)
time=05/28 11:36:41.074, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:36:41.074, level=ERROR, class = UartPhy, error_code = 5, error_message = Unable to read from COM-port.
time=05/28 11:36:41.092, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/28 11:36:41.109, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/28 11:36:41.109, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=05/28 11:37:29.079, level=INFO, class = UartPhy, port = 4, baud = 3000000, state = CloseInternal
time=05/28 11:37:29.079, level=DEBUG, class = UartPhy, state = scan
time=05/28 11:37:29.082, level=INFO, class = UartPhy, port = 4, baud = 3000000, handshake = 0, read/timeout = (3, 0)
time=05/28 11:37:29.082, level=ERROR, class = UartPhy, error_code = 4, error_message = Unable to open COM2
time=05/28 11:37:29.082, level=DEBUG, class = UartPhy, re-open = 2
time=05/28 11:37:29.082, level=ERROR, class = UartPhy, error_message = no device to read
time=05/28 11:37:29.082, level=ERROR, class = UartPhy, error_message = no device to write
time=05/28 11:37:29.095, level=ERROR, class = UartPhy, error_message = no device to read
time=05/28 11:37:29.110, level=WARN, class = Host, msg = Match timeout 28 ms(0)
time=05/28 11:37:29.110, level=ERROR, class = UartPhy, error_message = no device to read
time=05/28 11:37:29.126, level=ERROR, class = UartPhy, error_message = no device to read
time=05/28 11:37:29.141, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:37:29.141, level=WARN, class = BtromHandshake, timeout = 59, message = response timeout
time=05/28 11:37:29.141, level=ERROR, class = UartPhy, error_message = no device to read
time=05/28 11:37:29.141, level=ERROR, class = BtromHandshake, result = fail, timeout = 48078, error_message = btrom handshake timeout
time=05/28 11:37:29.141, level=ERROR, D:\project\crossover\src\task\flash\download_da_uart.cc:141(-1): class = DownloadDa_UART, error_message = btrom handshake fail
time=05/28 11:37:29.141, level=ERROR, class = Task, task_name = DownloadDa_UART, error_message = Fail to execute RunTask()
time=05/28 11:37:29.141, level=DEBUG, class = DownloadDa_UART, task_time = 48.081
time=05/28 11:37:29.141, level=DEBUG, class = Controller, RemoveObserver = callback
time=05/28 11:37:29.141, level=DEBUG, class = CallbackManager, deregister = callback
time=05/28 11:37:29.141, level=DEBUG, class = DisconnectDUT, state = init data, result = ok
time=05/28 11:37:29.141, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=05/28 11:37:29.141, level=DEBUG, class = UartDev, state = disconnect
time=05/28 11:37:29.141, level=DEBUG, class = DisconnectDUT, state = disconnect, device_name = DUT, device_type = UART
time=05/28 11:37:29.141, level=DEBUG, class = DisconnectDUT, task_time = 0.000
time=05/28 11:37:29.141, level=DEBUG, class = LogService, state = CloseLogFile, pass_fail = 1, postfix = 
