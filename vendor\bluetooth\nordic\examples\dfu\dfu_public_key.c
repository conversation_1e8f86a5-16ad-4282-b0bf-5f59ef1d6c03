
/* This file was automatically generated by nrfutil on 2021-07-12 (YY-MM-DD) at 11:09:40 */

#include "stdint.h"
#include "compiler_abstraction.h"

/** @brief Public key used to verify DFU images */
__ALIGN(4) const uint8_t pk[64] =
{
    0xbf, 0x14, 0x77, 0xd1, 0x1f, 0xd4, 0x14, 0xbf, 0x79, 0x2d, 0x3e, 0xed, 0xc3, 0xd1, 0x9e, 0x1c, 0xda, 0xeb, 0xb5, 0xcc, 0x20, 0x4a, 0xa5, 0xd1, 0xbc, 0x64, 0x0e, 0x91, 0xaf, 0x0b, 0x83, 0x47,
    0xe0, 0x4b, 0xc7, 0xe4, 0x1d, 0x14, 0x03, 0x78, 0xec, 0xa7, 0x30, 0xe2, 0x1e, 0x7a, 0xc9, 0xe9, 0xa7, 0x78, 0xb2, 0x0a, 0xbf, 0x3f, 0xdf, 0x62, 0x83, 0x19, 0xd7, 0xd7, 0x6d, 0x9c, 0x68, 0x94
};
