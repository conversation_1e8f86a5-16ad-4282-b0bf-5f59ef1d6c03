/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   AppCard.h
@Time    :   2024/12/13 10:17:36
*
**************************************************************************/

#ifndef __APP_CARD_H
#define __APP_CARD_H

#include "AppCardIf.h"
#include "BlankCard.h"
#include "AppCard\CompassCard.h"
#include "QwMenuItem/QwMenuItem.h"
#include "AppCard/DailyActivityCard.h"
#include "AppCard/StepCountCard.h"
#include "AppCard/CaloriesCard.h"
#include "AppCard/HeartRateCard.h"
#include "AppCard/ActiveHoursCard.h"
#include "AppCard/IntenseCard.h"
#include "AppCard/SleepCard.h"
#include "AppCard/HistoryCard.h"
// #include "AppCard/StairsCountCard.h"
// #include "AppCard/AlarmClockCard.h"
// #include "AppCard/CompassCard.h"
#include "AppCard/AltimeterCard.h"
#include "AppCard/BarometerCard.h"
#include "AppCard/SettingCard.h"
#include "AppCard/RunAbilityCard.h"
#include "AppCard/CyclingCard.h"
#include "AppCard/Spo2Card.h"
#include "AppCard/PressureCard.h"
#include "AppCard/TraingStatusCard.h"
#include "AppCard/HrvCard.h"
#include "AppCard/TrainingCoursesCard.h"
#include "AppCard/NotificationCard.h"
#include "AppCard/NavigationCard.h"
#include "AppCard/WeatherCard.h"
#include "AppCard/TrainingPlanCard.h"
#include "AppCard/AchievementsCard.h"

union AppCardUnion {
public:
    AppCardUnion() {}
    ~AppCardUnion() {}
private:
    DailyActivityCard DailyActivityCard_;
    StepCountCard StepCountCard_;
    CaloriesCard CaloriesCard_;
    ActiveHoursCard ActiveHoursCard_;
    IntenseCard IntenseCard_;
    SleepCard SleepCard_;
    HeartRateCard HeartRateCard_;
    CompassCard CompassCard_;
    HistoryCard HistoryCard_;
    AltimeterCard AltimeterCard_;
    BarometerCard BarometerCard_;
    RunAbilityCard RunAbilityCard_;
    CyclingCard CyclingCard_;
    SettingCard type_SettingCard;
    Spo2Card Spo2Card_;
    PressureCard PressureCard_;
    TraingStatusCard TraingStatusCard_;
    HrvCard HrvCard_;
    TrainingCoursesCard TrainingCoursesCard_;
    NotificationCard NotificationCard_;
    NavigationCard NavigationCard_;
    WeatherCard WeatherCard_;
    TrainingPlanCard TrainingPlanCard_;
    AchievementsCard AchievementsCard_;
    // BlankCard type_BlankCard;
    // CompassCard type_CompassCard;
    // AlarmClockCard type_AlarmClockCard;

    // StairsCountCard type_StairsCountCard;
};

class AppCard : public ItemBaseCtrl
{
protected:
    AppCardUnion item_buffer_;
    int index_;
    int type_;

    AppCardIf* context_;

public:
    AppCard();
    virtual ~AppCard();

    virtual void setup(int index, int type, void* info) ;
    virtual void quit() ;
    virtual void focus(bool focus) ;
    virtual void* get_user_data()  { return (void*)index_; };

    virtual void update(void *data);

    // void destroy_context();

};


#endif //__APP_CARD_H
