time=05/28 11:31:12.185, level=INFO, class = LogService, tool_version = 0.2.12/air_bta_ic_premium_g3
time=05/28 11:31:12.182, level=INFO, class = Controller, SetLogLevel = 3
time=05/28 11:31:12.182, level=DEBUG, class = ToolLogLevel, log_level = 3
time=05/28 11:31:12.182, level=DEBUG, class = ToolLogLevel, task_time = 0.000
time=05/28 11:31:12.182, level=DEBUG, class = ConnectDUT, state = init data, result = ok
time=05/28 11:31:12.182, level=DEBUG, class = SerialHost, phy_name = UART, trans_name = h4
time=05/28 11:31:12.182, level=DEBUG, class = Physical, type = 1, state = create
time=05/28 11:31:12.183, level=DEBUG, class = Transport, type = 4, state = create
time=05/28 11:31:12.184, level=INFO, class = UartPhy, desc = USB Serial Port (COM4)
time=05/28 11:31:12.184, level=INFO, class = UartPhy, instance_id = FTDIBUS\VID_0403+PID_6015+DQ014GZWA\0000
time=05/28 11:31:12.184, level=INFO, class = UartPhy, port = 4, baud = 3000000, handshake = 0, read/timeout = (3, 0)
time=05/28 11:31:12.225, level=DEBUG, class = SerialHost, state = open
time=05/28 11:31:12.225, level=DEBUG, class = UartDev, state = connect, ret = 0
time=05/28 11:31:12.225, level=DEBUG, class = ConnectDUT, state = connect, device_name = DUT, device_type = UART, result = ok
time=05/28 11:31:12.225, level=DEBUG, class = ConnectDUT, task_time = 0.043
time=05/28 11:31:12.225, level=DEBUG, class = CallbackManager, register = DUT, filter = 0, cb = 0x00D42C00
time=05/28 11:31:12.225, level=DEBUG, class = UartDev, SetBaudrate = 115200
time=05/28 11:31:12.225, level=DEBUG, class = Host, SwitchTransport = bypass
time=05/28 11:31:12.225, level=DEBUG, class = Transport, type = 1, state = create
time=05/28 11:31:12.225, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=05/28 11:31:12.237, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.237, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:12.255, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/28 11:31:12.274, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=05/28 11:31:12.274, level=WARN, class = BtromHandshake, timeout = 36, message = response timeout
time=05/28 11:31:12.274, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.274, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:12.292, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=05/28 11:31:12.309, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/28 11:31:12.309, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=05/28 11:31:12.310, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.310, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:12.328, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=05/28 11:31:12.345, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/28 11:31:12.345, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=05/28 11:31:12.345, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.345, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:12.364, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=05/28 11:31:12.382, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/28 11:31:12.382, level=WARN, class = BtromHandshake, timeout = 36, message = response timeout
time=05/28 11:31:12.382, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.382, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:12.400, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=05/28 11:31:12.426, level=WARN, class = Host, msg = Match timeout 26 ms(0)
time=05/28 11:31:12.426, level=WARN, class = BtromHandshake, timeout = 44, message = response timeout
time=05/28 11:31:12.426, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.426, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:12.457, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:12.457, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:12.458, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.458, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:12.488, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:12.488, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:12.489, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.489, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:12.520, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:12.520, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:12.520, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.520, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:12.550, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:12.550, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:12.550, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.550, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:12.581, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:12.581, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:12.581, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.581, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:12.612, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:12.612, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:12.613, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.613, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:12.644, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:12.644, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:12.644, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.644, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:12.675, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:12.675, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:12.675, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.675, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:12.707, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:12.707, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:12.707, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.707, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:12.738, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:12.738, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:12.738, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.738, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:12.768, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:12.768, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:12.769, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.769, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:12.800, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:12.800, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:12.800, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.800, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:12.831, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:12.831, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:12.831, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.831, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:12.862, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:12.862, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:12.863, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.863, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:12.894, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:12.894, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:12.894, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.894, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:12.925, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:12.925, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:12.925, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.925, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:12.956, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:12.956, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:12.956, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.956, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:12.987, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:12.987, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:12.988, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:12.988, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.019, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:13.019, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:13.019, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.019, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.050, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:13.050, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:13.050, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.050, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.080, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:13.080, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:13.080, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.080, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.111, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:13.111, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:13.111, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.111, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.142, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:13.142, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:13.143, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.143, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.174, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:13.174, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:13.174, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.174, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.206, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:13.206, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:13.206, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.206, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.237, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:13.237, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:13.237, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.237, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.269, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:13.269, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:13.269, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.269, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.300, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:13.300, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:13.300, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.300, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.332, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=05/28 11:31:13.332, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=05/28 11:31:13.333, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.333, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.363, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:13.363, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:13.364, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.364, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.394, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:13.394, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:13.394, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.394, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.425, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:13.425, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:13.425, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.425, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.456, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:13.456, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:13.456, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.456, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.487, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:13.487, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:13.487, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.487, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.518, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:13.518, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:13.518, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.518, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.549, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:13.549, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:13.549, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.549, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.581, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:13.581, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:13.581, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.581, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.612, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:13.612, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:13.612, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.612, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.643, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:13.643, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:13.643, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.643, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.674, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:13.674, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:13.674, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.674, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.705, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:13.705, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:13.705, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.705, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.737, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:13.737, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:13.737, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.737, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.768, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:13.768, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:13.768, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.768, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.799, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:13.799, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:13.799, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.799, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.831, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:13.831, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:13.831, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.831, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.862, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:13.862, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:13.863, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.863, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.893, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:13.893, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:13.893, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.893, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.924, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:13.924, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:13.924, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.924, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.955, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:13.955, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:13.955, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:13.955, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:13.985, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:31:14.017, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:14.017, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/28 11:31:14.017, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.017, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.048, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:14.048, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:14.048, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.048, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.079, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:14.079, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:14.080, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.080, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.110, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:14.110, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:14.111, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.111, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.142, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:14.142, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:14.143, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.143, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.172, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:31:14.204, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:14.204, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/28 11:31:14.204, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.204, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.235, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:14.235, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:14.235, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.235, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.266, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:14.266, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:14.266, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.266, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.297, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:14.297, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:14.297, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.297, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.329, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:14.329, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:14.329, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.329, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.360, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:14.360, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:14.360, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.360, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.391, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:14.391, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:14.392, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.392, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.422, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:14.422, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:14.423, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.423, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.454, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:14.454, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:14.454, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.454, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.484, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:31:14.516, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:14.516, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/28 11:31:14.516, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.516, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.547, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:14.547, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:14.547, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.547, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.578, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:14.578, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:14.578, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.578, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.609, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:14.609, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:14.609, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.609, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.639, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:14.639, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:14.639, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.639, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.671, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:14.671, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:14.671, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.671, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.702, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:14.702, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:14.702, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.702, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.733, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:14.733, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:14.733, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.733, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.764, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:14.764, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:14.764, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.764, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.795, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:14.795, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:14.795, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.795, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.827, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:14.827, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:14.827, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.827, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.858, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:14.858, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:14.859, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.859, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.889, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:14.889, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:14.890, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.890, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.920, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:14.920, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:14.920, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.920, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.951, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:14.951, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:14.951, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.951, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:14.983, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:14.983, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:14.984, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:14.984, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.014, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:15.014, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:15.014, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.014, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.046, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:15.046, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:15.046, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.046, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.078, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:15.078, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:15.078, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.078, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.108, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:15.108, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:15.109, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.109, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.140, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:15.140, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:15.140, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.140, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.171, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:15.171, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:15.171, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.171, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.202, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:15.202, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:15.203, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.203, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.233, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:15.233, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:15.233, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.233, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.264, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:15.264, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:15.264, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.264, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.295, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:15.295, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:15.296, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.296, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.326, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:15.326, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:15.327, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.327, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.357, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:15.357, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:15.358, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.358, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.389, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:15.389, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:15.389, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.389, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.420, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:15.420, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:15.420, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.420, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.450, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:31:15.482, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=05/28 11:31:15.482, level=WARN, class = BtromHandshake, timeout = 62, message = response timeout
time=05/28 11:31:15.482, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.482, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.512, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:31:15.544, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:15.544, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/28 11:31:15.544, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.544, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.575, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:15.575, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:15.576, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.576, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.606, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:15.606, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:15.606, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.606, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.637, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:15.637, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:15.638, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.638, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.669, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:15.669, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:15.669, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.669, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.700, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:15.700, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:15.700, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.700, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.731, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:15.731, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:15.731, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.731, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.762, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:15.762, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:15.763, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.763, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.793, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:15.793, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:15.794, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.794, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.825, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:15.825, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:15.825, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.825, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.856, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:15.856, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:15.857, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.857, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.887, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:15.887, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:15.888, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.888, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.919, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:15.919, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:15.919, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.919, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.950, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:15.950, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:15.950, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.950, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:15.981, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:15.981, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:15.981, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:15.981, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.012, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:16.012, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:16.012, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.012, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.043, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:16.043, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:16.043, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.043, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.074, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:16.074, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:16.074, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.074, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.105, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:16.105, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:16.105, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.105, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.136, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:16.136, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:16.136, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.136, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.167, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:16.167, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:16.167, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.167, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.199, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:16.199, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:16.199, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.199, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.231, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:16.231, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:16.231, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.231, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.262, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:16.262, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:16.263, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.263, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.294, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:16.294, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:16.294, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.294, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.325, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:16.325, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:16.326, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.326, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.357, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:16.357, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:16.357, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.357, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.387, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:31:16.419, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:16.419, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/28 11:31:16.419, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.419, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.449, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:16.449, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:16.449, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.449, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.481, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:16.481, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:16.481, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.481, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.512, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:16.512, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:16.512, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.512, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.543, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:16.543, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:16.544, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.544, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.575, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:16.575, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:16.575, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.575, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.606, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:16.606, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:16.606, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.606, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.637, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:16.637, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:16.637, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.637, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.667, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:16.667, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:16.668, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.668, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.699, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:16.699, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:16.699, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.699, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.730, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:16.730, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:16.730, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.730, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.762, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:16.762, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:16.762, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.762, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.793, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:16.793, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:16.793, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.793, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.824, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:16.824, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:16.824, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.824, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.856, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:16.856, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:16.856, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.856, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.887, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:16.887, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:16.888, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.888, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.918, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:16.918, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:16.919, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.919, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.950, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:16.950, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:16.950, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.950, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:16.980, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:16.980, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:16.981, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:16.981, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.012, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:17.012, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:17.012, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.012, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.043, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:17.043, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:17.043, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.043, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.074, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:17.074, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:17.075, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.075, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.106, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:17.106, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:17.106, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.106, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.137, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:17.137, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:17.138, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.138, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.169, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:17.169, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:17.169, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.169, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.200, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:17.200, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:17.200, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.200, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.231, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:17.231, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:17.231, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.231, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.262, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:17.262, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:17.262, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.262, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.293, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:17.293, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:17.293, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.293, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.324, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:17.324, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:17.324, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.324, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.355, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:17.355, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:17.355, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.355, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.386, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:17.386, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:17.387, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.387, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.417, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:17.417, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:17.417, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.418, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.449, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:17.449, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:17.449, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.449, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.480, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:17.480, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:17.480, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.480, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.511, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:17.511, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:17.511, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.511, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.542, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:17.542, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:17.542, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.542, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.573, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:17.573, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:17.573, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.573, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.604, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:17.604, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:17.604, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.604, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.635, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:17.635, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:17.635, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.635, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.666, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:17.666, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:17.667, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.667, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.697, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:17.697, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:17.697, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.697, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.729, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:17.729, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:17.729, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.729, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.760, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:17.760, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:17.760, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.760, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.791, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:17.791, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:17.791, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.791, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.822, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:17.822, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:17.822, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.822, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.854, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:17.854, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:17.854, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.854, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.885, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:17.885, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:17.885, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.885, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.917, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:17.917, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:17.917, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.917, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.949, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:17.949, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:17.949, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.949, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:17.979, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:17.979, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:17.980, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:17.980, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.010, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:18.010, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:18.011, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.011, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.041, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:18.041, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:18.042, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.042, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.073, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:18.073, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:18.073, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.073, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.105, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:18.105, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:18.105, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.105, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.137, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:18.137, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:18.137, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.137, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.169, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:18.169, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:18.169, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.169, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.200, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:18.200, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:18.200, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.200, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.231, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:18.231, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:18.231, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.231, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.263, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:18.263, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:18.263, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.263, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.293, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:18.293, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:18.294, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.294, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.325, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:18.325, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:18.325, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.325, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.356, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:18.356, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:18.356, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.356, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.388, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:18.388, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:18.388, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.388, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.419, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:18.419, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:18.419, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.419, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.449, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:18.449, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:18.449, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.449, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.481, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:18.481, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:18.482, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.482, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.512, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:18.513, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:18.513, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.513, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.544, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:18.544, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:18.544, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.544, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.575, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:18.575, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:18.575, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.575, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.606, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:18.606, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:18.607, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.607, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.637, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:18.637, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:18.638, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.638, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.669, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:18.669, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:18.669, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.669, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.699, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:18.699, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:18.700, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.700, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.732, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:18.732, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:18.732, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.732, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.763, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:18.763, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:18.764, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.764, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.794, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:18.794, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:18.794, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.794, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.826, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:18.826, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:18.827, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.827, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.857, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:18.857, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:18.858, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.858, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.888, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:18.888, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:18.889, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.889, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.919, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:18.919, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:18.919, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.919, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.950, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:18.950, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:18.950, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.950, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:18.980, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:31:18.980, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:18.981, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:18.981, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.011, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:19.011, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:19.012, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.012, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.042, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:31:19.072, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:19.072, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/28 11:31:19.073, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.073, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.104, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:19.104, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:19.104, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.104, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.135, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:19.135, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:19.136, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.136, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.166, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:19.166, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:19.167, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.167, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.197, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:19.197, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:19.198, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.198, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.228, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:19.228, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:19.229, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.229, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.259, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:19.259, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:19.259, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.259, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.291, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:19.291, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=05/28 11:31:19.292, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.292, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.323, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:19.323, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:19.323, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.323, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.354, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:19.354, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:19.354, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.354, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.385, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:19.385, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:19.385, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.386, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.416, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:19.416, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:19.417, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.417, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.448, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:19.448, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:19.448, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.448, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.479, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:19.479, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:19.479, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.479, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.510, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:19.510, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:19.511, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.511, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.541, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:19.541, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:19.542, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.542, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.572, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:19.572, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:19.572, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.572, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.603, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:19.603, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:19.603, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.603, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.634, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:19.634, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:19.635, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.635, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.665, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:19.665, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:19.666, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.666, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.696, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:19.696, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:19.697, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.697, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.728, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:19.728, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:19.728, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.728, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.759, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:19.759, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:19.759, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.759, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.790, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:19.790, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:19.791, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.791, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.822, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:19.822, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:19.822, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.822, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.854, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:19.854, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:19.855, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.855, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.886, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:19.886, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:19.886, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.886, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.917, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:19.917, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:19.918, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.918, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.948, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:19.948, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:19.949, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.949, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:19.979, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:19.979, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:19.980, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:19.980, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.010, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:20.010, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:20.011, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.011, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.041, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:20.041, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:20.041, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.041, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.072, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:20.072, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:20.072, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.072, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.103, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:20.103, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:20.103, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.103, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.134, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:20.134, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:20.134, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.134, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.165, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:20.165, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:20.166, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.166, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.197, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:20.197, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:20.197, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.197, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.228, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:20.228, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:20.228, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.228, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.258, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:31:20.289, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:20.289, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/28 11:31:20.289, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.289, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.321, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:20.321, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:20.322, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.322, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.352, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:20.352, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:20.353, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.353, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.384, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:20.384, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:20.384, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.384, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.415, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:20.415, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:20.415, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.415, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.445, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:20.445, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:20.446, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.446, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.476, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:20.476, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:20.477, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.477, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.508, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:20.508, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:20.508, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.508, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.539, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:20.539, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:20.540, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.540, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.570, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:20.570, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:20.570, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.570, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.602, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:20.602, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:20.602, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.602, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.634, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:20.634, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:20.634, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.634, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.665, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:20.665, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:20.665, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.665, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.696, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:20.696, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:20.697, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.697, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.727, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:20.727, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:20.728, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.728, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.758, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:20.758, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:20.758, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.758, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.789, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:20.789, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:20.789, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.789, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.820, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:20.820, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:20.821, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.821, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.852, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:20.852, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:20.853, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.853, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.884, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:20.884, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:20.884, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.884, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.915, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:20.915, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:20.915, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.915, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.946, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:20.946, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:20.946, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.946, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:20.977, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:20.977, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:20.978, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:20.978, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.008, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:21.008, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:21.009, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.009, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.039, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:21.039, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:21.039, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.039, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.070, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:21.070, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:21.071, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.071, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.102, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:21.102, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:21.102, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.102, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.133, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:21.133, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:21.133, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.133, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.164, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:21.164, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:21.164, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.164, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.195, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:21.195, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:21.195, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.195, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.226, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:21.226, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:21.227, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.227, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.258, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:21.258, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:21.258, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.258, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.289, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:21.289, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:21.289, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.289, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.320, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:21.320, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:21.321, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.321, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.352, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:21.352, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:21.352, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.352, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.383, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:21.383, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:21.383, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.383, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.414, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:21.414, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:21.414, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.414, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.444, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:31:21.475, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:21.475, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/28 11:31:21.476, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.476, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.506, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:21.506, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:21.507, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.507, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.537, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:21.537, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:21.538, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.538, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.569, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:21.569, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:21.569, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.569, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.601, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:21.601, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:21.602, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.602, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.632, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:21.632, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:21.633, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.633, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.664, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:21.664, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:21.664, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.664, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.695, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:21.695, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:21.696, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.696, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.727, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:21.727, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:21.728, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.728, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.759, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:21.759, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:21.759, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.759, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.789, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:31:21.820, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:21.820, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/28 11:31:21.821, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.821, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.850, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/28 11:31:21.881, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:21.881, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/28 11:31:21.882, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.882, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.913, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:21.913, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:21.913, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.913, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.943, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:21.943, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:21.944, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.944, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:21.974, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:21.974, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:21.975, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:21.975, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:22.005, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:22.005, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:22.006, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:22.006, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:22.037, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:22.037, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:22.038, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:22.038, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:22.068, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:22.068, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:22.068, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:22.068, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:22.099, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:22.099, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:22.099, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:22.099, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:22.130, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:22.130, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:22.130, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:22.130, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:22.161, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:22.161, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:22.161, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:22.161, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:22.193, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:22.193, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:22.193, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:22.193, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:22.224, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/28 11:31:22.224, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/28 11:31:22.224, level=DEBUG, [COM4] Write(4)
time=05/28 11:31:22.224, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/28 11:31:22.255, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/28 11:31:22.255, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/28 11:31:22.255, level=ERROR, class = BtromHandshake, result = fail, timeout = 10017, error_message = btrom handshake timeout
time=05/28 11:31:22.256, level=ERROR, D:\project\crossover\src\task\flash\download_da_uart.cc:141(-1): class = DownloadDa_UART, error_message = btrom handshake fail
time=05/28 11:31:22.256, level=ERROR, class = Task, task_name = DownloadDa_UART, error_message = Fail to execute RunTask()
time=05/28 11:31:22.256, level=DEBUG, class = DownloadDa_UART, task_time = 10.031
time=05/28 11:31:22.256, level=DEBUG, class = Controller, RemoveObserver = callback
time=05/28 11:31:22.256, level=DEBUG, class = CallbackManager, deregister = callback
time=05/28 11:31:22.256, level=DEBUG, class = DisconnectDUT, state = init data, result = ok
time=05/28 11:31:22.379, level=DEBUG, class = SerialHost, state = close
time=05/28 11:31:22.379, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=05/28 11:31:22.379, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=05/28 11:31:22.379, level=DEBUG, class = UartDev, state = disconnect
time=05/28 11:31:22.379, level=DEBUG, class = DisconnectDUT, state = disconnect, device_name = DUT, device_type = UART
time=05/28 11:31:22.379, level=DEBUG, class = DisconnectDUT, task_time = 0.123
time=05/28 11:31:22.379, level=DEBUG, class = LogService, state = CloseLogFile, pass_fail = 1, postfix = 
