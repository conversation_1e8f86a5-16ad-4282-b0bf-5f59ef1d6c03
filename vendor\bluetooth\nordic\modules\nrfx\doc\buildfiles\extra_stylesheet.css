.appliesto {background-color:#3D578C;color:#fff}

div.header
{
        background-image:none;
	background-color: #FFF;
	border-bottom: 0px;
}

body, table, div, p, dl {
    font-size: 16px;
    font-family: Open Sans, Calibri, Arial, Sans-Serif;
	color: #474747;
	line-height: 20px;

}

a.code {
 color: #1c99c7;
}

a.el {
 font-weight: normal;
}

.contents a:visited, a:visited.code {
	color: #16779a;
}

.title {
    font-size: 1.34em
}

h1 {
    font-size: 1.25em
}

h2 {
    font-size: 1.15em
}

h3 {
    font-size: 1.05em
}

h4 {
    font-size: 1em
}

table.memberdecls, table.directory, table.memname {
 margin:0px;
 border:0px;
 -moz-box-shadow: 0 0px 0px #d1d1d1;
 -webkit-box-shadow: 0 0px 0px #d1d1d1;
 box-shadow: 0 0px 0px #d1d1d1;
}

table.memberdecls tr {
 padding-left:0px;
}

table.memberdecls tr:hover td, table.memname tr:hover td {
 background: inherit;
}

table.directory tr.even, table.directory tr.odd {
 background: inherit;
}

table.memberdecls td, table.directory td, table.directory td.desc {
 border:0px;
 padding: 2px 0px 0px;
}

table.memberdecls td.memSeparator {
 background-color:#inherit;
 padding:2px;
 border-bottom: 1px dotted #DEE4F0;
}

.mdescLeft, .mdescRight,
.memItemLeft, .memItemRight,
.memTemplItemLeft, .memTemplItemRight, .memTemplParams {
	background-color: inherit;
}

div.levels {
 display:none;
}

table.retval {
border:#ccc 1px solid;
}


table.memname td, table.params td, table.retval td {
padding:5px;
border:0px;
}

div.fragment div.line {
font-size: 14px;
line-height:18px;
}

table.fieldtable, table.params, table.retval {
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

table.fieldtable th {
        border:none;
	border-bottom:1px solid #A8B8D9;
}

table.blank, table.blank tr th, table.blank tr td {
	border:none;
	-moz-box-shadow: none;
	-webkit-box-shadow: none;
	box-shadow: none;
}

table.blank tr:hover td {
	background: #ffffff;	
}


#projectlogo
{
	text-align: left;
	vertical-align: middle;
	border-collapse: separate;
}
 
#projectname
{
    font-size: 40px;
    font-family: Open Sans, Calibri, Arial, Sans-Serif;
	margin: 0px;
	padding: 2px 0px;
}
    
#projectbrief
{
    font-size: 30px;
	margin: 0px;
	padding: 0px;
}

#titlearea
{
	border-bottom: 1px solid #e0e0e0;
}

.label a, .item a
{
	border-bottom: none;
	
}

#nav-tree {
  background-image: none;
  background-color: #FAFAFA;
}

div.contents {
	margin-left: 30px;
	margin-right: 30px;

}

div.header {
	margin-left: 20px;
}

table td.doclinks a {
    font-size: 12px;
    font-style: italic;
    color: #e97c25;
    border: 1px solid #e97c25;
    padding: 2px 5px;
    text-decoration: none;
}

table td.docselected a {
    background: #e97c25;
    color: #fff;
}

table td.doclinkintro {
    font-size: 12px;
    font-style: italic;
}


/* nordic.css */
.p {
	margin-top: .3em;
}

/* fix for table spacing  */
td p.p {
	margin: 0em;
	padding: 0px;
} 
dt.line_sep {
	border-top: solid #c9c9c9 1px;
	padding-top: 5px;
	
}

a
{
    color: #1c99c7;
    text-decoration: none;
	border-bottom: 1px #e5e5e5 solid;
	
}

a:visited
{
   color: #16779a;
}
a:hover
{
   color: #none;
   text-decoration: underline;
   border: none;
}

a:active
{
   
}

ul.ul {
	margin-top: 4px;
	margin-bottom: 10px;
}


a[href*='.pdf'] {
background:transparent url(./pdf.png) center left no-repeat;
padding-left:22px;
line-height:18px;
}


a[href*='.zip'] {
background:transparent url(./zip_s.png) center left no-repeat;
padding-left:22px;
line-height:18px;
}

a[href*='.exe'] {
background:transparent url(./execute_s.png) center left no-repeat;
padding-left:22px;
line-height:18px;
}

a[href*='.msi'] {
background:transparent url(./msi_s.png) center left no-repeat;
padding-left:22px;
line-height:18px;
}

table a:link {
	color: #1c99c7;
    text-decoration: none;
	border-bottom: 1px #e5e5e5 solid;
}
table a:visited {
	color: #16779a;
}
table a:active,
table a:hover {
	color: #none;
   text-decoration: underline;
   border: none;
}
table {
	font-family:Calibri, Arial, Sans-Serif;
	color:#474747;
	font-size:16px;
	margin-left: auto;
	margin-right: auto;
	border:#ccc 1px solid;
	

	-moz-box-shadow: 0 1px 2px #d1d1d1;
	-webkit-box-shadow: 0 1px 2px #d1d1d1;
	box-shadow: 0 1px 2px #d1d1d1;
}
table th {
	color: #000;
	font-size: 18px;
	font-weight: bold;
	text-align: left;
	padding:10px 15px 10px 10px;
	border-top:1px solid #7eceed;
	border-bottom:1px solid #7eceed;
	border-right:1px solid #7eceed;
	border-left:1px solid #7eceed;

	background: #7eceed;
	background: -webkit-gradient(linear, left top, left bottom, from(#7eceed), to(#7eceed));
	background: -moz-linear-gradient(top,  #7eceed,  #7eceed);
}

table th.center {
	text-align: center;
}
table tr {
	text-align: left;
	
}
table td {
	padding:5px 5px 5px 10px;
	border-top: 1px solid #ffffff;
	border-bottom:1px solid #e0e0e0;
	border-left: 1px solid #e0e0e0;
	border-right: 1px solid #e0e0e0;

}
table tr:hover td {
	background: #ebebeb;
	background: -webkit-gradient(linear, left top, left bottom, from(#f2f2f2), to(#f0f0f0));
	background: -moz-linear-gradient(top,  #f2f2f2,  #f0f0f0);	
}




img {
	border: 0;
	margin-left: auto;
	margin-right: auto;
	max-width:100%;
}


/* make svg files scale in IE. compatible with Dita OT v2.0 
img:not(.png) {
	width: 100%;
	margin-left: auto;
	margin-right: auto;
}
*/


/* make svg files scale in IE */
embed.image:not(.png):not(.gif):not(.jpg) {
	width: 100%;
	margin-left: auto;
	margin-right: auto;
	margin-bottom: 15px;
}

svg {
	position: absolute;
	top: 0;
	left: 0;

}

caption {
	caption-side: bottom;
	text-align: center;
	font-size: 100%;
	font-weight: bold;
	margin-top: 15px;
	margin-bottom: 35px;
	
	}
	
figdesc {
	caption-side: bottom;
	text-align: center;
	font-size: 100%;
	font-weight: bold;
	margin-top: 15px;
	margin-bottom: 20px;
	
	}	

/* to get figure captions to appear below the image and center */	
	
div.fig {
    display: table; 
	width: 100%;
	margin-top: 10px;
	margin-bottom: 55px;
}	

div.fig span.figcap {
    display:table-footer-group;
    text-align:center;
	font-size: 100%;
	font-weight: bold;
	margin-top: 10px;
	margin-bottom: 20px;
	font-style: normal;
}
div.fig div.imagecenter {
    display:table-row-group;
}


/* fix to hide borders in image maps (Chrome only) */
img.map, map area{
    outline: none;
}


/* fix placement of <sup>&reg;</sup> */ 
sup {
    line-height: 1em;
}

.sdkversion span {
    font-size: 12px;
    font-style: italic;
    color: #e97c25;
    border: 1px solid #e97c25;
    padding: 2px 5px;
}

.sdkversion {
    text-align: right;
}

.whichSDs span,.whichnRF span {
    font-size: 12px;
    font-style: italic;
    color: #e97c25;
    border: 1px solid #e97c25;
    padding: 2px 5px;
 }

.whichSDs.nRF52 span,.whichnRF.nRF52 span {
    color: #e97c25;
    border-color: #e97c25;
}

.whichSDs.nRF52840 span,.whichnRF.nRF52840 span {
    color: #0081B7;
    border-color: #0081B7;
}

.whichSDs.nRF51 span,.whichnRF.nRF51 span {
    color: #7f7f7f;
    border-color: #7f7f7f;
}

div.whichnRF {
    padding-bottom: 5px;
}

span.whichnRF{
    font-size: 12px;
    font-style: italic;
    color: #e97c25;
    border: 1px solid #e97c25;
    padding: 2px 5px;
}

span.whichnRF.nRF52 {
    color: #e97c25;
    border-color: #e97c25;
}

span.whichnRF.nRF52840 {
    color: #0081B7;
    border-color: #0081B7;
}

span.whichnRF.nRF51 {
    color: #7f7f7f;
    border-color: #7f7f7f;
}

.orange {
	color: #e97c25;
}

hr {
    margin-top:20px;
    border-top:1px solid #8EA7B0;
}


.directory td.entry {
        white-space: normal;
        width:50%;
}

/* overrides */
.topicfooter {
    text-align: right;
//    margin-top: 1px;
    padding-right: 10px;
    box-shadow: inset 0px 1px 0px 0px #e0e0e0;
    font-size: 13px;
}

