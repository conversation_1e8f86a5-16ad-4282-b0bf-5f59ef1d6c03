time=06/25 17:36:49.543, level=INFO, class = LogService, tool_version = 0.2.12/air_bta_ic_premium_g3
time=06/25 17:36:49.541, level=INFO, class = Controller, SetLogLevel = 3
time=06/25 17:36:49.541, level=DEBUG, class = ToolLogLevel, log_level = 3
time=06/25 17:36:49.541, level=DEBUG, class = ToolLogLevel, task_time = 0.000
time=06/25 17:36:49.543, level=DEBUG, class = ConnectDUT, state = init data, result = ok
time=06/25 17:36:49.543, level=DEBUG, class = SerialHost, phy_name = UART, trans_name = h4
time=06/25 17:36:49.543, level=DEBUG, class = Physical, type = 1, state = create
time=06/25 17:36:49.543, level=DEBUG, class = Transport, type = 4, state = create
time=06/25 17:36:49.550, level=INFO, class = UartPhy, desc = USB-SERIAL CH340 (COM9)
time=06/25 17:36:49.550, level=INFO, class = UartPhy, instance_id = USB\VID_1A86&PID_7523\6&493701C&1&3
time=06/25 17:36:49.550, level=INFO, class = UartPhy, port = 9, baud = 3000000, handshake = 0, read/timeout = (3, 0)
time=06/25 17:36:49.550, level=ERROR, class = UartPhy, error_code = 9, error_message = Unable to open COM5
time=06/25 17:36:49.550, level=DEBUG, class = SerialHost, state = open
time=06/25 17:36:49.550, level=DEBUG, class = UartDev, state = connect, ret = 5
time=06/25 17:36:49.550, level=ERROR, class = UartPhy, error_message = no device to read
time=06/25 17:36:49.550, level=ERROR, class = UartPhy, error_message = no device to read
time=06/25 17:36:49.550, level=ERROR, D:\project\crossover\src\task\connect_device.cc:199(-1): class = ConnectDUT, device_name = DUT, device_type = UART, error_message = connect fail
time=06/25 17:36:49.550, level=ERROR, class = Task, task_name = ConnectDUT, error_message = Fail to execute RunTask()
time=06/25 17:36:49.550, level=DEBUG, class = ConnectDUT, task_time = 0.009
time=06/25 17:36:49.550, level=DEBUG, class = LogService, state = CloseLogFile, pass_fail = 1, postfix = 
