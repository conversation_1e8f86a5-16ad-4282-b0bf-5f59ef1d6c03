time=06/07 09:47:07.063, level=INFO, class = LogService, tool_version = 0.2.12/air_bta_ic_premium_g3
time=06/07 09:47:07.060, level=INFO, class = Controller, SetLogLevel = 3
time=06/07 09:47:07.060, level=DEBUG, class = ToolLogLevel, log_level = 3
time=06/07 09:47:07.060, level=DEBUG, class = ToolLogLevel, task_time = 0.000
time=06/07 09:47:07.060, level=DEBUG, class = ConnectDUT, state = init data, result = ok
time=06/07 09:47:07.060, level=DEBUG, class = SerialHost, phy_name = UART, trans_name = h4
time=06/07 09:47:07.060, level=DEBUG, class = Physical, type = 1, state = create
time=06/07 09:47:07.060, level=DEBUG, class = Transport, type = 4, state = create
time=06/07 09:47:07.062, level=INFO, class = UartPhy, desc = USB Serial Port (COM4)
time=06/07 09:47:07.062, level=INFO, class = UartPhy, instance_id = FTDIBUS\VID_0403+PID_6015+DQ014GZWA\0000
time=06/07 09:47:07.062, level=INFO, class = UartPhy, port = 4, baud = 3000000, handshake = 0, read/timeout = (3, 0)
time=06/07 09:47:07.102, level=DEBUG, class = SerialHost, state = open
time=06/07 09:47:07.102, level=DEBUG, class = UartDev, state = connect, ret = 0
time=06/07 09:47:07.102, level=DEBUG, class = ConnectDUT, state = connect, device_name = DUT, device_type = UART, result = ok
time=06/07 09:47:07.102, level=DEBUG, class = ConnectDUT, task_time = 0.042
time=06/07 09:47:07.102, level=DEBUG, class = CallbackManager, register = DUT, filter = 0, cb = 0x00EE2C00
time=06/07 09:47:07.103, level=DEBUG, class = UartDev, SetBaudrate = 115200
time=06/07 09:47:07.103, level=DEBUG, class = Host, SwitchTransport = bypass
time=06/07 09:47:07.103, level=DEBUG, class = Transport, type = 1, state = create
time=06/07 09:47:07.103, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=06/07 09:47:07.116, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.116, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.133, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:47:07.151, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:47:07.151, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=06/07 09:47:07.151, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.151, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.168, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:47:07.185, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:47:07.185, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=06/07 09:47:07.186, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.186, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.203, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:47:07.220, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:47:07.220, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=06/07 09:47:07.220, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.220, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.237, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:47:07.255, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:47:07.255, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=06/07 09:47:07.255, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.255, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.273, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=06/07 09:47:07.306, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=06/07 09:47:07.306, level=WARN, class = BtromHandshake, timeout = 50, message = response timeout
time=06/07 09:47:07.306, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.306, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.337, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:07.337, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:07.337, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.337, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.368, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:07.368, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:07.368, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.368, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.400, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:07.400, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:07.400, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.400, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.431, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:07.431, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:07.431, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.431, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.462, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:07.462, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:07.462, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.462, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.493, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:07.493, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:07.493, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.493, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.525, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:07.525, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:07.525, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.525, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.556, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:07.556, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:07.556, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.556, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.587, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:07.587, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:07.587, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.587, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.618, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:07.618, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:07.619, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.619, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.657, level=WARN, class = Host, msg = Match timeout 38 ms(0)
time=06/07 09:47:07.657, level=WARN, class = BtromHandshake, timeout = 38, message = response timeout
time=06/07 09:47:07.657, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.657, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.702, level=WARN, class = Host, msg = Match timeout 45 ms(0)
time=06/07 09:47:07.702, level=WARN, class = BtromHandshake, timeout = 45, message = response timeout
time=06/07 09:47:07.703, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.703, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.734, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:07.734, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:07.734, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.734, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.765, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:07.765, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:07.765, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.765, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.781, level=DEBUG, [COM4] Read(24)
time=06/07 09:47:07.781, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=06/07 09:47:07.781, level=INFO, [RxP 24] 24504149523338322C312A32450D0A24504149523130302C
time=06/07 09:47:07.796, level=DEBUG, [COM4] Read(30)
time=06/07 09:47:07.796, level=INFO, [RxP 30] 312C302A33410D0A24504149523036322C312C302A33460D0A2450414952
time=06/07 09:47:07.796, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:07.797, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.797, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.812, level=DEBUG, [COM4] Read(32)
time=06/07 09:47:07.812, level=INFO, [RxP 32] 3036322C352C302A33420D0A24504149523036322C332C302A33440D0A245041
time=06/07 09:47:07.827, level=DEBUG, [COM4] Read(22)
time=06/07 09:47:07.828, level=INFO, [RxP 22] 49523038302C302A32450D0A24504149523036362C31
time=06/07 09:47:07.828, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:07.828, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.828, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.843, level=DEBUG, [COM4] Read(32)
time=06/07 09:47:07.843, level=INFO, [RxP 32] 2C312C312C312C312C302A33420D0A24504149523038312A33330D0A24504149
time=06/07 09:47:07.859, level=DEBUG, [COM4] Read(11)
time=06/07 09:47:07.859, level=INFO, [RxP 11] 523439302C312A32410D0A
time=06/07 09:47:07.859, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:07.859, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.859, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.890, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:07.890, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:07.890, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.890, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.921, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:07.921, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:07.921, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.921, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.952, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:07.952, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:07.953, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.953, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:07.984, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:07.984, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:07.984, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:07.984, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.015, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:08.015, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:08.015, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.015, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.046, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:08.046, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:08.046, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.046, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.077, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:08.077, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:08.078, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.078, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.109, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:08.109, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:08.109, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.109, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.140, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:08.140, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:08.140, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.140, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.171, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:08.171, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:08.171, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.171, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.209, level=WARN, class = Host, msg = Match timeout 37 ms(0)
time=06/07 09:47:08.209, level=WARN, class = BtromHandshake, timeout = 37, message = response timeout
time=06/07 09:47:08.209, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.209, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.240, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:08.240, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:08.240, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.240, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.271, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:08.271, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:08.271, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.271, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.302, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:08.302, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:08.303, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.303, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.334, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:08.334, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:08.334, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.334, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.365, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:08.365, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:08.365, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.365, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.396, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:08.396, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:08.396, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.396, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.427, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:08.427, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:08.427, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.427, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.459, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:08.459, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:08.459, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.459, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.490, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:08.490, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:08.490, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.490, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.521, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:08.521, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:08.521, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.521, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.552, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:08.552, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:08.553, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.553, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.584, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:08.584, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:08.584, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.584, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.615, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:08.615, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:08.615, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.615, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.646, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:08.646, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:08.646, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.646, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.677, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:08.677, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:08.678, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.678, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.709, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:08.709, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:08.709, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.709, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.740, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:08.740, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:08.740, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.740, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.771, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:08.771, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:08.771, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.771, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.803, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:08.803, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:08.803, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.803, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.833, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:08.833, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:08.834, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.834, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.864, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:08.864, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:08.864, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.864, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.895, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:08.895, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:08.896, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.896, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.927, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:08.927, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:08.927, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.927, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.958, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:08.958, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:08.958, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.958, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:08.989, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:08.989, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:08.989, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:08.989, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.020, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:09.020, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:09.020, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.020, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.052, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:09.052, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:09.052, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.052, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.070, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:47:09.101, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:09.101, level=WARN, class = BtromHandshake, timeout = 48, message = response timeout
time=06/07 09:47:09.101, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.101, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.132, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:09.132, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:09.133, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.133, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.164, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:09.164, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:09.164, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.164, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.195, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:09.195, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:09.195, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.195, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.226, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:09.226, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:09.226, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.226, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.258, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=06/07 09:47:09.259, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=06/07 09:47:09.259, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.259, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.276, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:47:09.307, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:09.307, level=WARN, class = BtromHandshake, timeout = 48, message = response timeout
time=06/07 09:47:09.307, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.307, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.338, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:09.338, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:09.339, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.339, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.370, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:09.370, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:09.370, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.370, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.401, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:09.401, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:09.401, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.401, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.432, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:09.432, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:09.432, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.432, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.463, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:09.463, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:09.464, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.464, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.495, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:09.495, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:09.495, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.495, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.526, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:09.526, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:09.526, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.526, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.557, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:09.557, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:09.557, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.557, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.588, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:09.588, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:09.588, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.588, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.620, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:09.620, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:09.620, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.620, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.651, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:09.651, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:09.651, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.651, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.682, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:09.682, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:09.682, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.682, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.713, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:09.713, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:09.714, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.714, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.745, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:09.745, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:09.745, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.745, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.776, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:09.776, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:09.776, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.776, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.809, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=06/07 09:47:09.809, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=06/07 09:47:09.810, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.810, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.841, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:09.841, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:09.841, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.841, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.872, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:09.872, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:09.872, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.872, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.903, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:09.903, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:09.919, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.919, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.950, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:09.950, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:09.950, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.950, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:09.981, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:09.981, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:09.981, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:09.981, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.012, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:10.013, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:10.013, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.013, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.044, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:10.044, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:10.044, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.044, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.075, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:10.075, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:10.075, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.075, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.106, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:10.106, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:10.106, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.106, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.137, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:10.137, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:10.138, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.138, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.169, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:10.169, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:10.169, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.169, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.200, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:10.200, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:10.200, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.200, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.231, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:10.231, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:10.232, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.232, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.262, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:10.262, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:10.263, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.263, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.294, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:10.294, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:10.294, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.294, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.325, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:10.325, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:10.325, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.325, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.343, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=06/07 09:47:10.375, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:10.375, level=WARN, class = BtromHandshake, timeout = 49, message = response timeout
time=06/07 09:47:10.375, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.375, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.406, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:10.406, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:10.406, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.406, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.437, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:10.437, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:10.438, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.438, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.468, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:10.469, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:10.469, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.469, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.500, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:10.500, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:10.500, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.500, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.531, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:10.531, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:10.531, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.531, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.562, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:10.562, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:10.563, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.563, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.593, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:10.593, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:10.593, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.593, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.625, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:10.625, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:10.625, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.625, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.656, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:10.656, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:10.656, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.656, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.687, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:10.687, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:10.687, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.687, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.719, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:10.719, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:10.719, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.719, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.750, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:10.750, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:10.750, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.750, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.781, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:10.781, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:10.781, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.781, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.812, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:10.812, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:10.813, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.813, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.844, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:10.844, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:10.844, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.844, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.878, level=WARN, class = Host, msg = Match timeout 34 ms(0)
time=06/07 09:47:10.878, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=06/07 09:47:10.878, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.878, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.909, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:10.909, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:10.910, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.910, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.941, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:10.941, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:10.941, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.941, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:10.972, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:10.972, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:10.972, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:10.972, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.003, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:11.003, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:11.003, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.003, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.034, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:11.034, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:11.035, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.035, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.066, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:11.066, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:11.066, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.066, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.097, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:11.097, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:11.097, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.097, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.128, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:11.128, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:11.128, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.128, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.159, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:11.159, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:11.160, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.160, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.190, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:11.190, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:11.191, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.191, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.222, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:11.222, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:11.222, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.222, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.237, level=DEBUG, [COM4] Read(2)
time=06/07 09:47:11.237, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 36.74 B/s
time=06/07 09:47:11.237, level=INFO, [RxP 2] 2450
time=06/07 09:47:11.253, level=DEBUG, [COM4] Read(30)
time=06/07 09:47:11.253, level=INFO, [RxP 30] 4149523338322C312A32450D0A24504149523130302C312C302A33410D0A
time=06/07 09:47:11.253, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:11.253, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.253, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.269, level=DEBUG, [COM4] Read(32)
time=06/07 09:47:11.269, level=INFO, [RxP 32] 24504149523036322C312C302A33460D0A24504149523036322C352C302A3342
time=06/07 09:47:11.284, level=DEBUG, [COM4] Read(24)
time=06/07 09:47:11.284, level=INFO, [RxP 24] 0D0A24504149523036322C332C302A33440D0A2450414952
time=06/07 09:47:11.284, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:11.285, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.285, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.300, level=DEBUG, [COM4] Read(30)
time=06/07 09:47:11.300, level=INFO, [RxP 30] 3038302C302A32450D0A24504149523036362C312C312C312C312C312C30
time=06/07 09:47:11.316, level=DEBUG, [COM4] Read(31)
time=06/07 09:47:11.316, level=INFO, [RxP 31] 2A33420D0A24504149523038312A33330D0A24504149523439302C312A3241
time=06/07 09:47:11.316, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:11.316, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.316, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.331, level=DEBUG, [COM4] Read(2)
time=06/07 09:47:11.331, level=INFO, [RxP 2] 0D0A
time=06/07 09:47:11.362, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:11.362, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:11.362, level=DEBUG, [COM4] Read(3)
time=06/07 09:47:11.362, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.362, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.362, level=INFO, [RxP 3] 245041
time=06/07 09:47:11.378, level=DEBUG, [COM4] Read(14)
time=06/07 09:47:11.378, level=INFO, [RxP 14] 49523036322C332C312A33430D0A
time=06/07 09:47:11.411, level=WARN, class = Host, msg = Match timeout 33 ms(0)
time=06/07 09:47:11.411, level=WARN, class = BtromHandshake, timeout = 48, message = response timeout
time=06/07 09:47:11.412, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.412, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.442, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:11.443, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:11.443, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.443, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.474, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:11.474, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:11.474, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.474, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.505, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:11.505, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:11.505, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.505, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.536, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:11.536, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:11.537, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.537, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.567, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:11.568, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:11.568, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.568, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.583, level=DEBUG, [COM4] Read(24)
time=06/07 09:47:11.583, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 480.01 B/s
time=06/07 09:47:11.583, level=INFO, [RxP 24] 24504149523030322A33380D0A24504149523439312A3336
time=06/07 09:47:11.599, level=DEBUG, [COM4] Read(2)
time=06/07 09:47:11.599, level=INFO, [RxP 2] 0D0A
time=06/07 09:47:11.599, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:11.599, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.599, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.630, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:11.630, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:11.630, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.630, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.661, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:11.661, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:11.661, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.661, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.692, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:11.692, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:11.693, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.693, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.724, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:11.724, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:11.724, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.724, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.755, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:11.755, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:11.755, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.755, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.771, level=DEBUG, [COM4] Read(4)
time=06/07 09:47:11.771, level=INFO, [RxP 4] 24504149
time=06/07 09:47:11.786, level=DEBUG, [COM4] Read(22)
time=06/07 09:47:11.786, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 29.55 B/s
time=06/07 09:47:11.786, level=INFO, [RxP 22] 523030322A33380D0A24504149523439312A33360D0A
time=06/07 09:47:11.786, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:11.787, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.787, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.817, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:11.817, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:11.818, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.818, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.849, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:11.849, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:11.849, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.849, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.880, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:11.880, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:11.880, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.880, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.911, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:11.911, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:11.911, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.911, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.929, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:47:11.960, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:11.960, level=WARN, class = BtromHandshake, timeout = 48, message = response timeout
time=06/07 09:47:11.960, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.960, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:11.991, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:11.991, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:11.991, level=DEBUG, [COM4] Read(16)
time=06/07 09:47:11.991, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=06/07 09:47:11.991, level=INFO, [RxP 16] 24504149523030322A33380D0A245041
time=06/07 09:47:11.991, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:11.991, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.007, level=DEBUG, [COM4] Read(10)
time=06/07 09:47:12.007, level=INFO, [RxP 10] 49523439312A33360D0A
time=06/07 09:47:12.038, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:12.038, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:12.038, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.038, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.069, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:12.069, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:12.069, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.070, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.100, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:12.100, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:12.101, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.101, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.132, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:12.132, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:12.132, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.132, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.163, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:12.163, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:12.163, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.163, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.194, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:12.194, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:12.194, level=DEBUG, [COM4] Read(26)
time=06/07 09:47:12.194, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 49.18 B/s
time=06/07 09:47:12.194, level=INFO, [RxP 26] 24504149523030322A33380D0A24504149523439312A33360D0A
time=06/07 09:47:12.194, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.194, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.225, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:12.225, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:12.226, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.226, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.257, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:12.257, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:12.257, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.257, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.288, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:12.288, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:12.288, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.288, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.319, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:12.319, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:12.319, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.319, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.350, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:12.350, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:12.351, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.351, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.382, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:12.382, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:12.382, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.382, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.382, level=DEBUG, [COM4] Read(10)
time=06/07 09:47:12.382, level=INFO, [RxP 10] 24504149523030322A33
time=06/07 09:47:12.397, level=DEBUG, [COM4] Read(16)
time=06/07 09:47:12.397, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 49.24 B/s
time=06/07 09:47:12.397, level=INFO, [RxP 16] 380D0A24504149523439312A33360D0A
time=06/07 09:47:12.429, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:12.429, level=WARN, class = BtromHandshake, timeout = 47, message = response timeout
time=06/07 09:47:12.430, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.430, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.463, level=WARN, class = Host, msg = Match timeout 33 ms(0)
time=06/07 09:47:12.463, level=WARN, class = BtromHandshake, timeout = 33, message = response timeout
time=06/07 09:47:12.463, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.463, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.494, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:12.494, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:12.494, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.494, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.525, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:12.525, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:12.525, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.525, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.556, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:12.556, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:12.557, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.557, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.588, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:12.588, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:12.588, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:12.588, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:12.588, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.588, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.619, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:12.619, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:12.619, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.619, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.650, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:12.650, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:12.651, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.651, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.673, level=WARN, class = Host, msg = Match timeout 22 ms(0)
time=06/07 09:47:12.705, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:12.705, level=WARN, class = BtromHandshake, timeout = 54, message = response timeout
time=06/07 09:47:12.705, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.705, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.736, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:12.736, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:12.736, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.736, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.767, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:12.767, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:12.767, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.767, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.783, level=DEBUG, [COM4] Read(6)
time=06/07 09:47:12.783, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 33.71 B/s
time=06/07 09:47:12.783, level=INFO, [RxP 6] 245041495234
time=06/07 09:47:12.799, level=DEBUG, [COM4] Read(7)
time=06/07 09:47:12.799, level=INFO, [RxP 7] 39312A33360D0A
time=06/07 09:47:12.799, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:12.799, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.799, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.830, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:12.830, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:12.830, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.830, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.861, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:12.861, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:12.861, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.861, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.892, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:12.892, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:12.892, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.892, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.923, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:12.923, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:12.924, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.924, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.954, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:12.954, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:12.955, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.955, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:12.981, level=WARN, class = Host, msg = Match timeout 25 ms(0)
time=06/07 09:47:12.996, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:12.996, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 32.81 B/s
time=06/07 09:47:12.996, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:12.996, level=WARN, class = BtromHandshake, timeout = 41, message = response timeout
time=06/07 09:47:12.996, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:12.997, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.027, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:13.027, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:13.028, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.028, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.059, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:13.059, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:13.059, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.059, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.090, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:13.090, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:13.090, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.090, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.121, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:13.121, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:13.121, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.121, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.152, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:13.152, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:13.153, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.153, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.184, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:13.184, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:13.184, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.184, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.199, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:13.199, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=06/07 09:47:13.199, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:13.231, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:13.231, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:13.231, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.231, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.262, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:13.262, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:13.262, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.262, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.293, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:13.293, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:13.293, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.293, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.324, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:13.324, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:13.325, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.325, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.355, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:13.355, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:13.356, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.356, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.387, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:13.387, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:13.387, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:13.387, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:13.387, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.387, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.418, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:13.418, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:13.418, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.418, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.449, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:13.449, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:13.449, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.449, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.480, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:13.480, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:13.481, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.481, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.512, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:13.512, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:13.512, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.512, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.531, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=06/07 09:47:13.562, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:13.562, level=WARN, class = BtromHandshake, timeout = 50, message = response timeout
time=06/07 09:47:13.563, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.563, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.593, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:13.593, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:13.594, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.594, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.609, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:13.609, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 31.72 B/s
time=06/07 09:47:13.609, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:13.640, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:13.640, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:13.641, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.641, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.673, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=06/07 09:47:13.673, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=06/07 09:47:13.674, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.674, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.704, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:13.704, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:13.704, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.704, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.736, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:13.736, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:13.736, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.736, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.766, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:13.766, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:13.766, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.766, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.797, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:13.797, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:13.797, level=DEBUG, [COM4] Read(8)
time=06/07 09:47:13.797, level=DEBUG, class = Host, drop_size = 8 2450414952343931
time=06/07 09:47:13.797, level=INFO, [RxP 8] 2450414952343931
time=06/07 09:47:13.798, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.798, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.812, level=DEBUG, [COM4] Read(5)
time=06/07 09:47:13.812, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 39.38 B/s
time=06/07 09:47:13.812, level=INFO, [RxP 5] 2A33360D0A
time=06/07 09:47:13.844, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:13.844, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:13.844, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.844, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.875, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:13.875, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:13.875, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.875, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.906, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:13.906, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:13.906, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.906, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.937, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:13.937, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:13.937, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.937, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.968, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:13.968, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:13.968, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.968, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:13.999, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:13.999, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:13.999, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:13.999, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:13.999, level=DEBUG, class = Host, drop_size = 0 49523130302C312C302A33410D
time=06/07 09:47:13.999, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:13.999, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.030, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:14.030, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:14.030, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.030, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.075, level=WARN, class = Host, msg = Match timeout 44 ms(0)
time=06/07 09:47:14.075, level=WARN, class = BtromHandshake, timeout = 44, message = response timeout
time=06/07 09:47:14.075, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.075, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.106, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:14.106, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:14.107, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.107, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.138, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:14.138, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:14.138, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.138, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.169, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:14.169, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:14.169, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.169, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.200, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:14.200, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:14.200, level=DEBUG, [COM4] Read(4)
time=06/07 09:47:14.200, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 33.53 B/s
time=06/07 09:47:14.200, level=INFO, [RxP 4] 24504149
time=06/07 09:47:14.200, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.200, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.216, level=DEBUG, [COM4] Read(9)
time=06/07 09:47:14.216, level=INFO, [RxP 9] 523439312A33360D0A
time=06/07 09:47:14.247, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:14.247, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:14.247, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.247, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.278, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:14.278, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:14.278, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.278, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.309, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:14.309, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:14.309, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.309, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.341, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:14.341, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:14.341, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.341, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.372, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:14.372, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:14.372, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.372, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.403, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:14.403, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:14.403, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:14.403, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 44.31 B/s
time=06/07 09:47:14.403, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.403, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.403, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:14.434, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:14.434, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:14.435, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.435, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.465, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:14.465, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:14.465, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.465, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.497, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:14.497, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:14.497, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.497, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.528, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:14.528, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:14.528, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.528, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.566, level=WARN, class = Host, msg = Match timeout 37 ms(0)
time=06/07 09:47:14.566, level=WARN, class = BtromHandshake, timeout = 37, message = response timeout
time=06/07 09:47:14.566, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.566, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.597, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:14.597, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:14.597, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.597, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.613, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:14.613, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=06/07 09:47:14.613, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:14.644, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:14.644, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:14.644, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.644, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.675, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:14.675, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:14.675, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.675, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.706, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:14.706, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:14.707, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.707, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.737, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:14.737, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:14.738, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.738, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.769, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:14.769, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:14.769, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.769, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.800, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:14.800, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:14.800, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.800, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.800, level=DEBUG, [COM4] Read(12)
time=06/07 09:47:14.800, level=INFO, [RxP 12] 24504149523439312A33360D
time=06/07 09:47:14.816, level=DEBUG, [COM4] Read(1)
time=06/07 09:47:14.816, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 59.07 B/s
time=06/07 09:47:14.816, level=INFO, [RxP 1] 0A
time=06/07 09:47:14.847, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:14.847, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:14.847, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.847, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.878, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:14.878, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:14.878, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.878, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.909, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:14.909, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:14.909, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.909, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.941, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:14.941, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:14.941, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.941, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:14.972, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:14.972, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:14.972, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:14.972, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.003, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:15.003, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:15.003, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.003, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.019, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:15.019, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=06/07 09:47:15.019, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:15.050, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:15.050, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:15.050, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.050, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.081, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:15.081, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:15.081, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.081, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.116, level=WARN, class = Host, msg = Match timeout 34 ms(0)
time=06/07 09:47:15.116, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=06/07 09:47:15.116, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.116, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.147, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:15.147, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:15.148, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.148, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.178, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:15.178, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:15.179, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.179, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.210, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:15.210, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:15.210, level=DEBUG, [COM4] Read(6)
time=06/07 09:47:15.210, level=INFO, [RxP 6] 245041495234
time=06/07 09:47:15.210, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.210, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.226, level=DEBUG, [COM4] Read(7)
time=06/07 09:47:15.226, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 29.01 B/s
time=06/07 09:47:15.226, level=INFO, [RxP 7] 39312A33360D0A
time=06/07 09:47:15.257, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:15.257, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:15.257, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.257, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.288, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:15.288, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:15.288, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.288, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.319, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:15.319, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:15.320, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.320, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.350, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:15.350, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:15.351, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.351, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.382, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:15.382, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:15.382, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.382, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.413, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:15.413, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:15.413, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:15.413, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.413, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.413, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:15.444, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:15.444, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:15.444, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.444, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.475, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:15.475, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:15.476, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.476, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.507, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:15.507, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:15.507, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.507, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.538, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:15.538, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:15.538, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.538, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.569, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:15.569, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:15.569, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.569, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.600, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:15.600, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:15.600, level=DEBUG, [COM4] Read(2)
time=06/07 09:47:15.600, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 34.67 B/s
time=06/07 09:47:15.601, level=INFO, [RxP 2] 2450
time=06/07 09:47:15.601, level=DEBUG, class = Host, drop_size = 2 2450
time=06/07 09:47:15.601, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.601, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.616, level=DEBUG, [COM4] Read(11)
time=06/07 09:47:15.616, level=INFO, [RxP 11] 4149523439312A33360D0A
time=06/07 09:47:15.650, level=WARN, class = Host, msg = Match timeout 34 ms(0)
time=06/07 09:47:15.650, level=WARN, class = BtromHandshake, timeout = 49, message = response timeout
time=06/07 09:47:15.650, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.650, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.681, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:15.681, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:15.682, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.682, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.712, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:15.712, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:15.713, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.713, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.744, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:15.744, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:15.744, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.744, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.775, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:15.775, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:15.775, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.775, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.806, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:15.806, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:15.806, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:15.806, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 53.44 B/s
time=06/07 09:47:15.806, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.806, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.807, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:15.837, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:15.837, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:15.838, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.838, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.869, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:15.869, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:15.869, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.869, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.900, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:15.900, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:15.900, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.900, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.931, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:15.931, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:15.932, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.932, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.962, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:15.962, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:15.963, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.963, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:15.994, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:15.994, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:15.994, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:15.994, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.025, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:16.025, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:16.025, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.025, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.025, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:16.025, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=06/07 09:47:16.025, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:16.056, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:16.056, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:16.056, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.056, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.087, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:16.087, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:16.088, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.088, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.119, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:16.119, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:16.119, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.119, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.149, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:16.149, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:16.149, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.149, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.168, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=06/07 09:47:16.199, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:16.199, level=WARN, class = BtromHandshake, timeout = 49, message = response timeout
time=06/07 09:47:16.199, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.199, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.215, level=DEBUG, [COM4] Read(8)
time=06/07 09:47:16.215, level=INFO, [RxP 8] 2450414952343931
time=06/07 09:47:16.230, level=DEBUG, [COM4] Read(5)
time=06/07 09:47:16.230, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 39.01 B/s
time=06/07 09:47:16.230, level=INFO, [RxP 5] 2A33360D0A
time=06/07 09:47:16.230, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:16.231, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.231, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.261, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:16.261, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:16.262, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.262, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.293, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:16.293, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:16.293, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.293, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.324, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:16.324, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:16.324, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.324, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.355, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:16.355, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:16.355, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.355, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.386, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:16.386, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:16.387, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.387, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.418, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:16.418, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:16.418, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.418, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.418, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:16.418, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:16.449, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:16.449, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:16.449, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.449, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.480, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:16.480, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:16.480, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.480, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.511, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:16.511, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:16.512, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.512, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.543, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:16.543, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:16.543, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.543, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.574, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:16.574, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:16.574, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.574, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.605, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:16.605, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:16.605, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.605, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.605, level=DEBUG, [COM4] Read(4)
time=06/07 09:47:16.605, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 34.68 B/s
time=06/07 09:47:16.605, level=INFO, [RxP 4] 24504149
time=06/07 09:47:16.621, level=DEBUG, [COM4] Read(9)
time=06/07 09:47:16.621, level=INFO, [RxP 9] 523439312A33360D0A
time=06/07 09:47:16.652, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:16.652, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:16.652, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.652, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.683, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:16.683, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:16.683, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.683, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.716, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=06/07 09:47:16.716, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=06/07 09:47:16.717, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.717, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.748, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:16.748, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:16.748, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.748, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.779, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:16.779, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:16.779, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.779, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.810, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:16.810, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:16.810, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.810, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.826, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:16.826, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 40.80 B/s
time=06/07 09:47:16.826, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:16.857, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:16.857, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:16.857, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.857, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.888, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:16.888, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:16.888, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.888, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.919, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:16.919, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:16.920, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.920, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.951, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:16.951, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:16.951, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.951, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:16.982, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:16.982, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:16.982, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:16.982, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:17.013, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:17.013, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:47:17.013, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:17.013, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:17.029, level=DEBUG, [COM4] Read(13)
time=06/07 09:47:17.029, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=06/07 09:47:17.029, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=06/07 09:47:17.060, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:47:17.060, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=06/07 09:47:17.060, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:17.060, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:17.091, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:17.091, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:17.091, level=DEBUG, [COM4] Write(4)
time=06/07 09:47:17.091, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:47:17.122, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:47:17.122, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:47:17.123, level=ERROR, class = BtromHandshake, result = fail, timeout = 10006, error_message = btrom handshake timeout
time=06/07 09:47:17.124, level=ERROR, D:\project\crossover\src\task\flash\download_da_uart.cc:141(-1): class = DownloadDa_UART, error_message = btrom handshake fail
time=06/07 09:47:17.124, level=ERROR, class = Task, task_name = DownloadDa_UART, error_message = Fail to execute RunTask()
time=06/07 09:47:17.124, level=DEBUG, class = DownloadDa_UART, task_time = 10.021
time=06/07 09:47:17.124, level=DEBUG, class = Controller, RemoveObserver = callback
time=06/07 09:47:17.124, level=DEBUG, class = CallbackManager, deregister = callback
time=06/07 09:47:17.124, level=DEBUG, class = DisconnectDUT, state = init data, result = ok
time=06/07 09:47:17.247, level=DEBUG, class = SerialHost, state = close
time=06/07 09:47:17.247, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=06/07 09:47:17.247, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=06/07 09:47:17.247, level=DEBUG, class = UartDev, state = disconnect
time=06/07 09:47:17.247, level=DEBUG, class = DisconnectDUT, state = disconnect, device_name = DUT, device_type = UART
time=06/07 09:47:17.247, level=DEBUG, class = DisconnectDUT, task_time = 0.123
time=06/07 09:47:17.247, level=DEBUG, class = LogService, state = CloseLogFile, pass_fail = 1, postfix = 
