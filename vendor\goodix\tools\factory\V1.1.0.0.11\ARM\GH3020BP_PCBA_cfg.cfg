[LPThreshold]
#LED,  PD,   TESTE<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, LLPRATIO, 
LED0,  PD0,  0,      19.0,  1.0,    0.1,      
LED0,  PD1,  0,      19.0,  1.0,    0.1,      
LED0,  PD2,  0,      19.0,  1.0,    0.1,      
LED0,  PD3,  0,      19.0,  1.0,    0.1,      
                                        
LED1,  PD0,  0,      26.0,  0.8,    0.1,      
LED1,  PD1,  0,      26.0,  0.8,    0.1,      
LED1,  PD2,  0,      26.0,  0.8,    0.1,      
LED1,  PD3,  0,      26.0,  0.8,    0.1,      
                                        
LED2,  PD0,  0,      26.0,  0.8,    0.1,      
LED2,  PD1,  0,      26.0,  0.8,    0.1,      
LED2,  PD2,  0,      26.0,  0.8,    0.1,      
LED2,  PD3,  0,      26.0,  0.8,    0.1,      
                                        
LED3,  PD0,  0,      26.0,  0.8,    0.1,      
LED3,  PD1,  0,      26.0,  0.8,    0.1,      
LED3,  PD2,  0,      26.0,  0.8,    0.1,      
LED3,  PD3,  0,      26.0,  0.8,    0.1,      
                                        
LED4,  PD0,  0,      26.0,  0.8,    0.1,      
LED4,  PD1,  0,      26.0,  0.8,    0.1,      
LED4,  PD2,  0,      26.0,  0.8,    0.1,      
LED4,  PD3,  0,      26.0,  0.8,    0.1,      
                                        
LED5,  PD0,  1,      1.5,   0.0,    0.4,      
LED5,  PD1,  0,      26.0,  0.8,    0.1,      
LED5,  PD2,  0,      26.0,  0.8,    0.1,      
LED5,  PD3,  0,      26.0,  0.8,    0.1,      
                                        
LED6,  PD0,  1,      10.0,  0.0,    0.7,      
LED6,  PD1,  0,      26.0,  0.8,    0.1,      
LED6,  PD2,  0,      26.0,  0.8,    0.1,      
LED6,  PD3,  0,      26.0,  0.8,    0.1,      
                                
LED7,  PD0,  0,      26.0,  0.8,    0.1,      
LED7,  PD1,  0,      26.0,  0.8,    0.1,      
LED7,  PD2,  0,      26.0,  0.8,    0.1,      
LED7,  PD3,  0,      26.0,  0.8,    0.1,      

[PPGTestCFG]
#TESTITEM, S_NUM, S_SKIPNUM, S_MUTIPLE, TIA_RF, TIA_CF, ADC_N, BG_LVL, ACG, LEDDRV, SLOT_TMR, FIFO_LINE,
CTR,       100,   0,         4,         5,      2,      6,     2,      0,   102,    985,      32,
NOISE,     100,   100,       4,         5,      2,      6,     2,      3,   0,      985,      32,
BASENOISE, 100,   0,         4,         4,      2,      6,     0,      0,   0,      985,      32,
LEAKLIGHT, 20,    0,         4,         5,      2,      6,     2,      0,   102,    985,      32,

[HighPassFilterCFG]
# fs= 200 ,fc= 0.6 ,N= 6
#a,1.00000000000000,-5.92717111995484,14.63850288710803,-19.28223946869565,14.28741321401177,-5.64626396159812,0.92975844917206,
#b,0.96423982969594,-5.78543897817567,14.46359744543917,-19.28479659391889,14.46359744543917,-5.78543897817567,0.96423982969594,
# fs= 200 ,fc= 0.8 ,N= 6
a,1.00000000000000,-5.90289504587093,14.51917869076417,-19.04762026749797,14.05674204734917,-5.53286278826866,0.90745736376437,
b,0.95260556567993,-5.71563339407955,14.28908348519888,-19.05211131359851,14.28908348519888,-5.71563339407955,0.95260556567993,

[PressureTestCFG]
#TESTITEM, PRESSURE(g), S_NUM, S_SKIPNUM, PID_limit, PID_Kp, PID_Ki, PID_Kd, CLK_Period, MOVE_OFFSET,
A0,        0,           200,   1,         1,         20,     0,      0,      4,          0,
At1,       40,          20,    1,         1,         20,     0,      0,      4,          0,
At2,       80,          20,    1,         1,         20,     0,      0,      4,          0,
A1,        120,         20,    1,         1,         20,     0,      0,      4,          0,
At3,       80,          20,    1,         1,         20,     0,      0,      4,          0,
At4,       40,          20,    1,         1,         20,     0,      0,      4,          0,
At5,       0,           20,    1,         1,         20,     0,      0,      4,          0,