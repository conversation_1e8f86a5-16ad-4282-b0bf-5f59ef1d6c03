/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   TimerAppShowView.h
@Time    :   2025/02/20 10:19:51
*
**************************************************************************/
#pragma once
#include "TimerAppShowViewModel.h"
#include "QwFaBtn/QwFaBtn.h"

class TimerAppShowView : public PageView
{
private:

	// Drawable
	QwScrollView view_;
	TextArea top_text;
	TextArea group_index_text;
	TextArea countdown_text;
	QwFaBtn fab_;

	// Notification bak
	Notification<uint32_t>* p_set_countdown_seconds_;
	Notification<uint8_t>* p_set_group_index_;
	
	// Notification Callback
	Callback<TimerAppShowView, uint32_t> on_set_countdown_seconds_;
	Callback<TimerAppShowView, uint8_t> on_set_group_index_;
	
	// ObserverDrawable bak
	ObserverDrawable<Drawable, uint32_t, 1>* p_countdown_seconds_;
	ObserverDrawable<Drawable, uint8_t, 1>* p_group_index_;
	
	// Drawable Update
	Callback<TimerAppShowView, Drawable*, Parameters<uint32_t>*, int> update_countdown_seconds_;
	Callback<TimerAppShowView, Drawable*, Parameters<uint8_t>*, int> update_group_index_;
	
	// custom variables
	Callback<TimerAppShowView, uint8_t> fab_click_evt_;

	uint8_t group_index;
	
protected:

public:
	TimerAppShowView(PageManager* manager);
	virtual ~TimerAppShowView();

	// PageView override
	void setup() override;
	void quit() override;

	// Screen override
	void handleTickEvent() override;
	void handleKeyEvent(uint8_t c) override;
	void handleClickEvent(const ClickEvent& evt) override;
	void handleDragEvent(const DragEvent& evt) override;
	void handleGestureEvent(const GestureEvent& evt) override;

	// Notification Callback function
	void on_click_set_countdown_seconds(uint32_t t1);
	void set_on_set_countdown_seconds(Notification<uint32_t>* command);

	void on_click_set_group_index(uint8_t t1);
	void set_on_set_group_index(Notification<uint8_t>* command);

	// ObserverDrawable Callback function
	void set_update_countdown_seconds(ObserverDrawable<Drawable, uint32_t, 1>* observer);
	void update_countdown_seconds(Drawable* ctrl, Parameters<uint32_t>* data, int idx);

	void set_update_group_index(ObserverDrawable<Drawable, uint8_t, 1>* observer);
	void update_group_index(Drawable* ctrl, Parameters<uint8_t>* data, int idx);

	// custom functions
	// 处理开始按钮点击事件
	void process_start_event();
	// 处理返回按钮点击事件
	void process_back_event();
	// 替换下划线为数字
	// char* replace_underscore_with_number(uint8_t number);
};

