time=05/29 12:23:18.752, level=INFO, class = LogService, tool_version = 0.2.12/air_bta_ic_premium_g3
time=05/29 12:23:18.749, level=INFO, class = Controller, SetLogLevel = 3
time=05/29 12:23:18.749, level=DEBUG, class = ToolLogLevel, log_level = 3
time=05/29 12:23:18.749, level=DEBUG, class = ToolLogLevel, task_time = 0.000
time=05/29 12:23:18.749, level=DEBUG, class = ConnectDUT, state = init data, result = ok
time=05/29 12:23:18.749, level=DEBUG, class = SerialHost, phy_name = UART, trans_name = h4
time=05/29 12:23:18.749, level=DEBUG, class = Physical, type = 1, state = create
time=05/29 12:23:18.749, level=DEBUG, class = Transport, type = 4, state = create
time=05/29 12:23:18.750, level=INFO, class = UartPhy, desc = USB Serial Port (COM4)
time=05/29 12:23:18.750, level=INFO, class = UartPhy, instance_id = FTDIBUS\VID_0403+PID_6015+DQ014GZWA\0000
time=05/29 12:23:18.750, level=INFO, class = UartPhy, port = 4, baud = 3000000, handshake = 0, read/timeout = (3, 0)
time=05/29 12:23:19.181, level=DEBUG, class = SerialHost, state = open
time=05/29 12:23:19.181, level=DEBUG, class = UartDev, state = connect, ret = 0
time=05/29 12:23:19.181, level=DEBUG, class = ConnectDUT, state = connect, device_name = DUT, device_type = UART, result = ok
time=05/29 12:23:19.181, level=DEBUG, class = ConnectDUT, task_time = 0.432
time=05/29 12:23:19.181, level=DEBUG, class = CallbackManager, register = DUT, filter = 0, cb = 0x005A2C00
time=05/29 12:23:19.181, level=DEBUG, class = UartDev, SetBaudrate = 115200
time=05/29 12:23:19.182, level=DEBUG, class = Host, SwitchTransport = bypass
time=05/29 12:23:19.182, level=DEBUG, class = Transport, type = 1, state = create
time=05/29 12:23:19.182, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=05/29 12:23:19.184, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.184, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:19.203, level=WARN, class = Host, msg = Match timeout 19 ms(0)
time=05/29 12:23:19.203, level=DEBUG, [COM4] Read(49)
time=05/29 12:23:19.203, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=05/29 12:23:19.203, level=INFO, [RxP 49] 24474E4747412C3233353934322E3031332C2C2C2C2C302C302C2C2C4D2C2C4D2C2C2A35460D0A24474E4753412C412C31
time=05/29 12:23:19.235, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:19.235, level=WARN, class = BtromHandshake, timeout = 51, message = response timeout
time=05/29 12:23:19.235, level=DEBUG, [COM4] Read(196)
time=05/29 12:23:19.235, level=INFO, [RxP 196] 2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C312A31440D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C322A31450D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C332A31460D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C342A31380D0A24474E524D432C3233353934322E3031332C562C2C2C2C2C2C2C3035303138302C2C2C4E2C562A32430D0A24474E5A44412C3233353934322E3031332C30352C30312C313938302C2C2A34350D0A
time=05/29 12:23:19.235, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.235, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:19.265, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:23:19.297, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:19.297, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/29 12:23:19.297, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.297, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:19.328, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:19.328, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:19.328, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.328, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:19.359, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:19.359, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:19.359, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.359, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:19.390, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:19.390, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:19.390, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.390, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:19.422, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:19.422, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:19.422, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.422, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:19.453, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:19.453, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:19.453, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.453, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:19.484, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:19.484, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:19.484, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.484, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:19.516, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:19.516, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:19.516, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.516, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:19.547, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:19.547, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:19.547, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.547, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:19.578, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:19.578, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:19.578, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.578, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:19.610, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:19.610, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:19.610, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.610, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:19.641, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:19.641, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:19.641, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.641, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:19.671, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:23:19.702, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:19.702, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/29 12:23:19.702, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.702, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:19.732, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:19.732, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:19.733, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.733, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:19.764, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:19.764, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:19.765, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.765, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:19.796, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:19.796, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:19.796, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.796, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:19.827, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:19.827, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:19.827, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.827, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:19.858, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:19.858, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:19.858, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.858, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:19.889, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:19.889, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:19.890, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.890, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:19.921, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:19.921, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:19.921, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.921, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:19.952, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:19.952, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:19.953, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.953, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:19.983, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:19.983, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:19.983, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:19.983, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.014, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:20.014, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:20.014, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.014, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.045, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:20.045, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:20.045, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.045, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.077, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:20.077, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:20.077, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.077, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.108, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:20.108, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:20.108, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.108, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.139, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:20.139, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:20.139, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.139, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.170, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:20.170, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:20.171, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.171, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.186, level=DEBUG, [COM4] Read(245)
time=05/29 12:23:20.186, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 199.44 B/s
time=05/29 12:23:20.186, level=INFO, [RxP 245] 24474E4747412C3233353934332E3031322C2C2C2C2C302C302C2C2C4D2C2C4D2C2C2A35460D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C312A31440D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C322A31450D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C332A31460D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C342A31380D0A24474E524D432C3233353934332E3031322C562C2C2C2C2C2C2C3035303138302C2C2C4E2C562A32430D0A24474E5A44412C3233353934332E3031322C30352C30312C313938302C2C2A34350D0A
time=05/29 12:23:20.216, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:20.216, level=WARN, class = BtromHandshake, timeout = 45, message = response timeout
time=05/29 12:23:20.217, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.217, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.247, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:20.247, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:20.248, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.248, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.279, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:20.279, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:20.279, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.279, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.310, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:20.310, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:20.310, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.310, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.341, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:20.341, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:20.341, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.341, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.372, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:20.372, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:20.373, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.373, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.404, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:20.404, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:20.404, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.404, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.435, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:20.435, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:20.435, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.435, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.465, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:20.465, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:20.466, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.466, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.496, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:20.496, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:20.497, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.497, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.527, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:20.527, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:20.527, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.527, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.558, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:20.558, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:20.558, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.558, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.590, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:20.590, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:20.591, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.591, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.621, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:20.621, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:20.622, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.622, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.652, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:20.652, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:20.652, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.652, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.684, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:20.684, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:20.684, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.684, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.715, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:20.715, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:20.715, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.715, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.746, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:20.746, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:20.746, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.746, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.778, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:20.778, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:20.778, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.778, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.808, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:20.808, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:20.809, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.809, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.839, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:20.839, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:20.839, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.839, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.870, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:20.870, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:20.871, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.871, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.901, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:20.901, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:20.901, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.901, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.932, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:20.932, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:20.933, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.933, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.964, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:20.964, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:20.964, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.964, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:20.994, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:20.994, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:20.995, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:20.995, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.025, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:21.025, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:21.025, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.025, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.056, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:21.056, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:21.057, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.057, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.088, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:21.088, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:21.088, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.088, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.119, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:21.119, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:21.120, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.120, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.149, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:23:21.181, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:21.181, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/29 12:23:21.181, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.181, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.196, level=DEBUG, [COM4] Read(245)
time=05/29 12:23:21.196, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=05/29 12:23:21.196, level=INFO, [RxP 245] 24474E4747412C3233353934342E3031322C2C2C2C2C302C302C2C2C4D2C2C4D2C2C2A35380D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C312A31440D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C322A31450D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C332A31460D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C342A31380D0A24474E524D432C3233353934342E3031322C562C2C2C2C2C2C2C3035303138302C2C2C4E2C562A32420D0A24474E5A44412C3233353934342E3031322C30352C30312C313938302C2C2A34320D0A
time=05/29 12:23:21.228, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:21.228, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=05/29 12:23:21.228, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.228, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.259, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:21.259, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:21.259, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.259, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.290, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:21.290, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:21.290, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.290, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.321, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:21.321, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:21.322, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.322, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.353, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:21.353, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:21.353, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.353, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.384, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:21.384, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:21.399, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.399, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.431, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:21.431, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:21.431, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.431, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.462, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:21.462, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:21.462, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.462, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.493, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:21.493, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:21.494, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.494, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.524, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:21.524, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:21.525, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.525, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.555, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:21.555, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:21.555, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.555, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.587, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:21.587, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:21.587, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.587, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.617, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:21.617, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:21.618, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.618, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.649, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:21.649, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:21.649, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.649, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.680, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:21.680, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:21.681, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.681, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.712, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:21.712, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:21.727, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.727, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.758, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:21.758, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:21.758, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.758, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.790, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:21.790, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:21.790, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.790, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.821, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:21.821, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:21.821, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.821, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.851, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:23:21.882, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:21.882, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/29 12:23:21.882, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.882, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.913, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:21.913, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:21.913, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.913, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.944, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:21.944, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:21.945, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.945, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:21.977, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=05/29 12:23:21.977, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=05/29 12:23:21.977, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:21.977, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.008, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:22.008, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:22.008, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.008, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.039, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:22.039, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:22.039, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.039, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.070, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:22.070, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:22.070, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.070, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.101, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:22.101, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:22.102, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.102, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.132, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:22.132, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:22.132, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.132, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.163, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:22.163, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:22.163, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.163, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.193, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:23:22.193, level=DEBUG, [COM4] Read(245)
time=05/29 12:23:22.193, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=05/29 12:23:22.193, level=INFO, [RxP 245] 24474E4747412C3233353934352E3031322C2C2C2C2C302C302C2C2C4D2C2C4D2C2C2A35390D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C312A31440D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C322A31450D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C332A31460D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C342A31380D0A24474E524D432C3233353934352E3031322C562C2C2C2C2C2C2C3035303138302C2C2C4E2C562A32410D0A24474E5A44412C3233353934352E3031322C30352C30312C313938302C2C2A34330D0A
time=05/29 12:23:22.193, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:22.193, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.193, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.224, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:22.224, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:22.225, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.225, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.255, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:22.255, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:22.255, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.255, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.286, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:22.286, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:22.286, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.286, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.317, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:22.317, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:22.318, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.318, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.348, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:22.348, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:22.349, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.349, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.380, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:22.380, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:22.380, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.380, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.411, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:22.411, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:22.411, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.411, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.442, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:22.442, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:22.442, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.442, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.473, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:22.473, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:22.473, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.473, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.504, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:22.504, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:22.504, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.504, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.535, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:22.535, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:22.535, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.535, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.566, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:22.566, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:22.567, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.567, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.597, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:22.597, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:22.597, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.597, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.629, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:22.629, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:22.629, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.629, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.660, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:22.660, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:22.661, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.661, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.691, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:22.691, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:22.692, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.692, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.723, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:22.723, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:22.723, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.723, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.753, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:22.753, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:22.754, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.754, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.785, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:22.785, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:22.785, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.785, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.816, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:22.816, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:22.816, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.816, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.847, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:22.847, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:22.847, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.847, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.879, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:22.879, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:22.879, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.879, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.909, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:22.910, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:22.910, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.910, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.940, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:22.940, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:22.941, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.941, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:22.972, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:22.972, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:22.972, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:22.972, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.003, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:23.003, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:23.003, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.003, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.033, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:23.033, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:23.034, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.034, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.065, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:23.065, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:23.065, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.065, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.096, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:23.096, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:23.097, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.097, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.127, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:23.127, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:23.128, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.128, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.159, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:23.159, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:23.159, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.159, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.190, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:23.190, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:23.190, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.190, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.190, level=DEBUG, [COM4] Read(245)
time=05/29 12:23:23.190, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=05/29 12:23:23.190, level=INFO, [RxP 245] 24474E4747412C3233353934362E3031312C2C2C2C2C302C302C2C2C4D2C2C4D2C2C2A35390D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C312A31440D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C322A31450D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C332A31460D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C342A31380D0A24474E524D432C3233353934362E3031312C562C2C2C2C2C2C2C3035303138302C2C2C4E2C562A32410D0A24474E5A44412C3233353934362E3031312C30352C30312C313938302C2C2A34330D0A
time=05/29 12:23:23.221, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:23.221, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:23.222, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.222, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.252, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:23.252, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:23.253, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.253, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.283, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:23.283, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:23.284, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.284, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.315, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:23.315, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:23.315, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.315, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.346, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:23.346, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:23.346, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.346, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.376, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:23.376, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:23.376, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.376, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.407, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:23.407, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:23.407, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.407, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.439, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:23.439, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:23.439, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.439, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.470, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:23.470, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:23.470, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.470, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.501, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:23.501, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:23.501, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.501, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.533, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:23.533, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:23.533, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.533, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.564, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:23.564, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:23.564, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.564, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.595, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:23.595, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:23.595, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.595, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.625, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:23.625, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:23.625, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.625, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.657, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:23.657, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:23.657, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.657, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.688, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:23.688, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:23.688, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.688, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.718, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:23.718, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:23.718, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.719, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.750, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:23.750, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:23.750, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.750, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.780, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:23.780, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:23.781, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.781, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.812, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:23.812, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:23.812, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.812, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.842, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:23:23.874, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:23.874, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/29 12:23:23.874, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.874, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.905, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:23.905, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:23.905, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.905, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.936, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:23.936, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:23.936, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.936, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.967, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:23.968, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:23.968, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.968, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:23.999, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:23.999, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:23.999, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:23.999, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.030, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:24.030, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:24.030, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.030, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.060, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:24.060, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:24.061, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.061, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.092, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:24.092, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:24.092, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.092, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.123, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:24.123, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:24.123, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.123, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.154, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:24.154, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:24.154, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.154, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.185, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:24.185, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:24.185, level=DEBUG, [COM4] Read(245)
time=05/29 12:23:24.186, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=05/29 12:23:24.186, level=INFO, [RxP 245] 24474E4747412C3233353934372E3031322C2C2C2C2C302C302C2C2C4D2C2C4D2C2C2A35420D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C312A31440D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C322A31450D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C332A31460D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C342A31380D0A24474E524D432C3233353934372E3031322C562C2C2C2C2C2C2C3035303138302C2C2C4E2C562A32380D0A24474E5A44412C3233353934372E3031322C30352C30312C313938302C2C2A34310D0A
time=05/29 12:23:24.186, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.186, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.217, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:24.217, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:24.217, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.217, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.247, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:24.247, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:24.248, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.248, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.279, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:24.279, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:24.279, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.279, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.310, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:24.310, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:24.310, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.310, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.341, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:24.341, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:24.342, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.342, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.372, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:24.372, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:24.372, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.372, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.402, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:24.402, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:24.403, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.403, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.434, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:24.434, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:24.434, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.434, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.464, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:24.464, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:24.465, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.465, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.496, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:24.496, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:24.496, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.496, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.527, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:24.527, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:24.527, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.527, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.557, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:24.557, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:24.558, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.558, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.589, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:24.589, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:24.589, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.589, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.620, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:24.620, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:24.620, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.620, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.651, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:24.651, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:24.651, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.651, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.683, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:24.683, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:24.683, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.683, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.714, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:24.714, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:24.714, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.714, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.745, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:24.745, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:24.745, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.745, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.775, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:24.775, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:24.776, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.776, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.806, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:24.806, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:24.807, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.807, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.838, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:24.838, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:24.838, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.838, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.868, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:23:24.899, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:24.899, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/29 12:23:24.899, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.899, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.931, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:24.931, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:24.931, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.931, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.961, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:24.961, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:24.961, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.961, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:24.992, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:24.992, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:24.992, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:24.992, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.024, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:25.024, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:25.024, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.024, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.056, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:25.056, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:25.056, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.056, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.087, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:25.087, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:25.087, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.087, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.118, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:25.118, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:25.118, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.118, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.149, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:25.150, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:25.150, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.150, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.180, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:25.180, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:25.181, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.181, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.196, level=DEBUG, [COM4] Read(245)
time=05/29 12:23:25.196, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=05/29 12:23:25.196, level=INFO, [RxP 245] 24474E4747412C3233353934382E3031322C2C2C2C2C302C302C2C2C4D2C2C4D2C2C2A35340D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C312A31440D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C322A31450D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C332A31460D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C342A31380D0A24474E524D432C3233353934382E3031322C562C2C2C2C2C2C2C3035303138302C2C2C4E2C562A32370D0A24474E5A44412C3233353934382E3031322C30352C30312C313938302C2C2A34450D0A
time=05/29 12:23:25.227, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:25.227, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=05/29 12:23:25.227, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.227, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.259, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:25.259, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:25.259, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.259, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.290, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:25.290, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:25.290, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.290, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.321, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:25.321, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:25.321, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.321, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.352, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:25.352, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:25.352, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.352, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.383, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:25.383, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:25.383, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.383, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.413, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:23:25.444, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:25.444, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/29 12:23:25.444, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.444, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.475, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:25.475, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:25.476, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.476, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.506, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:25.506, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:25.506, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.506, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.538, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:25.538, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:25.539, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.539, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.569, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:25.569, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:25.570, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.570, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.602, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:25.602, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:25.602, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.602, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.633, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:25.633, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:25.633, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.633, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.663, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:25.663, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:25.663, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.663, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.694, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:25.694, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:25.695, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.695, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.726, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:25.726, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:25.726, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.726, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.756, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:23:25.787, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:25.787, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/29 12:23:25.787, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.787, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.818, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:25.818, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:25.818, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.818, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.850, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:25.850, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:25.850, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.850, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.880, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:25.880, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:25.881, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.881, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.912, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:25.912, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:25.912, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.912, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.942, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:25.942, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:25.943, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:25.943, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:25.973, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:23:26.004, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:26.004, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=05/29 12:23:26.004, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.004, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.036, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:26.036, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:26.036, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.036, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.066, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:26.066, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:26.067, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.067, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.098, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:26.098, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:26.098, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.098, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.130, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:26.130, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:26.130, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.130, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.160, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:23:26.191, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:26.191, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/29 12:23:26.192, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.192, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.192, level=DEBUG, [COM4] Read(245)
time=05/29 12:23:26.192, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=05/29 12:23:26.192, level=INFO, [RxP 245] 24474E4747412C3233353934392E3031322C2C2C2C2C302C302C2C2C4D2C2C4D2C2C2A35350D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C312A31440D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C322A31450D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C332A31460D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C342A31380D0A24474E524D432C3233353934392E3031322C562C2C2C2C2C2C2C3035303138302C2C2C4E2C562A32360D0A24474E5A44412C3233353934392E3031322C30352C30312C313938302C2C2A34460D0A
time=05/29 12:23:26.223, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:26.223, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:26.223, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.223, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.255, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:26.255, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:26.255, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.255, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.286, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:26.286, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:26.286, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.286, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.317, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:26.317, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:26.317, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.317, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.348, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:26.348, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:26.348, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.348, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.379, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:26.379, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:26.380, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.380, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.410, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:26.410, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:26.411, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.411, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.441, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:26.441, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:26.441, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.441, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.472, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:26.472, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:26.473, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.473, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.504, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:26.504, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:26.504, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.504, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.536, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:26.536, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:26.536, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.536, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.567, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:26.567, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:26.567, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.567, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.598, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:26.598, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:26.599, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.599, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.630, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:26.630, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:26.630, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.630, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.661, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:26.661, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:26.661, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.661, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.691, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:26.691, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:26.692, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.692, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.723, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:26.723, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:26.723, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.723, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.754, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:26.754, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:26.754, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.754, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.785, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:26.785, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:26.785, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.785, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.816, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:26.816, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:26.816, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.816, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.847, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:26.847, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:26.847, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.847, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.878, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:26.878, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:26.879, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.879, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.910, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:26.910, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:26.910, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.910, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.940, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:26.940, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:26.941, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.941, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:26.971, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:26.971, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:26.971, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:26.971, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.002, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:27.002, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:27.002, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.002, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.033, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:27.033, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:27.033, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.033, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.064, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:27.064, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:27.064, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.064, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.095, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:27.095, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:27.095, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.095, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.126, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:27.126, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:27.126, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.126, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.157, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:27.157, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:27.157, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.157, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.189, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:27.189, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:27.189, level=DEBUG, [COM4] Read(245)
time=05/29 12:23:27.189, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=05/29 12:23:27.189, level=INFO, [RxP 245] 24474E4747412C3233353935302E3031322C2C2C2C2C302C302C2C2C4D2C2C4D2C2C2A35440D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C312A31440D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C322A31450D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C332A31460D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C342A31380D0A24474E524D432C3233353935302E3031322C562C2C2C2C2C2C2C3035303138302C2C2C4E2C562A32450D0A24474E5A44412C3233353935302E3031322C30352C30312C313938302C2C2A34370D0A
time=05/29 12:23:27.189, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.189, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.220, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:27.220, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:27.220, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.220, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.251, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:27.251, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:27.251, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.251, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.282, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:27.282, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:27.282, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.282, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.313, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:27.313, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:27.313, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.313, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.343, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:27.343, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:27.344, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.344, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.374, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:27.374, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:27.374, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.374, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.405, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:27.405, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:27.406, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.406, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.437, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:27.437, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:27.437, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.437, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.468, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:27.468, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:27.468, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.468, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.499, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:27.499, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:27.499, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.499, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.530, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:27.530, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:27.531, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.531, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.562, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:27.562, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:27.562, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.562, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.593, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:27.593, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:27.609, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.609, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.640, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:27.640, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:27.640, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.640, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.672, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:27.672, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:27.673, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.673, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.704, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:27.704, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:27.704, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.704, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.735, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:27.735, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:27.735, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.735, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.766, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:27.766, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:27.766, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.766, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.799, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=05/29 12:23:27.799, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=05/29 12:23:27.799, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.799, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.830, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:27.830, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:27.831, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.831, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.862, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:27.862, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:27.862, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.862, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.892, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:27.892, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:27.893, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.893, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.924, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:27.924, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:27.925, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.925, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.955, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:27.955, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:27.955, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.955, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:27.987, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:27.987, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:27.987, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:27.987, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.018, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:28.018, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:28.018, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.018, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.049, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:28.049, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:28.049, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.049, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.081, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:28.081, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:28.081, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.081, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.112, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:28.112, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:28.112, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.112, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.143, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:28.143, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:28.143, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.143, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.174, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:28.174, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:28.174, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.174, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.189, level=DEBUG, [COM4] Read(245)
time=05/29 12:23:28.189, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=05/29 12:23:28.189, level=INFO, [RxP 245] 24474E4747412C3233353935312E3031322C2C2C2C2C302C302C2C2C4D2C2C4D2C2C2A35430D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C312A31440D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C322A31450D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C332A31460D0A24474E4753412C412C312C2C2C2C2C2C2C2C2C2C2C2C2C2C2C2C342A31380D0A24474E524D432C3233353935312E3031322C562C2C2C2C2C2C2C3035303138302C2C2C4E2C562A32460D0A24474E5A44412C3233353935312E3031322C30352C30312C313938302C2C2A34360D0A
time=05/29 12:23:28.220, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:28.220, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=05/29 12:23:28.221, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.221, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.251, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:28.251, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:28.252, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.252, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.282, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:28.282, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:28.283, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.283, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.313, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:28.313, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:28.313, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.313, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.344, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:28.344, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:28.344, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.344, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.375, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:28.375, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:28.375, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.375, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.407, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:28.407, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:28.407, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.407, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.438, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:28.438, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:28.439, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.439, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.470, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:28.470, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:28.470, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.470, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.501, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:28.501, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:28.502, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.502, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.532, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:28.532, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:28.532, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.532, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.563, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:28.563, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:28.563, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.563, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.594, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:28.594, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:28.594, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.594, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.625, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:28.625, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:28.625, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.625, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.655, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=05/29 12:23:28.687, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=05/29 12:23:28.687, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=05/29 12:23:28.688, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.688, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.718, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:28.718, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:28.718, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.718, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.749, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:28.749, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:28.749, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.749, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.780, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:28.780, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:28.780, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.780, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.811, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:28.811, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:28.811, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.811, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.842, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:28.842, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:28.843, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.843, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.873, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:28.873, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:28.873, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.873, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.904, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:28.904, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:28.904, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.904, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.935, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:28.935, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:28.935, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.935, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.966, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:28.966, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:28.966, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.966, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:28.997, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:28.997, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:28.997, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:28.997, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:29.028, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:29.028, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:29.029, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:29.029, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:29.059, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:29.059, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:29.060, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:29.060, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:29.091, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=05/29 12:23:29.091, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=05/29 12:23:29.091, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:29.091, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:29.122, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:29.122, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:29.123, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:29.123, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:29.154, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:29.154, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:29.155, level=DEBUG, [COM4] Write(4)
time=05/29 12:23:29.155, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=05/29 12:23:29.186, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=05/29 12:23:29.186, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=05/29 12:23:29.186, level=ERROR, class = BtromHandshake, result = fail, timeout = 10002, error_message = btrom handshake timeout
time=05/29 12:23:29.186, level=ERROR, D:\project\crossover\src\task\flash\download_da_uart.cc:141(-1): class = DownloadDa_UART, error_message = btrom handshake fail
time=05/29 12:23:29.186, level=ERROR, class = Task, task_name = DownloadDa_UART, error_message = Fail to execute RunTask()
time=05/29 12:23:29.186, level=DEBUG, class = DownloadDa_UART, task_time = 10.005
time=05/29 12:23:29.186, level=DEBUG, class = Controller, RemoveObserver = callback
time=05/29 12:23:29.186, level=DEBUG, class = CallbackManager, deregister = callback
time=05/29 12:23:29.186, level=DEBUG, class = DisconnectDUT, state = init data, result = ok
time=05/29 12:23:29.295, level=DEBUG, class = SerialHost, state = close
time=05/29 12:23:29.295, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=05/29 12:23:29.295, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=05/29 12:23:29.295, level=DEBUG, class = UartDev, state = disconnect
time=05/29 12:23:29.295, level=DEBUG, class = DisconnectDUT, state = disconnect, device_name = DUT, device_type = UART
time=05/29 12:23:29.295, level=DEBUG, class = DisconnectDUT, task_time = 0.109
time=05/29 12:23:29.295, level=DEBUG, class = LogService, state = CloseLogFile, pass_fail = 1, postfix = 
