/*-----------------------------------------
ProductName         :Public              
SensorType          :GH3220              
HardwareID          :E1                  
ToolVersion         :T2.3.7              
DescriptionID       :D1.1                
ConfigurationID     :C3.0.1              
SaveTime            :202502111216        
-----------------------------------------*/
unsigned char GH3100DrvCfgInitInfo[57] = {
0x31, 0x36, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 
0x32, 0x36, 0x47, 0x48, 0x33, 0x32, 0x32, 0x30, 
0x33, 0x32, 0x45, 0x31, 0x34, 0x36, 0x54, 0x32, 
0x2e, 0x33, 0x2e, 0x37, 0x35, 0x34, 0x44, 0x31, 
0x2e, 0x31, 0x36, 0x36, 0x43, 0x33, 0x2e, 0x30, 
0x2e, 0x31, 0x37, 0x31, 0x32, 0x32, 0x30, 0x32, 
0x35, 0x30, 0x32, 0x31, 0x31, 0x31, 0x32, 0x31, 
0x36, 
};

/*-----------------------------------------
Configuration Version    : 0x0000
Tool Version             : 0x3197
Project Id               : 0x0000
Time                     : 2025-02-11 12:16:34.000
-----------------------------------------*/
unsigned char GH3100DrvCfg[1662] = {
0x07, 0x00, 0xac, 0x08, 0x11, 0x12, 0x05, 0x18, 0x08, 0x20, 0x08, 0x28,
0x2d, 0xa0, 0x03, 0xa8, 0x0d, 0xb0, 0x0c, 0xc0, 0x04, 0xd8, 0x05, 0xe8,
0x05, 0xf0, 0x01, 0xf8, 0x00, 0x00, 0x97, 0x31, 0x00, 0x00, 0x22, 0xfc,
0x3d, 0x2f, 0x00, 0x00, 0x00, 0x00, 0x81, 0x00, 0x00, 0x1f, 0xc1, 0x00,
0x00, 0x05, 0x41, 0x01, 0x00, 0xa0, 0xc1, 0x01, 0x00, 0x0d, 0x01, 0x20,
0x01, 0x00, 0x41, 0x20, 0x03, 0x02, 0x81, 0x20, 0x05, 0x04, 0x41, 0x21,
0x27, 0x7c, 0x01, 0x22, 0x0f, 0x14, 0x41, 0x22, 0x55, 0x55, 0x81, 0x22,
0x00, 0xaa, 0xc1, 0x23, 0x00, 0x19, 0x01, 0x24, 0x00, 0x19, 0x41, 0x24,
0x0c, 0xbf, 0xc1, 0x24, 0x27, 0x44, 0x01, 0x25, 0x00, 0x01, 0x41, 0x25,
0x00, 0x00, 0x81, 0x25, 0x0f, 0x16, 0xc1, 0x25, 0x44, 0x41, 0x01, 0x26,
0x00, 0x02, 0x41, 0x27, 0x03, 0x19, 0x81, 0x27, 0x03, 0x19, 0xc1, 0x27,
0x19, 0xbf, 0x41, 0x28, 0x27, 0x44, 0x81, 0x28, 0x00, 0x01, 0xc1, 0x28,
0x00, 0x00, 0x01, 0x29, 0x0f, 0x16, 0x41, 0x29, 0x44, 0x41, 0x81, 0x29,
0x00, 0x02, 0xc1, 0x2a, 0x02, 0x19, 0x01, 0x2b, 0x02, 0x19, 0x41, 0x2b,
0x19, 0xbf, 0xc1, 0x2b, 0x27, 0x44, 0x01, 0x2c, 0x00, 0x01, 0x41, 0x2c,
0x00, 0x00, 0x81, 0x2c, 0x0c, 0x10, 0xc1, 0x2c, 0x44, 0x42, 0x01, 0x2d,
0x00, 0x02, 0x41, 0x2e, 0x02, 0x07, 0x81, 0x2e, 0x02, 0x07, 0x41, 0x2f,
0x27, 0x44, 0x81, 0x2f, 0x00, 0x01, 0xc1, 0x2f, 0x00, 0x00, 0x01, 0x30,
0x00, 0x00, 0x41, 0x30, 0x44, 0x42, 0x81, 0x30, 0x00, 0x02, 0xc1, 0x31,
0x02, 0x07, 0x01, 0x32, 0x02, 0x07, 0xc1, 0x32, 0xc7, 0x04, 0x01, 0x33,
0x00, 0x01, 0x41, 0x33, 0x00, 0x00, 0x81, 0x33, 0x0c, 0x10, 0xc1, 0x33,
0x44, 0x42, 0x01, 0x34, 0x00, 0x02, 0xc1, 0x34, 0x00, 0x03, 0x41, 0x35,
0x02, 0x07, 0x81, 0x35, 0x02, 0x07, 0x81, 0x3d, 0x01, 0x72, 0xc1, 0x3d,
0x03, 0x4a, 0x01, 0x3e, 0x03, 0x4a, 0x41, 0x3e, 0x00, 0xe8, 0x81, 0x3e,
0x00, 0x6a, 0xc1, 0x3e, 0x00, 0xe8, 0x01, 0x40, 0x01, 0x20, 0x01, 0x50,
0x0a, 0x00, 0x41, 0x50, 0xf5, 0x30, 0x81, 0x50, 0x93, 0x88, 0xc1, 0x50,
0xc4, 0x5c, 0x01, 0x51, 0xa3, 0x28, 0x01, 0x81, 0x00, 0x01, 0x01, 0x82,
0x49, 0xf0, 0x41, 0x82, 0x00, 0x82, 0x81, 0x82, 0x49, 0xf0, 0xc1, 0x82,
0x00, 0x82, 0x41, 0x84, 0x00, 0x11, 0x81, 0x84, 0x09, 0x01, 0xc1, 0x84,
0x01, 0x02, 0x01, 0x85, 0x01, 0x02, 0x41, 0xa0, 0x4f, 0x60, 0x81, 0xa0,
0x00, 0x0a, 0xc1, 0xa0, 0x00, 0xa1, 0x01, 0xb1, 0x00, 0x08, 0x41, 0xd2,
0x02, 0x33, 0x81, 0xd2, 0x04, 0x00, 0x01, 0xd3, 0x23, 0x10, 0x01, 0xe0,
0x00, 0x00, 0xff, 0xff, 0x01, 0x01, 0x01, 0x01, 0x03, 0x00, 0x00, 0x00,
0x02, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00,
0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
0xa0, 0x80, 0x01, 0x80, 0x01, 0x21, 0x01, 0x42, 0x01, 0x63, 0x02, 0x80,
0x02, 0x21, 0x02, 0x42, 0x02, 0x63, 0x26, 0x80, 0x46, 0x01, 0x09, 0x80,
0x69, 0x01, 0x89, 0x02, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00,
0x10, 0x00, 0x10, 0x00, 0x00, 0x20, 0x01, 0x01, 0x02, 0x01, 0x06, 0x06,
0x09, 0x19, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0xff, 0xff, 0x02, 0x02,
0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x10, 0x00, 0x00, 0x00, 0x01, 0x01,
0x02, 0x00, 0x06, 0x06, 0x09, 0x19, 0x02, 0x02, 0x02, 0x02, 0x05, 0x01,
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x02, 0x00, 0x00,
0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x03, 0x00,
0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x0b, 0x06, 0x6c, 0x84, 0xfd, 0xff,
0xe3, 0x87, 0xff, 0xff, 0x38, 0xcc, 0x0f, 0x00, 0x01, 0x00, 0x00, 0x00,
0x00, 0x00, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00,
0x04, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
0x00, 0x00, 0x00, 0x00, 0xa0, 0x00, 0x05, 0x00, 0x00, 0x00, 0x01, 0x04,
0x01, 0x00, 0x01, 0x21, 0x01, 0x42, 0x01, 0x63, 0x02, 0x04, 0x01, 0x00,
0x01, 0x21, 0x01, 0x42, 0x01, 0x63, 0x06, 0x02, 0x02, 0x00, 0x04, 0x01,
0x07, 0x00, 0x00, 0x00, 0x02, 0x04, 0x68, 0x45, 0x40, 0x99, 0xd9, 0x00,
0x00, 0x88, 0x93, 0x00, 0x00, 0x5c, 0xc4, 0x00, 0x00, 0x28, 0xa3, 0x00,
0x01, 0x00, 0x00, 0x00, 0xf0, 0x49, 0x82, 0x00, 0x05, 0x00, 0x19, 0x10,
0x19, 0x20, 0x19, 0x60, 0x19, 0x90, 0x00, 0x00, 0xc8, 0x00, 0x01, 0x00,
0xff, 0xff, 0xff, 0xff, 0xdf, 0x85
};
/*-----------------------------------------
Register: 0x81,0x00,0x00,0x1f,0xc1,0x00,0x00,0x05,0x41,0x01,0x00,0xa0,0xc1,0x01,0x00,0x0d,0x01,0x20,0x01,0x00,0x41,0x20,0x03,0x02,0x81,0x20,0x05,0x04,0x41,0x21,0x27,0x7c,0x01,0x22,0x0f,0x14,0x41,0x22,0x55,0x55,0x81,0x22,0x00,0xaa,0xc1,0x23,0x00,0x19,0x01,0x24,0x00,0x19,0x41,0x24,0x0c,0xbf,0xc1,0x24,0x27,0x44,0x01,0x25,0x00,0x01,0x41,0x25,0x00,0x00,0x81,0x25,0x0f,0x16,0xc1,0x25,0x44,0x41,0x01,0x26,0x00,0x02,0x41,0x27,0x03,0x19,0x81,0x27,0x03,0x19,0xc1,0x27,0x19,0xbf,0x41,0x28,0x27,0x44,0x81,0x28,0x00,0x01,0xc1,0x28,0x00,0x00,0x01,0x29,0x0f,0x16,0x41,0x29,0x44,0x41,0x81,0x29,0x00,0x02,0xc1,0x2a,0x02,0x19,0x01,0x2b,0x02,0x19,0x41,0x2b,0x19,0xbf,0xc1,0x2b,0x27,0x44,0x01,0x2c,0x00,0x01,0x41,0x2c,0x00,0x00,0x81,0x2c,0x0c,0x10,0xc1,0x2c,0x44,0x42,0x01,0x2d,0x00,0x02,0x41,0x2e,0x02,0x07,0x81,0x2e,0x02,0x07,0x41,0x2f,0x27,0x44,0x81,0x2f,0x00,0x01,0xc1,0x2f,0x00,0x00,0x01,0x30,0x00,0x00,0x41,0x30,0x44,0x42,0x81,0x30,0x00,0x02,0xc1,0x31,0x02,0x07,0x01,0x32,0x02,0x07,0xc1,0x32,0xc7,0x04,0x01,0x33,0x00,0x01,0x41,0x33,0x00,0x00,0x81,0x33,0x0c,0x10,0xc1,0x33,0x44,0x42,0x01,0x34,0x00,0x02,0xc1,0x34,0x00,0x03,0x41,0x35,0x02,0x07,0x81,0x35,0x02,0x07,0x81,0x3d,0x01,0x72,0xc1,0x3d,0x03,0x4a,0x01,0x3e,0x03,0x4a,0x41,0x3e,0x00,0xe8,0x81,0x3e,0x00,0x6a,0xc1,0x3e,0x00,0xe8,0x01,0x40,0x01,0x20,0x01,0x50,0x0a,0x00,0x41,0x50,0xf5,0x30,0x81,0x50,0x93,0x88,0xc1,0x50,0xc4,0x5c,0x01,0x51,0xa3,0x28,0x01,0x81,0x00,0x01,0x01,0x82,0x49,0xf0,0x41,0x82,0x00,0x82,0x81,0x82,0x49,0xf0,0xc1,0x82,0x00,0x82,0x41,0x84,0x00,0x11,0x81,0x84,0x09,0x01,0xc1,0x84,0x01,0x02,0x01,0x85,0x01,0x02,0x41,0xa0,0x4f,0x60,0x81,0xa0,0x00,0x0a,0xc1,0xa0,0x00,0xa1,0x01,0xb1,0x00,0x08,0x41,0xd2,0x02,0x33,0x81,0xd2,0x04,0x00,0x01,0xd3,0x23,0x10,0x01,0xe0,0x00,0x00
addr: 0x0004, value: 0x001f, 
addr: 0x0006, value: 0x0005, 
addr: 0x000a, value: 0x00a0, 
addr: 0x000e, value: 0x000d, 
addr: 0x0100, value: 0x0100, 
addr: 0x0102, value: 0x0302, 
addr: 0x0104, value: 0x0504, 
addr: 0x010a, value: 0x277c, 
addr: 0x0110, value: 0x0f14, 
addr: 0x0112, value: 0x5555, 
addr: 0x0114, value: 0x00aa, 
addr: 0x011e, value: 0x0019, 
addr: 0x0120, value: 0x0019, 
addr: 0x0122, value: 0x0cbf, 
addr: 0x0126, value: 0x2744, 
addr: 0x0128, value: 0x0001, 
addr: 0x012a, value: 0x0000, 
addr: 0x012c, value: 0x0f16, 
addr: 0x012e, value: 0x4441, 
addr: 0x0130, value: 0x0002, 
addr: 0x013a, value: 0x0319, 
addr: 0x013c, value: 0x0319, 
addr: 0x013e, value: 0x19bf, 
addr: 0x0142, value: 0x2744, 
addr: 0x0144, value: 0x0001, 
addr: 0x0146, value: 0x0000, 
addr: 0x0148, value: 0x0f16, 
addr: 0x014a, value: 0x4441, 
addr: 0x014c, value: 0x0002, 
addr: 0x0156, value: 0x0219, 
addr: 0x0158, value: 0x0219, 
addr: 0x015a, value: 0x19bf, 
addr: 0x015e, value: 0x2744, 
addr: 0x0160, value: 0x0001, 
addr: 0x0162, value: 0x0000, 
addr: 0x0164, value: 0x0c10, 
addr: 0x0166, value: 0x4442, 
addr: 0x0168, value: 0x0002, 
addr: 0x0172, value: 0x0207, 
addr: 0x0174, value: 0x0207, 
addr: 0x017a, value: 0x2744, 
addr: 0x017c, value: 0x0001, 
addr: 0x017e, value: 0x0000, 
addr: 0x0180, value: 0x0000, 
addr: 0x0182, value: 0x4442, 
addr: 0x0184, value: 0x0002, 
addr: 0x018e, value: 0x0207, 
addr: 0x0190, value: 0x0207, 
addr: 0x0196, value: 0xc704, 
addr: 0x0198, value: 0x0001, 
addr: 0x019a, value: 0x0000, 
addr: 0x019c, value: 0x0c10, 
addr: 0x019e, value: 0x4442, 
addr: 0x01a0, value: 0x0002, 
addr: 0x01a6, value: 0x0003, 
addr: 0x01aa, value: 0x0207, 
addr: 0x01ac, value: 0x0207, 
addr: 0x01ec, value: 0x0172, 
addr: 0x01ee, value: 0x034a, 
addr: 0x01f0, value: 0x034a, 
addr: 0x01f2, value: 0x00e8, 
addr: 0x01f4, value: 0x006a, 
addr: 0x01f6, value: 0x00e8, 
addr: 0x0200, value: 0x0120, 
addr: 0x0280, value: 0x0a00, 
addr: 0x0282, value: 0xf530, 
addr: 0x0284, value: 0x9388, 
addr: 0x0286, value: 0xc45c, 
addr: 0x0288, value: 0xa328, 
addr: 0x0408, value: 0x0001, 
addr: 0x0410, value: 0x49f0, 
addr: 0x0412, value: 0x0082, 
addr: 0x0414, value: 0x49f0, 
addr: 0x0416, value: 0x0082, 
addr: 0x0422, value: 0x0011, 
addr: 0x0424, value: 0x0901, 
addr: 0x0426, value: 0x0102, 
addr: 0x0428, value: 0x0102, 
addr: 0x0502, value: 0x4f60, 
addr: 0x0504, value: 0x000a, 
addr: 0x0506, value: 0x00a1, 
addr: 0x0588, value: 0x0008, 
addr: 0x0692, value: 0x0233, 
addr: 0x0694, value: 0x0400, 
addr: 0x0698, value: 0x2310, 
addr: 0x0700, value: 0x0000, 
-----------------------------------------
Function Channel Map: 0xff,0xff,0x01,0x01,0x01,0x01,0x03,0x00,0x00,0x00,0x02,0x00,0x00,0x00,0x02,0x00,0x00,0x00,0x02,0x00,0x00,0x00,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xa0,0x80,0x01,0x80,0x01,0x21,0x01,0x42,0x01,0x63,0x02,0x80,0x02,0x21,0x02,0x42,0x02,0x63,0x26,0x80,0x46,0x01,0x09,0x80,0x69,0x01,0x89,0x02,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00
Function Id: 0, Channel Id: 0 (first channel) 
Function Id: 1, Channel Id: 0 (first channel) 
Function Id: 1, Channel Id: 1 
Function Id: 1, Channel Id: 2 
Function Id: 1, Channel Id: 3 
Function Id: 2, Channel Id: 0 (first channel) 
Function Id: 2, Channel Id: 1 
Function Id: 2, Channel Id: 2 
Function Id: 2, Channel Id: 3 
Function Id: 6, Channel Id: 0 (first channel) 
Function Id: 6, Channel Id: 1 
Function Id: 9, Channel Id: 0 (first channel) 
Function Id: 9, Channel Id: 1 
Function Id: 9, Channel Id: 2 
-----------------------------------------
Function Channel Extra Map: 
-----------------------------------------
Function Slot Map: 0x00,0x20,0x01,0x01,0x02,0x01,0x06,0x06,0x09,0x19
Function Id: 0, Slot Id: 32 
Function Id: 1, Slot Id: 1 
Function Id: 2, Slot Id: 1 
Function Id: 6, Slot Id: 6 
Function Id: 9, Slot Id: 25 

-----------------------------------------
SlotXLedDrvUse: 0x02,0x02,0x02,0x02,0x02,0x02,0xff,0xff,0x02,0x02,0x02,0x02,0x02,0x02,0x02,0x02
Slot0:  LedDrvUse:   2   MainDrv: 2
Slot1:  LedDrvUse:   2   MainDrv: 2
Slot2:  LedDrvUse:   2   MainDrv: 2
Slot3:  LedDrvUse:   2   MainDrv: 2
Slot4:  LedDrvUse:   2   MainDrv: 2
Slot5:  LedDrvUse:   2   MainDrv: 2
Slot6:  LedDrvUse: 255   MainDrv: 2
Slot7:  LedDrvUse: 255   MainDrv: 2

-----------------------------------------
SrForApp: 0x05,0x00,0x19,0x10,0x19,0x20,0x19,0x60,0x19,0x90
Function Id: 0, App sample rate: 5 Hz 
Function Id: 1, App sample rate: 25 Hz 
Function Id: 2, App sample rate: 25 Hz 
Function Id: 6, App sample rate: 25 Hz 
Function Id: 9, App sample rate: 25 Hz 

-----------------------------------------
GsensorCfg: 0x00,0x00,0xc8,0x00,0x01,0x00,0xff,0xff,0xff,0xff

-----------------------------------------
AdtCfgVec: 0x01,0x00,0x00,0x00,0xf0,0x49,0x82,0x00

-----------------------------------------
SoftAgcCfgVec: 0x07,0x00,0x00,0x00,0x02,0x04,0x68,0x45,0x40,0x99,0xd9,0x00,0x00,0x88,0x93,0x00,0x00,0x5c,0xc4,0x00,0x00,0x28,0xa3,0x00

-----------------------------------------
AlgoChnlCfgVec: 0x01,0x04,0x01,0x00,0x01,0x21,0x01,0x42,0x01,0x63,0x02,0x04,0x01,0x00,0x01,0x21,0x01,0x42,0x01,0x63,0x06,0x02,0x02,0x00,0x04,0x01

-----------------------------------------
Other package(Package ID: 5): 0x10,0x00,0x00,0x00,0x01,0x01,0x02,0x00,0x06,0x06,0x09,0x19,0x02,0x02,0x02,0x02

-----------------------------------------
Other package(Package ID: 20): 0x05,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x05,0x02,0x00,0x00,0x00,0x00,0x20,0x00,0x00,0x00,0x10,0x00,0x00,0x00,0x03,0x00,0x00,0x00,0x03,0x00,0x00,0x00,0x0b,0x06,0x6c,0x84,0xfd,0xff,0xe3,0x87,0xff,0xff,0x38,0xcc,0x0f,0x00,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1e,0x00,0x00,0x00,0x0f,0x00,0x00,0x00,0x04,0x00,0x00,0x00,0x03,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00

-----------------------------------------*/


--------------version cfg---------------------------
RegisterVersion: 0x00,0x00,0x97,0x31,0x00,0x00,0x22,0xfc,0x3d,0x2f,0x00,0x00,0x00,0x00
addr: 0x1000, value: 0x0000, 
addr: 0x1002, value: 0x3197, 
addr: 0x1004, value: 0x0000, 
addr: 0x1006, value: 0xfc22, 
addr: 0x1008, value: 0x2f3d, 
addr: 0x100a, value: 0x0001, 

--------------Function channel cfg---------------------------
Function Channel Map: 0xff,0xff,0x01,0x01,0x01,0x01,0x03,0x00,0x00,0x00,0x02,0x00,0x00,0x00,0x02,0x00,0x00,0x00,0x02,0x00,0x00,0x00,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xa0,0x80,0x01,0x80,0x01,0x21,0x01,0x42,0x01,0x63,0x02,0x80,0x02,0x21,0x02,0x42,0x02,0x63,0x26,0x80,0x46,0x01,0x09,0x80,0x69,0x01,0x89,0x02,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00,0x10,0x00
addr: 0x2000, value: 0x0001, 
addr: 0x2002, value: 0x00a2, 
addr: 0x2022, value: 0x0004, 
addr: 0x2024, value: 0x0901, 
addr: 0x2026, value: 0x1911, 
addr: 0x2044, value: 0x0004, 
addr: 0x2046, value: 0x0901, 
addr: 0x2048, value: 0x1911, 
addr: 0x20cc, value: 0x0002, 
addr: 0x20ce, value: 0x4223, 
addr: 0x2132, value: 0x0003, 
addr: 0x2134, value: 0x6201, 
addr: 0x2136, value: 0x0082, 

--------------Function channel Extra cfg---------------------------
Function Channel Extra Map: 

--------------Function slot cfg---------------------------
Function Slot Map: 

--------------LEDDrv cfg---------------------------
SlotXLedDrvUse: 0x02,0x02,0x02,0x02,0x02,0x02,0xff,0xff,0x02,0x02,0x02,0x02,0x02,0x02,0x02,0x02
addr: 0x10e0, value: 0x0202, 
addr: 0x10e2, value: 0x0202, 
addr: 0x10e4, value: 0x0202, 
addr: 0x10e6, value: 0xffff, 
addr: 0x10e8, value: 0x0202, 
addr: 0x10ea, value: 0x0202, 
addr: 0x10ec, value: 0x0202, 
addr: 0x10ee, value: 0x0202, 

--------------Fifo cfg---------------------------
Fifo cfgUse: 0xa0,0x00,0x05,0x00,0x00,0x00
addr: 0x1120, value: 0x00a0, 
addr: 0x1122, value: 0x0005, 
addr: 0x1124, value: 0x0000, 

--------------ADT cfg---------------------------
ADT cfg Use: 0x01,0x00,0x00,0x00,0xf0,0x49,0x82,0x00
addr: 0x3000, value: 0x0001, 
addr: 0x3002, value: 0x0000, 
addr: 0x3004, value: 0x49f0, 
addr: 0x3006, value: 0x0082, 

--------------FUNSR cfg---------------------------
FUNSR cfgUse: 0x05,0x00,0x19,0x10,0x19,0x20,0x19,0x60,0x19,0x90
addr: 0x2880, value: 0x0005, 
addr: 0x2882, value: 0x0019, 
addr: 0x2884, value: 0x0019, 
addr: 0x288c, value: 0x0019, 
addr: 0x2892, value: 0x0019, 

--------------Gsensor cfg---------------------------
Gsensor cfgUse: 0x00,0x00,0xc8,0x00,0x01,0x00,0xff,0xff,0xff,0xff
addr: 0x1140, value: 0x0000, 
addr: 0x1142, value: 0x00c8, 
addr: 0x1144, value: 0x0001, 

--------------SoftAgcCfg cfg---------------------------
SoftAgcCfg cfgUse: 0x07,0x00,0x00,0x00,0x02,0x04,0x68,0x45,0x40,0x99,0xd9,0x00,0x00,0x88,0x93,0x00,0x00,0x5c,0xc4,0x00,0x00,0x28,0xa3,0x00
addr: 0x1160, value: 0x0007, 
addr: 0x1162, value: 0x0000, 
addr: 0x1164, value: 0x0402, 
addr: 0x1166, value: 0x4568, 
addr: 0x1168, value: 0x9940, 
addr: 0x116a, value: 0x00d9, 
addr: 0x116c, value: 0x8800, 
addr: 0x116e, value: 0x0093, 
addr: 0x1170, value: 0x5c00, 
addr: 0x1172, value: 0x00c4, 
addr: 0x1174, value: 0x2800, 
addr: 0x1176, value: 0x00a3, 

--------------AlgoChnlCfg cfg---------------------------
AlgoChnlCfg cfgUse: 0x01,0x04,0x01,0x00,0x01,0x21,0x01,0x42,0x01,0x63,0x02,0x04,0x01,0x00,0x01,0x21,0x01,0x42,0x01,0x63,0x06,0x02,0x02,0x00,0x04,0x01
addr: 0x35c0, value: 0x0004, 
addr: 0x35c4, value: 0x0100, 
addr: 0x35c6, value: 0x0302, 
addr: 0x44c0, value: 0x0001, 
addr: 0x44cc, value: 0xff01, 
addr: 0x44d4, value: 0xff00, 

-----------------------------------------
Other package(Package ID: 5): 0x10,0x00,0x00,0x00,0x01,0x01,0x02,0x00,0x06,0x06,0x09,0x19,0x02,0x02,0x02,0x02

-----------------------------------------
Other package(Package ID: 20): 0x05,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x05,0x02,0x00,0x00,0x00,0x00,0x20,0x00,0x00,0x00,0x10,0x00,0x00,0x00,0x03,0x00,0x00,0x00,0x03,0x00,0x00,0x00,0x0b,0x06,0x6c,0x84,0xfd,0xff,0xe3,0x87,0xff,0xff,0x38,0xcc,0x0f,0x00,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1e,0x00,0x00,0x00,0x0f,0x00,0x00,0x00,0x04,0x00,0x00,0x00,0x03,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00

-----------------------------------------*/
/*-------------- List all registers ---------------------------*/
STGh3x2xReg stGh3x2xRegConfigArr[141] =
{
    {0x0004, 0x001F}, {0x0006, 0x0005}, {0x000A, 0x00A0}, {0x000E, 0x000D}, {0x0100, 0x0100}, {0x0102, 0x0302},
    {0x0104, 0x0504}, {0x010A, 0x277C}, {0x0110, 0x0F14}, {0x0112, 0x5555}, {0x0114, 0x00AA}, {0x011E, 0x0019},
    {0x0120, 0x0019}, {0x0122, 0x0CBF}, {0x0126, 0x2744}, {0x0128, 0x0001}, {0x012A, 0x0000}, {0x012C, 0x0F16},
    {0x012E, 0x4441}, {0x0130, 0x0002}, {0x013A, 0x0319}, {0x013C, 0x0319}, {0x013E, 0x19BF}, {0x0142, 0x2744},
    {0x0144, 0x0001}, {0x0146, 0x0000}, {0x0148, 0x0F16}, {0x014A, 0x4441}, {0x014C, 0x0002}, {0x0156, 0x0219},
    {0x0158, 0x0219}, {0x015A, 0x19BF}, {0x015E, 0x2744}, {0x0160, 0x0001}, {0x0162, 0x0000}, {0x0164, 0x0C10},
    {0x0166, 0x4442}, {0x0168, 0x0002}, {0x0172, 0x0207}, {0x0174, 0x0207}, {0x017A, 0x2744}, {0x017C, 0x0001},
    {0x017E, 0x0000}, {0x0180, 0x0000}, {0x0182, 0x4442}, {0x0184, 0x0002}, {0x018E, 0x0207}, {0x0190, 0x0207},
    {0x0196, 0xC704}, {0x0198, 0x0001}, {0x019A, 0x0000}, {0x019C, 0x0C10}, {0x019E, 0x4442}, {0x01A0, 0x0002},
    {0x01A6, 0x0003}, {0x01AA, 0x0207}, {0x01AC, 0x0207}, {0x01EC, 0x0172}, {0x01EE, 0x034A}, {0x01F0, 0x034A},
    {0x01F2, 0x00E8}, {0x01F4, 0x006A}, {0x01F6, 0x00E8}, {0x0200, 0x0120}, {0x0280, 0x0A00}, {0x0282, 0xF530},
    {0x0284, 0x9388}, {0x0286, 0xC45C}, {0x0288, 0xA328}, {0x0408, 0x0001}, {0x0410, 0x49F0}, {0x0412, 0x0082},
    {0x0414, 0x49F0}, {0x0416, 0x0082}, {0x0422, 0x0011}, {0x0424, 0x0901}, {0x0426, 0x0102}, {0x0428, 0x0102},
    {0x0502, 0x4F60}, {0x0504, 0x000A}, {0x0506, 0x00A1}, {0x0588, 0x0008}, {0x0692, 0x0233}, {0x0694, 0x0400},
    {0x0698, 0x2310}, {0x0700, 0x0000}, {0x1000, 0x0000}, {0x1002, 0x3197}, {0x1004, 0x0000}, {0x1006, 0xFC22},
    {0x1008, 0x2F3D}, {0x100A, 0x0001}, {0x10E0, 0x0202}, {0x10E2, 0x0202}, {0x10E4, 0x0202}, {0x10E6, 0xFFFF},
    {0x10E8, 0x0202}, {0x10EA, 0x0202}, {0x10EC, 0x0202}, {0x10EE, 0x0202}, {0x1120, 0x00A0}, {0x1122, 0x0005},
    {0x1124, 0x0000}, {0x1140, 0x0000}, {0x1142, 0x00C8}, {0x1144, 0x0001}, {0x1160, 0x0007}, {0x1162, 0x0000},
    {0x1164, 0x0402}, {0x1166, 0x4568}, {0x1168, 0x9940}, {0x116A, 0x00D9}, {0x116C, 0x8800}, {0x116E, 0x0093},
    {0x1170, 0x5C00}, {0x1172, 0x00C4}, {0x1174, 0x2800}, {0x1176, 0x00A3}, {0x2000, 0x0001}, {0x2002, 0x00A2},
    {0x2022, 0x0004}, {0x2024, 0x0901}, {0x2026, 0x1911}, {0x2044, 0x0004}, {0x2046, 0x0901}, {0x2048, 0x1911},
    {0x20CC, 0x0002}, {0x20CE, 0x4223}, {0x2132, 0x0003}, {0x2134, 0x6201}, {0x2136, 0x0082}, {0x2880, 0x0005},
    {0x2882, 0x0019}, {0x2884, 0x0019}, {0x288C, 0x0019}, {0x2892, 0x0019}, {0x3000, 0x0001}, {0x3002, 0x0000},
    {0x3004, 0x49F0}, {0x3006, 0x0082}, {0xFFFF, 0x0001}
};
STGh3x2xReg stGh3x2xRegConfigArr[49] =
{
    {0x33C0, 0x0000}, {0x33C2, 0x0000}, {0x33C4, 0x0000}, {0x33C6, 0x0000}, {0x33C8, 0x0000}, {0x33CA, 0x0000},
    {0x33CC, 0x0000}, {0x33CE, 0x0000}, {0x33D0, 0x0000}, {0x33D2, 0x0000}, {0x35C0, 0x0004}, {0x35C4, 0x0100},
    {0x35C6, 0x0302}, {0x36C0, 0x0000}, {0x36C2, 0x0000}, {0x36C4, 0x0020}, {0x36C6, 0x0000}, {0x36C8, 0x0010},
    {0x36CA, 0x0000}, {0x36CC, 0x0003}, {0x36CE, 0x0000}, {0x36D0, 0x0003}, {0x36D2, 0x0000}, {0x42C0, 0x846C},
    {0x42C2, 0xFFFD}, {0x42C4, 0x87E3}, {0x42C6, 0xFFFF}, {0x42C8, 0xCC38}, {0x42CA, 0x000F}, {0x42CC, 0x0001},
    {0x42CE, 0x0000}, {0x42D0, 0x0000}, {0x42D2, 0x0000}, {0x42D4, 0x001E}, {0x42D6, 0x0000}, {0x42D8, 0x000F},
    {0x42DA, 0x0000}, {0x42DC, 0x0004}, {0x42DE, 0x0000}, {0x42E0, 0x0003}, {0x42E2, 0x0000}, {0x42E4, 0x0000},
    {0x42E6, 0x0000}, {0x42E8, 0x0000}, {0x42EA, 0x0000}, {0x44C0, 0x0001}, {0x44CC, 0xFF01}, {0x44D4, 0xFF00},
    {0xFFFF, 0x0001}
};
/*-----------------------------------------*/



