time=04/28 09:25:34.391, level=INFO, class = LogService, tool_version = 0.2.12/air_bta_ic_premium_g3
time=04/28 09:25:34.388, level=INFO, class = Controller, SetLogLevel = 3
time=04/28 09:25:34.388, level=DEBUG, class = ToolLogLevel, log_level = 3
time=04/28 09:25:34.388, level=DEBUG, class = ToolLogLevel, task_time = 0.000
time=04/28 09:25:34.388, level=DEBUG, class = ConnectDUT, state = init data, result = ok
time=04/28 09:25:34.388, level=DEBUG, class = SerialHost, phy_name = UART, trans_name = h4
time=04/28 09:25:34.388, level=DEBUG, class = Physical, type = 1, state = create
time=04/28 09:25:34.388, level=DEBUG, class = Transport, type = 4, state = create
time=04/28 09:25:34.390, level=INFO, class = UartPhy, desc = USB Serial Port (COM4)
time=04/28 09:25:34.390, level=INFO, class = UartPhy, instance_id = FTDIBUS\VID_0403+PID_6015+DQ014GZWA\0000
time=04/28 09:25:34.390, level=INFO, class = UartPhy, port = 4, baud = 3000000, handshake = 0, read/timeout = (3, 0)
time=04/28 09:25:34.431, level=DEBUG, class = SerialHost, state = open
time=04/28 09:25:34.431, level=DEBUG, class = UartDev, state = connect, ret = 0
time=04/28 09:25:34.431, level=DEBUG, class = ConnectDUT, state = connect, device_name = DUT, device_type = UART, result = ok
time=04/28 09:25:34.431, level=DEBUG, class = ConnectDUT, task_time = 0.043
time=04/28 09:25:34.431, level=DEBUG, class = CallbackManager, register = DUT, filter = 0, cb = 0x004D2C00
time=04/28 09:25:34.432, level=DEBUG, class = UartDev, SetBaudrate = 115200
time=04/28 09:25:34.432, level=DEBUG, class = Host, SwitchTransport = bypass
time=04/28 09:25:34.432, level=DEBUG, class = Transport, type = 1, state = create
time=04/28 09:25:34.432, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=04/28 09:25:34.443, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:34.443, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:34.460, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=04/28 09:25:34.478, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=04/28 09:25:34.478, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=04/28 09:25:34.478, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:34.478, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:34.495, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=04/28 09:25:34.513, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=04/28 09:25:34.513, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=04/28 09:25:34.513, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:34.513, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:34.531, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=04/28 09:25:34.548, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=04/28 09:25:34.548, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=04/28 09:25:34.548, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:34.548, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:34.566, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=04/28 09:25:34.584, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=04/28 09:25:34.584, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=04/28 09:25:34.584, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:34.584, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:34.601, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=04/28 09:25:34.619, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=04/28 09:25:34.619, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=04/28 09:25:34.619, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:34.619, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:34.644, level=WARN, class = Host, msg = Match timeout 25 ms(0)
time=04/28 09:25:34.676, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:34.676, level=WARN, class = BtromHandshake, timeout = 56, message = response timeout
time=04/28 09:25:34.676, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:34.676, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:34.706, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:34.706, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:34.707, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:34.707, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:34.738, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:34.738, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:34.738, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:34.738, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:34.769, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:34.769, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:34.769, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:34.769, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:34.801, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:34.801, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:34.801, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:34.801, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:34.832, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:34.832, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:34.832, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:34.832, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:34.863, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:34.863, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:34.863, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:34.863, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:34.895, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:34.895, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:34.895, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:34.895, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:34.926, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:34.926, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:34.927, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:34.927, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:34.958, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:34.958, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:34.958, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:34.958, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:34.989, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:34.989, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:34.989, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:34.989, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.020, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:35.020, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:35.020, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.020, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.051, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:35.051, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:35.051, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.051, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.082, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:35.082, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:35.082, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.082, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.114, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:35.114, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:35.114, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.114, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.145, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:35.145, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:35.145, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.145, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.175, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:35.175, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:35.176, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.176, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.207, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:35.207, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:35.207, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.207, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.238, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:35.238, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:35.238, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.238, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.268, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:35.268, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:35.269, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.269, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.299, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:35.299, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:35.300, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.300, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.330, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:35.330, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:35.331, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.331, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.362, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:35.362, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:35.362, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.362, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.393, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:35.393, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:35.394, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.394, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.424, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:35.424, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:35.424, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.424, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.456, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:35.456, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:35.456, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.456, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.487, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:35.487, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:35.487, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.487, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.518, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:35.518, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:35.518, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.518, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.550, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:35.550, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:35.550, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.550, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.581, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:35.581, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:35.581, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.581, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.611, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:35.611, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:35.612, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.612, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.643, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:35.643, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:35.644, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.644, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.673, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:35.704, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:35.704, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=04/28 09:25:35.704, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.704, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.736, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:35.736, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:35.736, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.736, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.767, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:35.767, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:35.768, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.768, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.798, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:35.798, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:35.799, level=DEBUG, [COM4] Read(2)
time=04/28 09:25:35.799, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=04/28 09:25:35.799, level=INFO, [RxP 2] 2450
time=04/28 09:25:35.799, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.799, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.814, level=DEBUG, [COM4] Read(32)
time=04/28 09:25:35.814, level=INFO, [RxP 32] 4149523338322C312A32450D0A24504149523130302C312C302A33410D0A2450
time=04/28 09:25:35.830, level=DEBUG, [COM4] Read(30)
time=04/28 09:25:35.830, level=INFO, [RxP 30] 4149523036322C312C302A33460D0A24504149523036322C352C302A3342
time=04/28 09:25:35.830, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:35.830, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.830, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.845, level=DEBUG, [COM4] Read(24)
time=04/28 09:25:35.845, level=INFO, [RxP 24] 0D0A24504149523036322C332C302A33440D0A2450414952
time=04/28 09:25:35.860, level=DEBUG, [COM4] Read(30)
time=04/28 09:25:35.860, level=INFO, [RxP 30] 3038302C302A32450D0A24504149523036362C312C312C312C312C312C30
time=04/28 09:25:35.860, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:35.861, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.861, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.876, level=DEBUG, [COM4] Read(30)
time=04/28 09:25:35.876, level=INFO, [RxP 30] 2A33420D0A24504149523038312A33330D0A24504149523439302C312A32
time=04/28 09:25:35.892, level=DEBUG, [COM4] Read(3)
time=04/28 09:25:35.892, level=INFO, [RxP 3] 410D0A
time=04/28 09:25:35.892, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:35.892, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.892, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.922, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:35.922, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:35.923, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.923, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.954, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:35.954, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:35.954, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.954, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:35.985, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:35.985, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:35.985, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:35.985, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.016, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:36.016, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:36.016, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.016, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.048, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:36.048, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:36.048, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.048, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.079, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:36.079, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:36.080, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.080, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.111, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:36.111, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:36.111, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.111, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.142, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:36.142, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:36.142, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.142, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.173, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:36.173, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:36.173, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.173, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.205, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:36.205, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:36.205, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.205, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.237, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:36.237, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:36.237, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.237, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.267, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:36.298, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:36.298, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=04/28 09:25:36.299, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.299, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.330, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:36.330, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:36.331, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.331, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.361, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:36.361, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:36.361, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.361, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.392, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:36.392, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:36.392, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.392, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.423, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:36.423, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:36.423, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.423, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.455, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:36.455, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:36.455, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.455, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.485, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:36.485, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:36.485, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.485, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.516, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:36.516, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:36.517, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.517, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.547, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:36.547, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:36.548, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.548, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.578, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:36.578, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:36.578, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.578, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.610, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:36.610, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:36.610, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.610, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.641, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:36.641, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:36.641, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.641, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.671, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:36.671, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:36.672, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.672, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.703, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:36.703, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:36.703, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.703, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.734, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:36.734, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:36.734, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.734, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.765, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:36.765, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:36.766, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.766, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.796, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:36.796, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:36.796, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.796, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.828, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:36.828, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:36.828, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.828, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.859, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:36.859, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:36.860, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.860, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.891, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:36.891, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:36.891, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.891, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.922, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:36.922, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:36.922, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.922, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.954, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:36.954, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:36.955, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.955, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:36.986, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:36.986, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:36.987, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:36.987, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.017, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:37.017, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:37.017, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.017, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.048, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:37.048, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:37.048, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.048, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.079, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:37.079, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:37.079, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.079, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.109, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:37.109, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:37.110, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.110, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.141, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:37.141, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:37.141, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.141, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.172, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:37.172, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:37.172, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.172, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.203, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:37.203, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:37.203, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.203, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.234, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:37.234, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:37.234, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.234, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.265, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:37.265, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:37.265, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.265, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.296, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:37.296, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:37.296, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.296, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.327, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:37.327, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:37.328, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.328, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.358, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:37.358, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:37.359, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.359, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.390, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:37.390, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:37.390, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.390, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.420, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:37.420, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:37.421, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.421, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.452, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:37.452, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:37.453, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.453, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.483, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:37.483, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:37.483, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.483, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.514, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:37.514, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:37.514, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.514, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.545, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:37.545, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:37.545, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.545, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.576, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:37.576, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:37.576, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.576, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.607, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:37.607, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:37.607, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.607, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.639, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:37.639, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:37.639, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.639, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.669, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:37.669, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:37.670, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.670, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.701, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:37.701, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:37.701, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.701, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.732, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:37.732, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:37.732, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.732, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.763, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:37.763, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:37.764, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.764, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.795, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:37.795, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:37.795, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.795, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.825, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:37.856, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:37.856, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=04/28 09:25:37.856, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.856, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.887, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:37.887, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:37.887, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.887, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.918, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:37.918, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:37.918, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.918, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:37.948, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:37.980, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:37.980, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=04/28 09:25:37.980, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:37.980, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.012, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:38.012, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:38.012, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.012, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.042, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:38.072, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:38.072, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=04/28 09:25:38.073, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.073, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.104, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:38.104, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:38.104, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.104, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.135, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:38.135, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:38.135, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.135, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.166, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:38.166, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:38.166, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.166, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.198, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:38.198, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:38.198, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.198, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.228, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:38.228, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:38.229, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.229, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.260, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:38.260, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:38.260, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.260, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.291, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:38.291, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:38.291, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.291, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.323, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:38.323, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:38.323, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.323, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.353, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:38.353, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:38.354, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.354, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.385, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:38.385, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:38.385, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.385, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.416, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:38.416, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:38.416, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.416, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.447, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:38.447, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:38.447, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.447, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.478, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:38.478, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:38.478, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.478, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.509, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:38.509, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:38.509, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.509, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.541, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:38.541, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:38.541, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.541, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.571, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:38.571, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:38.572, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.572, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.603, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:38.603, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:38.603, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.603, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.634, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:38.634, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:38.634, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.634, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.664, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:38.664, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:38.664, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.664, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.696, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:38.696, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:38.696, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.696, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.727, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:38.727, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:38.728, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.728, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.758, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:38.758, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:38.758, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.758, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.774, level=DEBUG, [COM4] Read(31)
time=04/28 09:25:38.774, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 50.08 B/s
time=04/28 09:25:38.774, level=INFO, [RxP 31] 24504149523338322C312A32450D0A24504149523130302C312C302A33410D
time=04/28 09:25:38.789, level=DEBUG, [COM4] Read(31)
time=04/28 09:25:38.789, level=INFO, [RxP 31] 0A24504149523036322C312C302A33460D0A24504149523036322C352C302A
time=04/28 09:25:38.789, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:38.790, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.790, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.805, level=DEBUG, [COM4] Read(22)
time=04/28 09:25:38.805, level=INFO, [RxP 22] 33420D0A24504149523036322C332C302A33440D0A24
time=04/28 09:25:38.821, level=DEBUG, [COM4] Read(32)
time=04/28 09:25:38.821, level=INFO, [RxP 32] 504149523038302C302A32450D0A24504149523036362C312C312C312C312C31
time=04/28 09:25:38.821, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:38.821, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.821, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.836, level=DEBUG, [COM4] Read(30)
time=04/28 09:25:38.836, level=INFO, [RxP 30] 2C302A33420D0A24504149523038312A33330D0A24504149523439302C31
time=04/28 09:25:38.852, level=DEBUG, [COM4] Read(5)
time=04/28 09:25:38.852, level=INFO, [RxP 5] 2A32410D0A
time=04/28 09:25:38.852, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:38.852, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.852, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.883, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:38.883, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:38.883, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.883, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.914, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:38.914, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:38.914, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.914, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.945, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:38.945, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:38.946, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.946, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:38.976, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:38.976, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:38.977, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:38.977, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.007, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:39.007, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:39.008, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.008, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.039, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:39.039, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:39.040, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.040, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.070, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:39.070, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:39.070, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.070, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.101, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:39.101, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:39.101, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.101, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.132, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:39.132, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:39.132, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.132, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.163, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:39.163, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:39.163, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.163, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.194, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:39.194, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:39.194, level=DEBUG, [COM4] Read(12)
time=04/28 09:25:39.194, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 285.28 B/s
time=04/28 09:25:39.194, level=INFO, [RxP 12] 24504149523030322A33380D
time=04/28 09:25:39.195, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.195, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.225, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:39.225, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:39.225, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.225, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.225, level=DEBUG, [COM4] Read(14)
time=04/28 09:25:39.225, level=INFO, [RxP 14] 0A24504149523439312A33360D0A
time=04/28 09:25:39.256, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:39.256, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:39.256, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.256, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.286, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:39.318, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:39.318, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=04/28 09:25:39.318, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.318, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.349, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:39.349, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:39.349, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.349, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.380, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:39.380, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:39.380, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.380, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.411, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:39.411, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:39.411, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.411, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.412, level=DEBUG, [COM4] Read(13)
time=04/28 09:25:39.412, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 64.45 B/s
time=04/28 09:25:39.412, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=04/28 09:25:39.443, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:39.443, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:39.443, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.443, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.474, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:39.474, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:39.475, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.475, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.506, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:39.506, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:39.507, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.507, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.538, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:39.538, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:39.538, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.538, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.569, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:39.569, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:39.569, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.569, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.600, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:39.600, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:39.600, level=DEBUG, [COM4] Read(4)
time=04/28 09:25:39.600, level=INFO, [RxP 4] 24504149
time=04/28 09:25:39.601, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.601, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.616, level=DEBUG, [COM4] Read(9)
time=04/28 09:25:39.616, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 19.53 B/s
time=04/28 09:25:39.616, level=INFO, [RxP 9] 523439312A33360D0A
time=04/28 09:25:39.647, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:39.647, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=04/28 09:25:39.647, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.647, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.678, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:39.678, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:39.678, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.678, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.709, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:39.709, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:39.709, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.709, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.741, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:39.741, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:39.741, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.741, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.771, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:39.771, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:39.771, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.771, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.803, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:39.803, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:39.803, level=DEBUG, [COM4] Read(13)
time=04/28 09:25:39.803, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=04/28 09:25:39.803, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.803, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.834, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:39.834, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:39.834, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.834, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.865, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:39.865, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:39.865, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.865, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.896, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:39.896, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:39.897, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.897, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.928, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:39.928, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:39.928, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.928, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.959, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:39.959, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:39.959, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.959, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:39.989, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:39.989, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:39.989, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:39.989, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.020, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:40.020, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:40.020, level=DEBUG, [COM4] Read(13)
time=04/28 09:25:40.020, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 32.17 B/s
time=04/28 09:25:40.020, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=04/28 09:25:40.021, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.021, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.051, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:40.051, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:40.051, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.051, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.082, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:40.082, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:40.082, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.082, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.114, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:40.114, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:40.114, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.114, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.144, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:40.144, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:40.144, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.144, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.176, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:40.176, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:40.176, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.176, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.207, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:40.207, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:40.207, level=DEBUG, [COM4] Read(10)
time=04/28 09:25:40.207, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.207, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.207, level=INFO, [RxP 10] 24504149523439312A33
time=04/28 09:25:40.223, level=DEBUG, [COM4] Read(3)
time=04/28 09:25:40.223, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 49.34 B/s
time=04/28 09:25:40.223, level=INFO, [RxP 3] 360D0A
time=04/28 09:25:40.254, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:40.254, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=04/28 09:25:40.254, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.254, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.284, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:40.284, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:40.285, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.285, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.315, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:40.315, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:40.316, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.316, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.346, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:40.346, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:40.346, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.346, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.378, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:40.378, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:40.378, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.378, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.409, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:40.409, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:40.409, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.409, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.425, level=DEBUG, [COM4] Read(13)
time=04/28 09:25:40.425, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=04/28 09:25:40.425, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=04/28 09:25:40.455, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:40.455, level=WARN, class = BtromHandshake, timeout = 45, message = response timeout
time=04/28 09:25:40.456, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.456, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.486, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:40.486, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:40.486, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.486, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.517, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:40.517, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:40.517, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.517, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.548, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:40.548, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:40.548, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.548, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.579, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:40.579, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:40.579, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.579, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.611, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:40.611, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:40.611, level=DEBUG, [COM4] Read(4)
time=04/28 09:25:40.611, level=INFO, [RxP 4] 24504149
time=04/28 09:25:40.611, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.611, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.627, level=DEBUG, [COM4] Read(9)
time=04/28 09:25:40.627, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 19.78 B/s
time=04/28 09:25:40.627, level=INFO, [RxP 9] 523439312A33360D0A
time=04/28 09:25:40.658, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:40.658, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=04/28 09:25:40.658, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.658, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.689, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:40.689, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:40.689, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.689, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.720, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:40.720, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:40.720, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.720, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.750, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:40.750, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:40.751, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.751, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.782, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:40.782, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:40.782, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.782, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.812, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:40.812, level=DEBUG, [COM4] Read(13)
time=04/28 09:25:40.812, level=INFO, [RxP 13] 24504149523439312A33360D0A
time=04/28 09:25:40.842, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:40.842, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=04/28 09:25:40.842, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.842, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.873, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:40.873, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:40.874, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.874, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.905, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:40.905, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:40.905, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.905, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.935, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:40.935, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:40.935, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.935, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.966, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:40.966, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:40.966, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.966, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:40.997, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:40.997, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:40.997, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:40.997, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.028, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:41.028, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:41.028, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.028, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.059, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:41.059, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:41.060, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.060, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.091, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:41.091, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:41.091, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.091, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.121, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:41.153, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:41.153, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=04/28 09:25:41.153, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.153, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.183, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:41.183, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:41.183, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.183, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.214, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:41.214, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:41.214, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.214, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.245, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:41.245, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:41.246, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.246, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.277, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:41.277, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:41.277, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.277, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.308, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:41.308, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:41.308, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.308, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.339, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:41.339, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:41.339, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.339, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.370, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:41.370, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:41.370, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.370, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.400, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:41.400, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:41.401, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.401, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.432, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:41.432, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:41.432, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.432, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.464, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:41.464, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:41.464, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.464, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.494, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:41.525, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:41.525, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=04/28 09:25:41.525, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.525, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.556, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:41.556, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:41.556, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.556, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.587, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:41.587, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:41.587, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.587, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.618, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:41.618, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:41.618, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.618, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.650, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:41.650, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:41.650, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.650, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.681, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:41.681, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:41.681, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.681, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.712, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:41.712, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:41.712, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.712, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.743, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:41.743, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:41.743, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.743, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.775, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:41.775, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:41.775, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.775, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.806, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:41.806, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:41.806, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.806, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.836, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:41.836, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:41.837, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.837, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.867, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:41.867, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:41.867, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.867, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.899, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:41.899, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:41.899, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.899, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.930, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:41.930, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:41.930, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.930, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.961, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:41.961, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:41.961, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.961, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:41.992, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:41.992, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:41.992, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:41.992, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.023, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:42.023, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:42.024, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.024, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.054, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:42.054, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:42.054, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.054, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.085, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:42.085, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:42.086, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.086, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.116, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:42.116, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:42.117, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.117, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.148, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:42.148, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:42.148, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.148, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.179, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:42.179, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:42.179, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.179, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.210, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:42.210, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:42.210, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.210, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.241, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:42.241, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:42.241, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.241, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.272, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:42.272, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:42.272, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.272, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.302, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:42.333, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:42.333, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=04/28 09:25:42.333, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.333, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.364, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:42.364, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:42.365, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.365, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.395, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:42.395, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:42.396, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.396, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.426, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:42.426, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:42.427, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.427, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.458, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:42.458, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:42.458, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.458, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.489, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:42.489, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:42.489, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.489, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.520, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:42.520, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:42.520, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.520, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.551, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:42.551, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:42.551, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.551, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.583, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:42.583, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:42.583, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.583, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.613, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:42.613, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:42.613, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.613, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.645, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:42.645, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:42.646, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.646, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.676, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:42.676, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:42.676, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.676, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.707, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:42.707, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:42.708, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.708, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.739, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:42.739, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:42.739, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.739, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.770, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:42.770, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:42.771, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.771, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.801, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:42.801, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:42.802, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.802, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.833, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:42.833, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:42.833, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.833, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.863, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:42.863, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:42.863, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.863, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.895, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=04/28 09:25:42.895, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=04/28 09:25:42.896, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.896, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.926, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:42.926, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:42.927, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.927, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.957, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:42.957, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:42.958, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.958, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:42.989, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:42.989, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:42.989, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:42.989, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.019, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:43.019, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:43.019, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.019, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.050, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:43.050, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:43.050, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.050, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.081, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:43.081, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:43.081, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.081, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.113, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:43.113, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:43.113, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.113, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.144, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:43.144, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:43.144, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.144, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.175, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:43.175, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:43.176, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.176, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.206, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:43.206, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:43.207, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.207, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.237, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:43.237, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:43.237, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.237, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.269, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=04/28 09:25:43.269, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=04/28 09:25:43.269, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.269, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.301, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:43.301, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:43.301, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.301, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.332, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:43.332, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:43.332, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.332, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.363, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:43.363, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:43.363, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.363, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.395, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:43.395, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:43.395, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.395, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.426, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:43.426, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:43.426, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.426, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.456, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:43.456, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:43.457, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.457, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.488, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:43.488, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:43.488, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.488, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.519, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:43.519, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:43.519, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.519, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.550, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:43.550, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:43.550, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.550, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.581, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:43.581, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:43.581, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.581, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.612, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:43.612, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:43.612, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.612, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.643, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:43.643, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:43.643, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.643, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.674, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:43.674, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:43.674, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.674, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.705, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:43.705, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:43.706, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.706, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.736, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:43.767, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:43.767, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=04/28 09:25:43.768, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.768, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.799, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:43.799, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:43.799, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.799, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.830, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:43.830, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:43.830, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.830, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.862, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:43.862, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:43.862, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.862, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.893, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:43.893, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:43.893, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.893, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.924, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:43.924, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:43.925, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.925, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.955, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:43.955, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:43.955, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.955, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:43.986, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:43.986, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:43.986, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:43.986, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:44.017, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:44.017, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:44.017, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:44.017, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:44.048, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:44.048, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:44.048, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:44.048, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:44.080, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:44.080, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:44.080, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:44.080, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:44.112, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:44.112, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:44.112, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:44.112, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:44.143, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:44.143, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:44.143, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:44.143, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:44.174, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:44.174, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:44.174, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:44.174, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:44.205, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:44.205, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:44.205, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:44.205, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:44.236, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:44.236, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:44.236, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:44.236, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:44.267, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:44.267, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:44.267, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:44.267, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:44.298, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:44.298, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:44.298, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:44.298, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:44.329, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:44.329, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:44.329, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:44.329, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:44.359, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:44.359, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:44.359, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:44.359, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:44.390, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:44.390, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:44.390, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:44.390, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:44.421, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:44.421, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:44.422, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:44.422, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:44.453, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:44.453, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:44.453, level=ERROR, class = BtromHandshake, result = fail, timeout = 10009, error_message = btrom handshake timeout
time=04/28 09:25:44.454, level=ERROR, D:\project\crossover\src\task\flash\download_da_uart.cc:141(-1): class = DownloadDa_UART, error_message = btrom handshake fail
time=04/28 09:25:44.454, level=ERROR, class = Task, task_name = DownloadDa_UART, error_message = Fail to execute RunTask()
time=04/28 09:25:44.454, level=DEBUG, class = DownloadDa_UART, task_time = 10.022
time=04/28 09:25:44.454, level=DEBUG, class = Controller, RemoveObserver = callback
time=04/28 09:25:44.454, level=DEBUG, class = CallbackManager, deregister = callback
time=04/28 09:25:44.454, level=DEBUG, class = DisconnectDUT, state = init data, result = ok
time=04/28 09:25:44.563, level=DEBUG, class = SerialHost, state = close
time=04/28 09:25:44.563, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=04/28 09:25:44.563, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=04/28 09:25:44.563, level=DEBUG, class = UartDev, state = disconnect
time=04/28 09:25:44.563, level=DEBUG, class = DisconnectDUT, state = disconnect, device_name = DUT, device_type = UART
time=04/28 09:25:44.563, level=DEBUG, class = DisconnectDUT, task_time = 0.109
time=04/28 09:25:44.563, level=DEBUG, class = LogService, state = CloseLogFile, pass_fail = 1, postfix = 
