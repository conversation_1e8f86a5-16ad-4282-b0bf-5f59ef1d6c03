time=06/07 09:48:03.485, level=INFO, class = LogService, tool_version = 0.2.12/air_bta_ic_premium_g3
time=06/07 09:48:03.484, level=INFO, class = Controller, SetLogLevel = 3
time=06/07 09:48:03.484, level=DEBUG, class = ToolLogLevel, log_level = 3
time=06/07 09:48:03.484, level=DEBUG, class = ToolLogLevel, task_time = 0.000
time=06/07 09:48:03.484, level=DEBUG, class = ConnectDUT, state = init data, result = ok
time=06/07 09:48:03.484, level=DEBUG, class = SerialHost, phy_name = UART, trans_name = h4
time=06/07 09:48:03.484, level=DEBUG, class = Physical, type = 1, state = create
time=06/07 09:48:03.484, level=DEBUG, class = Transport, type = 4, state = create
time=06/07 09:48:03.485, level=INFO, class = UartPhy, desc = USB Serial Port (COM4)
time=06/07 09:48:03.485, level=INFO, class = UartPhy, instance_id = FTDIBUS\VID_0403+PID_6015+DQ014GZWA\0000
time=06/07 09:48:03.485, level=INFO, class = UartPhy, port = 4, baud = 3000000, handshake = 0, read/timeout = (3, 0)
time=06/07 09:48:03.526, level=DEBUG, class = SerialHost, state = open
time=06/07 09:48:03.526, level=DEBUG, class = UartDev, state = connect, ret = 0
time=06/07 09:48:03.526, level=DEBUG, class = ConnectDUT, state = connect, device_name = DUT, device_type = UART, result = ok
time=06/07 09:48:03.526, level=DEBUG, class = ConnectDUT, task_time = 0.042
time=06/07 09:48:03.526, level=DEBUG, class = CallbackManager, register = DUT, filter = 0, cb = 0x00EE2C00
time=06/07 09:48:03.526, level=DEBUG, class = UartDev, SetBaudrate = 115200
time=06/07 09:48:03.527, level=DEBUG, class = Host, SwitchTransport = bypass
time=06/07 09:48:03.527, level=DEBUG, class = Transport, type = 1, state = create
time=06/07 09:48:03.527, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=06/07 09:48:03.528, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:03.528, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:03.545, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:48:03.562, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:48:03.562, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=06/07 09:48:03.562, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:03.562, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:03.580, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:48:03.598, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:48:03.598, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=06/07 09:48:03.598, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:03.598, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:03.616, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=06/07 09:48:03.633, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:48:03.633, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=06/07 09:48:03.634, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:03.634, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:03.651, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:48:03.668, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:48:03.668, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=06/07 09:48:03.668, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:03.668, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:03.686, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=06/07 09:48:03.704, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=06/07 09:48:03.704, level=WARN, class = BtromHandshake, timeout = 36, message = response timeout
time=06/07 09:48:03.705, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:03.705, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:03.725, level=WARN, class = Host, msg = Match timeout 20 ms(0)
time=06/07 09:48:03.757, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=06/07 09:48:03.757, level=WARN, class = BtromHandshake, timeout = 52, message = response timeout
time=06/07 09:48:03.758, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:03.758, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:03.788, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:03.788, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:03.788, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:03.788, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:03.819, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:03.819, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:03.819, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:03.819, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:03.850, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:03.850, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:03.850, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:03.850, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:03.881, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:03.881, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:03.881, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:03.881, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:03.912, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:03.912, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:03.912, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:03.912, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:03.944, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:03.944, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:03.944, level=DEBUG, [COM4] Read(32)
time=06/07 09:48:03.944, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=06/07 09:48:03.944, level=INFO, [RxP 32] 24504149523338322C312A32450D0A24504149523130302C312C302A33410D0A
time=06/07 09:48:03.944, level=DEBUG, class = Host, drop_size = 0 0000000000000000000000000000000000000000000000000000000000000000
time=06/07 09:48:03.944, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:03.944, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:03.959, level=DEBUG, [COM4] Read(30)
time=06/07 09:48:03.959, level=INFO, [RxP 30] 24504149523036322C312C302A33460D0A24504149523036322C352C302A
time=06/07 09:48:03.975, level=DEBUG, [COM4] Read(26)
time=06/07 09:48:03.975, level=INFO, [RxP 26] 33420D0A24504149523036322C332C302A33440D0A2450414952
time=06/07 09:48:03.975, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:03.975, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:03.975, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:03.990, level=DEBUG, [COM4] Read(28)
time=06/07 09:48:03.990, level=INFO, [RxP 28] 3038302C302A32450D0A24504149523036362C312C312C312C312C31
time=06/07 09:48:04.006, level=DEBUG, [COM4] Read(32)
time=06/07 09:48:04.006, level=INFO, [RxP 32] 2C302A33420D0A24504149523038312A33330D0A24504149523439302C312A32
time=06/07 09:48:04.006, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:04.006, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.006, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.021, level=DEBUG, [COM4] Read(3)
time=06/07 09:48:04.021, level=INFO, [RxP 3] 410D0A
time=06/07 09:48:04.053, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:04.053, level=WARN, class = BtromHandshake, timeout = 47, message = response timeout
time=06/07 09:48:04.053, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.053, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.075, level=WARN, class = Host, msg = Match timeout 21 ms(0)
time=06/07 09:48:04.106, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:04.106, level=WARN, class = BtromHandshake, timeout = 52, message = response timeout
time=06/07 09:48:04.106, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.106, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.137, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:04.137, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:04.137, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.137, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.168, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:04.168, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:04.169, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.169, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.200, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:04.200, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:04.200, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.200, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.231, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:04.231, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:04.231, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.231, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.262, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:04.262, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:04.263, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.263, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.293, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:04.293, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:04.293, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.293, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.325, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:04.325, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:04.325, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.325, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.356, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:04.356, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:04.356, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.356, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.387, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:04.387, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:04.387, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.387, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.420, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=06/07 09:48:04.420, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=06/07 09:48:04.420, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.420, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.451, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:04.451, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:04.451, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.451, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.482, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:04.482, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:04.483, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.483, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.514, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:04.514, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:04.514, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.514, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.545, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:04.545, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:04.545, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.545, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.576, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:04.576, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:04.576, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.576, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.607, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:04.607, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:04.607, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.607, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.639, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:04.639, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:04.639, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.639, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.670, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:04.670, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:04.670, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.670, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.701, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:04.701, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:04.701, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.701, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.732, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:04.732, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:04.733, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.733, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.763, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:04.763, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:04.764, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.764, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.795, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:04.795, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:04.795, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.795, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.826, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:04.826, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:04.826, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.826, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.857, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:04.857, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:04.858, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.858, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.888, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:04.888, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:04.889, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.889, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.920, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:04.920, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:04.921, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.921, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.953, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=06/07 09:48:04.953, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=06/07 09:48:04.953, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.953, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:04.984, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:04.984, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:04.985, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:04.985, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.015, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:05.015, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:05.016, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.016, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.047, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:05.047, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:05.047, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.047, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.078, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:05.078, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:05.078, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.078, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.109, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:05.109, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:05.109, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.109, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.140, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:05.140, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:05.141, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.141, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.172, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:05.172, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:05.172, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.172, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.203, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:05.203, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:05.203, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.203, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.234, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:05.234, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:05.234, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.234, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.265, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:05.265, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:05.266, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.266, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.297, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:05.297, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:05.297, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.297, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.328, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:05.328, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:05.328, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.328, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.359, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:05.359, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:05.359, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.359, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.390, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:05.390, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:05.391, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.391, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.422, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:05.422, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:05.422, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.422, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.454, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:05.454, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:05.454, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.454, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.471, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:48:05.503, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:05.503, level=WARN, class = BtromHandshake, timeout = 48, message = response timeout
time=06/07 09:48:05.503, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.503, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.534, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:05.534, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:05.534, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.534, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.565, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:05.565, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:05.566, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.566, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.597, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:05.597, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:05.597, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.597, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.628, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:05.628, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:05.628, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.628, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.659, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:05.659, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:05.659, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.659, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.690, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:05.690, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:05.690, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.690, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.721, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:05.721, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:05.722, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.722, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.753, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:05.753, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:05.753, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.753, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.784, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:05.784, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:05.784, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.784, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.815, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:05.815, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:05.816, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.816, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.847, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:05.847, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:05.847, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.847, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.878, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:05.878, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:05.878, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.878, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.909, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:05.909, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:05.909, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.909, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.940, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:05.940, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:05.941, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.941, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:05.972, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:05.972, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:05.972, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:05.972, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.004, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:06.004, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:06.004, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.004, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.035, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.035, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:06.035, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.035, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.066, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.066, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:06.067, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.067, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.097, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.097, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:06.098, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.098, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.129, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.129, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:06.129, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.129, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.160, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.160, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:06.160, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.160, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.191, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.191, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:06.191, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.191, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.222, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.222, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:06.223, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.223, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.254, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.254, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:06.254, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.254, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.285, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:06.285, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:06.285, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.285, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.316, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.316, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:06.316, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.316, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.347, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.347, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:06.348, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.348, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.379, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.379, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:06.379, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.379, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.410, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:06.410, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:06.410, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.410, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.441, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.441, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:06.441, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.441, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.472, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.472, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:06.473, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.473, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.504, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:06.504, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:06.504, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.504, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.522, level=WARN, class = Host, msg = Match timeout 18 ms(0)
time=06/07 09:48:06.553, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:06.553, level=WARN, class = BtromHandshake, timeout = 49, message = response timeout
time=06/07 09:48:06.554, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.554, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.584, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.584, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:06.585, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.585, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.616, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.616, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:06.616, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.616, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.647, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:06.647, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:06.647, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.647, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.678, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.678, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:06.678, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.678, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.709, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:06.709, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:06.710, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.710, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.741, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.741, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:06.741, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.741, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.772, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.772, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:06.772, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.772, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.803, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.803, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:06.803, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.803, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.834, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:06.834, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:06.835, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.835, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.866, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.866, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:06.866, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.866, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.897, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.897, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:06.897, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.897, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.928, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.928, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:06.928, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.928, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.959, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.959, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:06.960, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.960, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:06.991, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:06.991, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:06.991, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:06.991, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.022, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:07.022, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:07.022, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.022, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.056, level=WARN, class = Host, msg = Match timeout 34 ms(0)
time=06/07 09:48:07.056, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=06/07 09:48:07.057, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.057, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.088, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:07.088, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:07.088, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.088, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.119, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:07.119, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:07.119, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.119, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.150, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:07.150, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:07.150, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.150, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.181, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:07.181, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:07.182, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.182, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.212, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:07.212, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:07.212, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.212, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.244, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:07.244, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:07.244, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.244, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.275, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:07.275, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:07.275, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.275, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.306, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:07.306, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:07.307, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.307, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.337, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:07.337, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:07.338, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.338, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.369, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:07.369, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:07.369, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.369, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.400, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:07.400, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:07.400, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.400, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.431, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:07.431, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:07.431, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.431, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.462, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:07.462, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:07.463, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.463, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.494, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:07.494, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:07.494, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.494, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.525, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:07.525, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:07.525, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.525, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.557, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:07.557, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:07.557, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.557, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.591, level=WARN, class = Host, msg = Match timeout 33 ms(0)
time=06/07 09:48:07.591, level=WARN, class = BtromHandshake, timeout = 33, message = response timeout
time=06/07 09:48:07.591, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.591, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.622, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:07.622, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:07.622, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.622, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.653, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:07.653, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:07.653, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.653, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.679, level=WARN, class = Host, msg = Match timeout 25 ms(0)
time=06/07 09:48:07.711, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:07.711, level=WARN, class = BtromHandshake, timeout = 57, message = response timeout
time=06/07 09:48:07.711, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.711, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.742, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:07.742, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:07.742, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.742, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.773, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:07.773, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:07.773, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.773, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.804, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:07.804, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:07.805, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.805, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.836, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:07.836, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:07.836, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.836, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.867, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:07.867, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:07.867, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.867, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.898, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:07.898, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:07.898, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.898, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.929, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:07.929, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:07.929, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.929, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.960, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:07.960, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:07.960, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.960, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:07.992, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:07.992, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:07.992, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:07.992, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.023, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:08.023, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:08.023, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.023, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.054, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:08.054, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:08.055, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.055, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.086, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:08.086, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:08.086, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.086, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.108, level=WARN, class = Host, msg = Match timeout 21 ms(0)
time=06/07 09:48:08.139, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:08.139, level=WARN, class = BtromHandshake, timeout = 52, message = response timeout
time=06/07 09:48:08.139, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.139, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.170, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:08.170, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:08.170, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.170, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.201, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:08.201, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:08.202, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.202, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.233, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:08.233, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:08.233, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.233, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.264, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:08.264, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:08.264, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.264, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.295, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:08.295, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:08.295, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.295, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.326, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:08.326, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:08.327, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.327, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.358, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:08.358, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:08.358, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.358, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.389, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:08.389, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:08.389, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.389, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.420, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:08.420, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:08.420, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.420, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.451, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:08.451, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:08.452, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.452, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.482, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:08.482, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:08.483, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.483, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.514, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:08.514, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:08.514, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.514, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.545, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:08.545, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:08.545, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.545, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.576, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:08.576, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:08.577, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.577, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.607, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:08.607, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:08.608, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.608, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.641, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=06/07 09:48:08.641, level=WARN, class = BtromHandshake, timeout = 33, message = response timeout
time=06/07 09:48:08.641, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.641, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.671, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:08.671, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:08.672, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.672, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.703, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:08.703, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:08.703, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.703, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.734, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:08.734, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:08.734, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.734, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.764, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:08.764, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:08.765, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.765, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.795, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:08.795, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:08.796, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.796, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.827, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:08.827, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:08.827, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.827, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.858, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:08.858, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:08.858, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.858, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.890, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:08.890, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:08.890, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.890, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.921, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:08.921, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:08.921, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.921, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.953, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:08.953, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:08.953, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.953, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:08.983, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:08.983, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:08.983, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:08.983, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.014, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:09.014, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:09.014, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.014, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.045, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:09.045, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:09.045, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.045, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.063, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:48:09.094, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:09.094, level=WARN, class = BtromHandshake, timeout = 49, message = response timeout
time=06/07 09:48:09.094, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.094, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.125, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:09.125, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:09.126, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.126, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.157, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:09.157, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:09.158, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.158, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.176, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:48:09.207, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:09.207, level=WARN, class = BtromHandshake, timeout = 49, message = response timeout
time=06/07 09:48:09.207, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.207, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.238, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:09.238, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:09.238, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.238, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.269, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:09.269, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:09.270, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.270, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.300, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:09.301, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:09.301, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.301, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.332, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:09.332, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:09.332, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.332, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.363, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:09.363, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:09.363, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.363, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.394, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:09.394, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:09.394, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.394, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.425, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:09.425, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:09.426, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.426, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.457, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:09.457, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:09.457, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.457, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.488, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:09.488, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:09.488, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.488, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.519, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:09.519, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:09.520, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.520, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.550, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:09.550, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:09.551, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.551, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.582, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:09.582, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:09.582, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.582, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.613, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:09.613, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:09.613, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.613, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.644, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:09.644, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:09.644, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.644, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.676, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:09.676, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:09.676, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.676, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.709, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=06/07 09:48:09.709, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=06/07 09:48:09.709, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.709, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.740, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:09.740, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:09.741, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.741, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.772, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:09.772, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:09.772, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.772, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.803, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:09.803, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:09.803, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.803, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.834, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:09.834, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:09.834, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.834, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.865, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:09.865, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:09.865, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.865, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.896, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:09.896, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:09.897, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.897, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.928, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:09.928, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:09.928, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.928, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.959, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:09.959, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:09.959, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.959, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:09.990, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:09.990, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:09.990, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:09.990, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.021, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:10.021, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:10.022, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.022, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.053, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:10.053, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:10.053, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.053, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.084, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:10.084, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:10.084, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.084, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.115, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:10.115, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:10.115, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.115, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.146, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:10.146, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:10.147, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.147, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.178, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:10.178, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:10.178, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.178, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.209, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:10.209, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:10.210, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.210, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.242, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=06/07 09:48:10.242, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=06/07 09:48:10.243, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.243, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.274, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:10.274, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:10.274, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.274, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.305, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:10.305, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:10.305, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.305, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.336, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:10.336, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:10.336, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.336, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.367, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:10.367, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:10.368, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.368, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.398, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:10.398, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:10.399, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.399, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.430, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:10.430, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:10.430, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.430, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.461, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:10.461, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:10.461, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.461, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.492, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:10.492, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:10.493, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.493, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.524, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:10.524, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:10.524, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.524, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.555, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:10.555, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:10.555, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.555, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.586, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:10.586, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:10.586, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.586, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.617, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:10.617, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:10.618, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.618, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.649, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:10.649, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:10.649, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.649, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.680, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:10.680, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:10.680, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.680, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.711, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:10.711, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:10.711, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.711, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.742, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:10.742, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:10.742, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.742, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.760, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=06/07 09:48:10.792, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:10.792, level=WARN, class = BtromHandshake, timeout = 49, message = response timeout
time=06/07 09:48:10.792, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.792, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.823, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:10.823, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:10.823, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.823, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.854, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:10.854, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:10.854, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.854, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.885, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:10.885, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:10.885, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.885, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.916, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:10.916, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:10.916, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.916, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.947, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:10.947, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:10.948, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.948, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:10.979, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:10.979, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:10.979, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:10.979, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.010, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:11.010, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:11.010, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.010, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.041, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:11.041, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:11.041, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.041, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.073, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:11.073, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:11.073, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.073, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.104, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:11.104, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:11.104, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.104, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.135, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:11.135, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:11.135, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.135, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.166, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:11.166, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:11.166, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.166, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.198, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:11.198, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:11.198, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.198, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.229, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:11.229, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:11.229, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.229, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.260, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:11.260, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:11.260, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.260, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.295, level=WARN, class = Host, msg = Match timeout 34 ms(0)
time=06/07 09:48:11.295, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=06/07 09:48:11.295, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.295, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.326, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:11.326, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:11.327, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.327, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.358, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:11.358, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:11.358, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.358, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.389, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:11.389, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:11.389, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.389, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.420, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:11.420, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:11.420, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.420, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.451, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:11.451, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:11.451, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.451, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.483, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:11.483, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:11.483, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.483, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.514, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:11.514, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:11.514, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.514, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.545, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:11.545, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:11.545, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.545, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.576, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:11.576, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:11.576, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.576, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.607, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:11.607, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:11.608, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.608, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.639, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:11.639, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:11.639, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.639, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.670, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:11.670, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:11.670, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.670, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.701, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:11.701, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:11.701, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.701, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.732, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:11.732, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:11.733, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.733, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.763, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:11.763, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:11.764, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.764, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.793, level=WARN, class = Host, msg = Match timeout 28 ms(0)
time=06/07 09:48:11.828, level=WARN, class = Host, msg = Match timeout 35 ms(0)
time=06/07 09:48:11.828, level=WARN, class = BtromHandshake, timeout = 64, message = response timeout
time=06/07 09:48:11.828, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.828, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.859, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:11.859, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:11.859, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.859, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.890, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:11.890, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:11.891, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.891, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.922, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:11.922, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:11.922, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.922, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.953, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:11.953, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:11.953, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.953, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:11.984, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:11.984, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:11.984, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:11.984, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.015, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:12.015, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:12.016, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.016, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.047, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:12.047, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:12.047, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.047, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.078, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:12.078, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:12.078, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.078, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.109, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:12.109, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:12.109, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.109, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.140, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:12.140, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:12.141, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.141, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.172, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:12.172, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:12.172, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.172, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.203, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:12.203, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:12.203, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.203, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.234, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:12.234, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:12.234, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.234, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.265, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:12.265, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:12.266, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.266, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.297, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:12.297, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:12.297, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.297, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.327, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:12.327, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:12.328, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.328, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.361, level=WARN, class = Host, msg = Match timeout 33 ms(0)
time=06/07 09:48:12.361, level=WARN, class = BtromHandshake, timeout = 33, message = response timeout
time=06/07 09:48:12.362, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.362, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.393, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:12.393, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:12.393, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.393, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.424, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:12.424, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:12.424, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.424, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.455, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:12.455, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:12.455, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.455, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.486, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:12.486, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:12.487, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.487, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.518, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:12.518, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:12.518, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.518, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.549, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:12.549, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:12.549, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.549, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.580, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:12.580, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:12.580, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.580, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.611, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:12.611, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:12.611, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.611, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.642, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:12.642, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:12.643, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.643, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.684, level=WARN, class = Host, msg = Match timeout 41 ms(0)
time=06/07 09:48:12.684, level=WARN, class = BtromHandshake, timeout = 41, message = response timeout
time=06/07 09:48:12.684, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.684, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.715, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:12.715, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:12.715, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.715, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.747, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:12.747, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:12.747, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.747, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.778, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:12.778, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:12.778, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.778, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.809, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:12.809, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:12.810, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.810, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.840, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:12.840, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:12.841, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.841, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.879, level=WARN, class = Host, msg = Match timeout 38 ms(0)
time=06/07 09:48:12.879, level=WARN, class = BtromHandshake, timeout = 38, message = response timeout
time=06/07 09:48:12.880, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.880, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.911, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:12.911, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:12.911, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.911, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.942, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:12.942, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:12.942, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.942, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:12.973, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:12.973, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:12.973, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:12.973, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:13.004, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:13.004, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:13.005, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:13.005, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:13.036, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:13.036, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:13.036, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:13.036, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:13.067, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:13.067, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:13.067, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:13.067, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:13.098, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:13.098, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:13.098, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:13.098, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:13.129, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:13.129, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:13.129, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:13.129, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:13.160, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:13.160, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:13.161, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:13.161, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:13.192, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:13.192, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:13.192, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:13.192, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:13.223, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:13.223, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:13.223, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:13.223, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:13.254, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:13.254, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:13.254, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:13.255, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:13.285, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:13.285, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:13.286, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:13.286, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:13.317, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:13.317, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:13.317, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:13.317, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:13.348, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:13.348, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:13.348, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:13.348, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:13.379, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:13.379, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:13.379, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:13.379, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:13.413, level=WARN, class = Host, msg = Match timeout 33 ms(0)
time=06/07 09:48:13.413, level=WARN, class = BtromHandshake, timeout = 33, message = response timeout
time=06/07 09:48:13.413, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:13.413, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:13.444, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:13.444, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:13.444, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:13.444, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:13.475, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:13.475, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:13.475, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:13.475, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:13.507, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=06/07 09:48:13.507, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=06/07 09:48:13.507, level=DEBUG, [COM4] Write(4)
time=06/07 09:48:13.507, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=06/07 09:48:13.538, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=06/07 09:48:13.538, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=06/07 09:48:13.538, level=ERROR, class = BtromHandshake, result = fail, timeout = 10010, error_message = btrom handshake timeout
time=06/07 09:48:13.538, level=ERROR, D:\project\crossover\src\task\flash\download_da_uart.cc:141(-1): class = DownloadDa_UART, error_message = btrom handshake fail
time=06/07 09:48:13.538, level=ERROR, class = Task, task_name = DownloadDa_UART, error_message = Fail to execute RunTask()
time=06/07 09:48:13.538, level=DEBUG, class = DownloadDa_UART, task_time = 10.012
time=06/07 09:48:13.538, level=DEBUG, class = Controller, RemoveObserver = callback
time=06/07 09:48:13.538, level=DEBUG, class = CallbackManager, deregister = callback
time=06/07 09:48:13.538, level=DEBUG, class = DisconnectDUT, state = init data, result = ok
time=06/07 09:48:13.647, level=DEBUG, class = SerialHost, state = close
time=06/07 09:48:13.647, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=06/07 09:48:13.647, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=06/07 09:48:13.647, level=DEBUG, class = UartDev, state = disconnect
time=06/07 09:48:13.647, level=DEBUG, class = DisconnectDUT, state = disconnect, device_name = DUT, device_type = UART
time=06/07 09:48:13.647, level=DEBUG, class = DisconnectDUT, task_time = 0.109
time=06/07 09:48:13.647, level=DEBUG, class = LogService, state = CloseLogFile, pass_fail = 1, postfix = 
