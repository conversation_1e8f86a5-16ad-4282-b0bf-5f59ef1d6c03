time=04/28 09:25:50.525, level=INFO, class = LogService, tool_version = 0.2.12/air_bta_ic_premium_g3
time=04/28 09:25:50.521, level=INFO, class = Controller, SetLogLevel = 3
time=04/28 09:25:50.521, level=DEBUG, class = ToolLogLevel, log_level = 3
time=04/28 09:25:50.521, level=DEBUG, class = ToolLogLevel, task_time = 0.000
time=04/28 09:25:50.521, level=DEBUG, class = ConnectDUT, state = init data, result = ok
time=04/28 09:25:50.521, level=DEBUG, class = SerialHost, phy_name = UART, trans_name = h4
time=04/28 09:25:50.521, level=DEBUG, class = Physical, type = 1, state = create
time=04/28 09:25:50.521, level=DEBUG, class = Transport, type = 4, state = create
time=04/28 09:25:50.522, level=INFO, class = UartPhy, desc = USB Serial Port (COM4)
time=04/28 09:25:50.522, level=INFO, class = UartPhy, instance_id = FTDIBUS\VID_0403+PID_6015+DQ014GZWA\0000
time=04/28 09:25:50.522, level=INFO, class = UartPhy, port = 4, baud = 3000000, handshake = 0, read/timeout = (3, 0)
time=04/28 09:25:50.560, level=DEBUG, class = SerialHost, state = open
time=04/28 09:25:50.560, level=DEBUG, class = UartDev, state = connect, ret = 0
time=04/28 09:25:50.560, level=DEBUG, class = ConnectDUT, state = connect, device_name = DUT, device_type = UART, result = ok
time=04/28 09:25:50.560, level=DEBUG, class = ConnectDUT, task_time = 0.039
time=04/28 09:25:50.561, level=DEBUG, class = CallbackManager, register = DUT, filter = 0, cb = 0x004D2C00
time=04/28 09:25:50.561, level=DEBUG, class = UartDev, SetBaudrate = 115200
time=04/28 09:25:50.561, level=DEBUG, class = Host, SwitchTransport = bypass
time=04/28 09:25:50.561, level=DEBUG, class = Transport, type = 1, state = create
time=04/28 09:25:50.561, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=04/28 09:25:50.563, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:50.563, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:50.580, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=04/28 09:25:50.598, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=04/28 09:25:50.598, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=04/28 09:25:50.598, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:50.598, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:50.615, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=04/28 09:25:50.632, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=04/28 09:25:50.632, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=04/28 09:25:50.633, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:50.633, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:50.650, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=04/28 09:25:50.667, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=04/28 09:25:50.667, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=04/28 09:25:50.667, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:50.667, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:50.685, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=04/28 09:25:50.703, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=04/28 09:25:50.703, level=WARN, class = BtromHandshake, timeout = 35, message = response timeout
time=04/28 09:25:50.703, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:50.703, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:50.720, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=04/28 09:25:50.738, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=04/28 09:25:50.738, level=WARN, class = BtromHandshake, timeout = 34, message = response timeout
time=04/28 09:25:50.738, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:50.738, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:50.755, level=WARN, class = Host, msg = Match timeout 17 ms(0)
time=04/28 09:25:50.778, level=WARN, class = Host, msg = Match timeout 23 ms(0)
time=04/28 09:25:50.778, level=WARN, class = BtromHandshake, timeout = 40, message = response timeout
time=04/28 09:25:50.778, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:50.778, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:50.810, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:50.810, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:50.810, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:50.810, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:50.841, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:50.841, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:50.841, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:50.841, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:50.872, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:50.872, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:50.872, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:50.872, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:50.903, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:50.903, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:50.903, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:50.903, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:50.933, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:50.964, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:50.964, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=04/28 09:25:50.964, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:50.964, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:50.996, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:50.996, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:50.996, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:50.996, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.027, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:51.027, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:51.027, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.027, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.058, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:51.058, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:51.059, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.059, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.090, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:51.090, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:51.090, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.090, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.121, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:51.121, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:51.122, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.122, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.152, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:51.152, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:51.152, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.152, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.184, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:51.184, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:51.184, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.184, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.215, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:51.215, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:51.215, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.215, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.245, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:51.276, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:51.276, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=04/28 09:25:51.276, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.276, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.307, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:51.307, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:51.307, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.307, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.338, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:51.338, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:51.338, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.338, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.369, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:51.369, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:51.370, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.370, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.399, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:51.430, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:51.430, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=04/28 09:25:51.430, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.430, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.461, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:51.462, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:51.462, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.462, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.493, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:51.493, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:51.493, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.493, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.525, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:51.525, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:51.525, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.525, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.556, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:51.556, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:51.556, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.556, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.587, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:51.587, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:51.587, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.587, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.619, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:51.619, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:51.619, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.619, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.651, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:51.651, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:51.651, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.651, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.682, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:51.682, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:51.682, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.682, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.713, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:51.713, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:51.713, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.713, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.744, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:51.744, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:51.744, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.744, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.775, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:51.775, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:51.775, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.775, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.805, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:51.805, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:51.806, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.806, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.836, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:51.836, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:51.836, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.836, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.867, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:51.867, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:51.867, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.867, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.899, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:51.899, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:51.899, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.899, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.929, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:51.929, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:51.930, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.930, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.961, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:51.961, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:51.961, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.961, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:51.992, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:51.992, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:51.992, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:51.992, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.023, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:52.023, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:52.024, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.024, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.054, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:52.054, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:52.054, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.054, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.085, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:52.085, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:52.085, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.085, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.116, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:52.116, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:52.117, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.117, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.146, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:52.178, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:52.178, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=04/28 09:25:52.178, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.178, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.208, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:52.239, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:52.239, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=04/28 09:25:52.239, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.239, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.270, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:52.270, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:52.270, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.271, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.301, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:52.301, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:52.301, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.301, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.332, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:52.332, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:52.332, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.332, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.363, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:52.363, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:52.363, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.363, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.395, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:52.395, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:52.395, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.395, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.425, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:52.456, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:52.456, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=04/28 09:25:52.457, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.457, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.488, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:52.488, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:52.488, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.488, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.519, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:52.519, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:52.551, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.551, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.581, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:52.581, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:52.581, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.581, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.611, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:52.611, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:52.612, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.612, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.643, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:52.643, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:52.643, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.643, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.673, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:52.673, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:52.674, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.674, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.705, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:52.705, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:52.706, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.706, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.736, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:52.736, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:52.737, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.737, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.767, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:52.767, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:52.768, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.768, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.799, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:52.799, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:52.800, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.800, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.831, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:52.831, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:52.832, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.832, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.863, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:52.863, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:52.863, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.863, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.895, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:52.895, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:52.895, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.895, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.927, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:52.927, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:52.927, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.927, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.957, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:52.957, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:52.957, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.957, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:52.988, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:52.988, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:52.989, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:52.989, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.020, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:53.020, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:53.020, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.020, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.051, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:53.051, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:53.051, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.052, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.083, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:53.083, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:53.083, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.083, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.115, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:53.115, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:53.115, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.115, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.145, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:53.145, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:53.145, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.145, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.177, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:53.177, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:53.177, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.177, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.207, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:53.207, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:53.208, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.208, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.239, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:53.239, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:53.239, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.239, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.270, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:53.270, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:53.270, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.270, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.302, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:53.302, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:53.302, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.302, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.333, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:53.333, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:53.333, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.333, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.363, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:53.363, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:53.364, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.364, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.394, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:53.394, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:53.394, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.394, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.426, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:53.426, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:53.426, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.426, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.456, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:53.488, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=04/28 09:25:53.488, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=04/28 09:25:53.488, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.488, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.518, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:53.518, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:53.519, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.519, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.550, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:53.550, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:53.550, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.550, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.580, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:53.580, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:53.580, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.580, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.611, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:53.611, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:53.611, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.611, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.642, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:53.642, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:53.642, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.642, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.673, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:53.673, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:53.673, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.673, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.704, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:53.704, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:53.705, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.705, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.735, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:53.735, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:53.735, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.735, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.766, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:53.766, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:53.767, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.767, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.798, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:53.798, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:53.798, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.798, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.829, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:53.829, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:53.829, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.829, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.860, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:53.860, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:53.860, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.860, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.892, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:53.892, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:53.892, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.892, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.923, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:53.923, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:53.923, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.923, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.954, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:53.954, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:53.955, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.955, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:53.986, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:53.986, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:53.986, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:53.986, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.017, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:54.017, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:54.017, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.017, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.048, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:54.048, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:54.048, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.048, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.079, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:54.079, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:54.079, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.079, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.110, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:54.110, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:54.110, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.110, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.142, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:54.142, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:54.142, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.142, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.174, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:54.174, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:54.174, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.174, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.205, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:54.205, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:54.205, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.205, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.235, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:54.266, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:54.266, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=04/28 09:25:54.267, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.267, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.297, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:54.297, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:54.298, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.298, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.328, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:54.328, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:54.329, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.329, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.359, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:54.359, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:54.359, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.359, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.391, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:54.391, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:54.391, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.391, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.422, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:54.422, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:54.422, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.422, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.453, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:54.453, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:54.453, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.453, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.484, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:54.484, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:54.485, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.485, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.515, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:54.515, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:54.515, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.515, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.545, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:54.576, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:54.576, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=04/28 09:25:54.577, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.577, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.608, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:54.608, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:54.609, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.609, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.639, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:54.639, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:54.640, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.640, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.670, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:54.670, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:54.670, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.670, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.702, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:54.702, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:54.702, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.702, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.733, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:54.733, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:54.733, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.733, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.763, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:54.763, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:54.764, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.764, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.794, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:54.794, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:54.794, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.794, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.826, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:54.826, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:54.826, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.826, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.857, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:54.857, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:54.857, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.857, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.888, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:54.888, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:54.888, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.888, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.904, level=DEBUG, [COM4] Read(20)
time=04/28 09:25:54.904, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 0.00 B/s
time=04/28 09:25:54.904, level=INFO, [RxP 20] 24504149523338322C312A32450D0A2450414952
time=04/28 09:25:54.919, level=DEBUG, [COM4] Read(30)
time=04/28 09:25:54.919, level=INFO, [RxP 30] 3130302C312C302A33410D0A24504149523036322C312C302A33460D0A24
time=04/28 09:25:54.919, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:54.919, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.919, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.935, level=DEBUG, [COM4] Read(30)
time=04/28 09:25:54.935, level=INFO, [RxP 30] 504149523036322C352C302A33420D0A24504149523036322C332C302A33
time=04/28 09:25:54.950, level=DEBUG, [COM4] Read(24)
time=04/28 09:25:54.950, level=INFO, [RxP 24] 440D0A24504149523038302C302A32450D0A245041495230
time=04/28 09:25:54.950, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:54.950, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.950, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:54.965, level=DEBUG, [COM4] Read(30)
time=04/28 09:25:54.966, level=INFO, [RxP 30] 36362C312C312C312C312C312C302A33420D0A24504149523038312A3333
time=04/28 09:25:54.981, level=DEBUG, [COM4] Read(17)
time=04/28 09:25:54.981, level=INFO, [RxP 17] 0D0A24504149523439302C312A32410D0A
time=04/28 09:25:54.981, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:54.981, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:54.981, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.013, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:55.013, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:55.013, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.013, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.044, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:55.044, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:55.044, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.044, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.075, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:55.075, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:55.075, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.075, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.105, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:55.105, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:55.105, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.105, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.136, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:55.136, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:55.137, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.137, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.168, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:55.168, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:55.168, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.168, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.199, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:55.199, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:55.199, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.199, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.230, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:55.230, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:55.230, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.230, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.261, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:55.261, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:55.261, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.261, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.292, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:55.292, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:55.292, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.292, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.323, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:55.323, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:55.323, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.323, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.339, level=DEBUG, [COM4] Read(2)
time=04/28 09:25:55.339, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 300.92 B/s
time=04/28 09:25:55.339, level=INFO, [RxP 2] 2450
time=04/28 09:25:55.354, level=DEBUG, [COM4] Read(24)
time=04/28 09:25:55.354, level=INFO, [RxP 24] 4149523030322A33380D0A24504149523439312A33360D0A
time=04/28 09:25:55.354, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:55.354, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.354, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.385, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:55.385, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:55.385, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.385, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.416, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:55.416, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:55.416, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.416, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.447, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:55.447, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:55.448, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.448, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.479, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:55.479, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:55.479, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.479, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.510, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:55.510, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:55.510, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.510, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.541, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:55.541, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:55.541, level=DEBUG, [COM4] Read(12)
time=04/28 09:25:55.541, level=INFO, class = UartPhy, port = COM4, state = read, data_rate = 118.86 B/s
time=04/28 09:25:55.541, level=INFO, [RxP 12] 24504149523030322A33380D
time=04/28 09:25:55.541, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.541, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.557, level=DEBUG, [COM4] Read(14)
time=04/28 09:25:55.557, level=INFO, [RxP 14] 0A24504149523439312A33360D0A
time=04/28 09:25:55.588, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:55.588, level=WARN, class = BtromHandshake, timeout = 46, message = response timeout
time=04/28 09:25:55.588, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.588, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.619, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:55.619, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:55.619, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.619, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.650, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:55.650, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:55.650, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.650, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.681, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:55.681, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:55.681, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.681, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.712, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:55.712, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:55.712, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.712, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.743, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:55.743, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:55.743, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.743, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.775, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:55.775, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:55.775, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.775, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.805, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:55.805, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:55.806, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.806, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.837, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:55.837, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:55.837, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.837, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.867, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:55.867, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:55.867, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.867, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.899, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:55.899, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:55.899, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.899, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.929, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:55.929, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:55.930, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.930, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.960, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:55.960, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:55.961, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.961, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:55.991, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:55.991, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:55.991, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:55.991, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.022, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:56.022, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:56.022, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.022, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.054, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:56.054, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:56.054, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.054, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.085, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:56.085, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:56.085, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.085, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.115, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:56.145, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:56.145, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=04/28 09:25:56.145, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.145, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.176, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:56.176, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:56.176, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.176, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.207, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:56.207, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:56.207, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.207, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.238, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:56.238, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:56.238, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.238, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.269, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:56.269, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:56.269, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.269, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.300, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:56.300, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:56.301, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.301, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.331, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:56.332, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:56.332, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.332, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.363, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:56.363, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:56.363, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.363, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.394, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:56.394, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:56.395, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.395, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.425, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:56.425, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:56.426, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.426, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.456, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:56.456, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:56.456, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.456, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.487, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:56.487, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:56.487, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.487, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.517, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:56.517, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:56.518, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.518, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.548, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:56.548, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:56.549, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.549, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.581, level=WARN, class = Host, msg = Match timeout 32 ms(0)
time=04/28 09:25:56.581, level=WARN, class = BtromHandshake, timeout = 32, message = response timeout
time=04/28 09:25:56.581, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.581, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.611, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:56.642, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:56.642, level=WARN, class = BtromHandshake, timeout = 60, message = response timeout
time=04/28 09:25:56.642, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.642, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.674, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:56.674, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:56.674, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.674, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.705, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:56.705, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:56.705, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.705, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.736, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:56.736, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:56.736, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.736, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.767, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:56.767, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:56.768, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.768, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.798, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:56.798, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:56.798, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.798, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.829, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:56.829, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:56.829, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.829, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.859, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:56.860, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:56.860, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.860, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.891, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:56.891, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:56.891, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.891, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.922, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:56.922, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:56.922, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.922, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.953, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:56.953, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:56.953, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.953, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:56.984, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:56.984, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:56.985, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:56.985, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.015, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:57.015, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:57.016, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.016, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.046, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:57.046, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:57.046, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.046, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.077, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:57.077, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:57.077, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.077, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.109, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:57.109, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:57.109, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.109, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.139, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:57.171, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:57.171, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=04/28 09:25:57.171, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.171, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.201, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:57.233, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:57.233, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=04/28 09:25:57.233, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.233, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.264, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:57.264, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:57.265, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.265, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.296, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:57.296, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:57.296, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.296, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.327, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:57.327, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:57.327, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.327, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.358, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:57.358, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:57.358, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.358, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.389, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:57.389, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:57.389, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.389, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.420, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:57.420, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:57.420, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.420, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.451, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:57.451, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:57.451, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.451, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.482, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:57.482, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:57.482, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.482, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.513, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:57.513, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:57.513, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.513, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.544, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:57.544, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:57.545, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.545, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.575, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:57.575, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:57.576, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.576, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.606, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:57.606, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:57.607, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.607, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.638, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:57.638, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:57.638, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.638, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.668, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:57.668, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:57.669, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.669, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.699, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:57.699, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:57.700, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.700, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.731, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:57.731, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:57.731, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.732, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.762, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:57.762, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:57.763, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.763, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.793, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:57.793, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:57.794, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.794, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.824, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:57.824, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:57.824, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.824, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.856, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:57.856, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:57.856, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.856, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.886, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:57.886, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:57.887, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.887, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.917, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:57.917, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:57.917, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.917, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.948, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:57.948, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:57.948, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.948, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:57.979, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:57.979, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:57.980, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:57.980, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.010, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:58.010, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:58.010, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.010, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.041, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:58.041, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:58.042, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.042, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.073, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:58.073, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:58.073, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.074, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.105, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:58.105, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:58.105, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.105, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.136, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:58.136, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:58.136, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.136, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.168, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:58.168, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:58.168, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.168, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.200, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:58.200, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:58.200, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.200, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.230, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:58.230, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:58.231, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.231, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.261, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:58.261, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:58.261, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.261, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.293, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:58.293, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:58.293, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.293, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.324, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:58.324, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:58.324, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.324, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.355, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:58.355, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:58.355, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.355, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.386, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:58.386, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:58.386, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.386, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.417, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:58.417, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:58.418, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.418, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.448, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:58.448, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:58.448, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.448, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.479, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:58.479, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:58.480, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.480, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.511, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:58.511, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:58.511, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.511, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.542, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:58.542, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:58.542, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.542, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.572, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:58.572, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:58.573, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.573, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.604, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:58.604, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:58.604, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.604, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.635, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:58.635, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:58.635, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.635, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.666, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:58.666, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:58.666, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.666, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.697, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:58.697, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:58.697, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.697, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.728, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:58.728, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:58.728, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.728, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.760, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:58.760, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:58.760, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.760, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.791, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:58.791, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:58.791, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.791, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.822, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:58.822, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:58.822, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.822, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.853, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:58.853, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:58.854, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.854, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.885, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:58.885, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:58.885, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.885, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.916, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:58.916, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:58.916, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.916, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.947, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:58.947, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:58.947, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.947, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:58.978, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:58.978, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:58.978, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:58.978, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.009, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:59.009, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:59.009, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.009, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.040, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:59.040, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:59.040, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.040, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.071, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:59.071, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:59.072, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.072, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.102, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:59.102, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:59.102, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.102, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.133, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:59.133, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:59.133, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.133, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.165, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:59.165, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:59.165, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.165, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.195, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:59.195, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:59.196, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.196, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.226, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:59.226, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:59.226, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.226, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.257, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:59.257, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:59.257, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.258, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.288, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:59.288, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:59.288, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.288, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.319, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:59.319, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:59.319, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.319, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.351, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:59.351, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:59.351, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.351, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.382, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:59.382, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:59.382, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.382, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.413, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:59.414, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:59.414, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.414, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.445, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:59.445, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:59.445, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.445, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.476, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:59.476, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:59.476, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.476, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.507, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:59.507, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:59.508, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.508, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.538, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:59.538, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:59.539, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.539, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.569, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:59.569, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:59.570, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.570, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.601, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:59.601, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:59.601, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.601, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.632, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:59.632, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:59.632, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.632, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.663, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:59.663, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:59.663, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.663, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.694, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:59.694, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:59.694, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.694, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.725, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:59.725, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:59.725, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.725, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.756, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:59.756, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:59.756, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.756, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.787, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:59.787, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:59.787, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.787, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.818, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:59.818, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:59.819, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.819, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.849, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:59.849, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:59.849, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.849, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.880, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:59.880, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:25:59.881, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.881, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.911, level=WARN, class = Host, msg = Match timeout 29 ms(0)
time=04/28 09:25:59.943, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:25:59.943, level=WARN, class = BtromHandshake, timeout = 61, message = response timeout
time=04/28 09:25:59.943, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.943, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:25:59.974, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:25:59.974, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:25:59.974, level=DEBUG, [COM4] Write(4)
time=04/28 09:25:59.974, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:26:00.005, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:26:00.005, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:26:00.005, level=DEBUG, [COM4] Write(4)
time=04/28 09:26:00.005, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:26:00.037, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:26:00.037, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:26:00.037, level=DEBUG, [COM4] Write(4)
time=04/28 09:26:00.037, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:26:00.068, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:26:00.068, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:26:00.068, level=DEBUG, [COM4] Write(4)
time=04/28 09:26:00.068, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:26:00.099, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:26:00.099, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:26:00.099, level=DEBUG, [COM4] Write(4)
time=04/28 09:26:00.099, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:26:00.130, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:26:00.130, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:26:00.130, level=DEBUG, [COM4] Write(4)
time=04/28 09:26:00.130, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:26:00.161, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:26:00.161, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:26:00.161, level=DEBUG, [COM4] Write(4)
time=04/28 09:26:00.161, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:26:00.192, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:26:00.192, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:26:00.192, level=DEBUG, [COM4] Write(4)
time=04/28 09:26:00.192, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:26:00.223, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:26:00.223, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:26:00.223, level=DEBUG, [COM4] Write(4)
time=04/28 09:26:00.223, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:26:00.254, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:26:00.254, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:26:00.254, level=DEBUG, [COM4] Write(4)
time=04/28 09:26:00.254, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:26:00.285, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:26:00.285, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:26:00.285, level=DEBUG, [COM4] Write(4)
time=04/28 09:26:00.285, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:26:00.315, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:26:00.315, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:26:00.316, level=DEBUG, [COM4] Write(4)
time=04/28 09:26:00.316, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:26:00.346, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:26:00.346, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:26:00.346, level=DEBUG, [COM4] Write(4)
time=04/28 09:26:00.346, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:26:00.377, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:26:00.377, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:26:00.377, level=DEBUG, [COM4] Write(4)
time=04/28 09:26:00.377, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:26:00.408, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:26:00.408, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:26:00.409, level=DEBUG, [COM4] Write(4)
time=04/28 09:26:00.409, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:26:00.439, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:26:00.439, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:26:00.440, level=DEBUG, [COM4] Write(4)
time=04/28 09:26:00.440, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:26:00.470, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:26:00.470, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:26:00.470, level=DEBUG, [COM4] Write(4)
time=04/28 09:26:00.470, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:26:00.501, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:26:00.501, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:26:00.501, level=DEBUG, [COM4] Write(4)
time=04/28 09:26:00.501, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:26:00.533, level=WARN, class = Host, msg = Match timeout 31 ms(0)
time=04/28 09:26:00.533, level=WARN, class = BtromHandshake, timeout = 31, message = response timeout
time=04/28 09:26:00.533, level=DEBUG, [COM4] Write(4)
time=04/28 09:26:00.533, level=DEBUG, [COM4][write 4 BROM_HANDSHAKE] A00A5005
time=04/28 09:26:00.564, level=WARN, class = Host, msg = Match timeout 30 ms(0)
time=04/28 09:26:00.564, level=WARN, class = BtromHandshake, timeout = 30, message = response timeout
time=04/28 09:26:00.564, level=ERROR, class = BtromHandshake, result = fail, timeout = 10001, error_message = btrom handshake timeout
time=04/28 09:26:00.564, level=ERROR, D:\project\crossover\src\task\flash\download_da_uart.cc:141(-1): class = DownloadDa_UART, error_message = btrom handshake fail
time=04/28 09:26:00.564, level=ERROR, class = Task, task_name = DownloadDa_UART, error_message = Fail to execute RunTask()
time=04/28 09:26:00.564, level=DEBUG, class = DownloadDa_UART, task_time = 10.004
time=04/28 09:26:00.564, level=DEBUG, class = Controller, RemoveObserver = callback
time=04/28 09:26:00.564, level=DEBUG, class = CallbackManager, deregister = callback
time=04/28 09:26:00.564, level=DEBUG, class = DisconnectDUT, state = init data, result = ok
time=04/28 09:26:00.689, level=DEBUG, class = SerialHost, state = close
time=04/28 09:26:00.689, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=04/28 09:26:00.689, level=DEBUG, class = Host, msg = Clear buffer and queue.
time=04/28 09:26:00.689, level=DEBUG, class = UartDev, state = disconnect
time=04/28 09:26:00.689, level=DEBUG, class = DisconnectDUT, state = disconnect, device_name = DUT, device_type = UART
time=04/28 09:26:00.689, level=DEBUG, class = DisconnectDUT, task_time = 0.125
time=04/28 09:26:00.689, level=DEBUG, class = LogService, state = CloseLogFile, pass_fail = 1, postfix = 
