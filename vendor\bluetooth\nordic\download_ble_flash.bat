@echo off
setlocal
set PATH=%PATH%;C:\Program Files (x86)\SEGGER\JLink

rem 设置下载脚本download_bin.bat的路径
set DOWNLOAD_SCRIPT=..\WR02\tools\segger\download_ble_hex.bat

rem 设置lcpu.bin文件的路径
set HEX_FILE=examples\ble_nrf52_fw\iGS800_ble_all.hex

rem 检查BIN_FILE是否存在
if not exist "%HEX_FILE%" (
    echo 错误：BIN_FILE "%HEX_FILE%" 不存在。
    exit /b 1
)

rem 检查DOWNLOAD_SCRIPT是否存在
if not exist "%DOWNLOAD_SCRIPT%" (
    echo 错误：DOWNLOAD_SCRIPT "%DOWNLOAD_SCRIPT%" 不存在。
    exit /b 1
)

rem 使用适当的参数调用download_bin.bat脚本
call "%DOWNLOAD_SCRIPT%" "%HEX_FILE%"

rem 检查上一个命令的错误级别
if %errorlevel% neq 0 (
    echo 错误：文件下载失败。
    exit /b 1
)

echo boot download success!

pause

endlocal