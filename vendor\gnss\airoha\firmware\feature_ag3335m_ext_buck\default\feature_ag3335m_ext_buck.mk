IC_CONFIG                             = ag3335
BOARD_CONFIG                          = ag3335m_evk
IC_CONFIG_AG3335_E1                   = y
BL_FEATURE                            = bl_feature_ag3335m.mk

# debug level: none, error, warning, info and debug
MTK_DEBUG_LEVEL                       = info
MTK_NVDM_ENABLE                       = y
MTK_USB_DEMO_ENABLED                  = y
# SWLA
MTK_SWLA_ENABLE                       = y

MTK_HAL_EXT_32K_ENABLE                = y
MTK_NO_PSRAM_ENABLE                   = y

# heap dump
MTK_SUPPORT_HEAP_DEBUG                = y
MTK_SUPPORT_HEAP_DEBUG_ADVANCED       = n
# heap peak profiling
MTK_HEAP_SIZE_GUARD_ENABLE            = n

# system hang debug: none, y, o1 o2 and mp
MTK_SYSTEM_HANG_TRACER_ENABLE         = y

MTK_MEMORY_MONITOR_ENABLE             = n

# port service
MTK_PORT_SERVICE_ENABLE               = y

# ATCI
ATCI_ENABLE                           = y
MTK_AT_CMD_DISABLE                    = n

# Race
MTK_RACE_CMD_ENABLE                   = n

# GNSS Basic Config
MTK_GNSS_SERVICE_ENABLE               = y
MTK_GNSS_L5_ENABLE                    = y
MTK_GNSS_RTK_ENABLE                   = n
MTK_GNSS_NAVIC_ENABLE                 = n

# GNSS Demo Config
MTK_GNSS_SUPPORT_LOCUS                = y

# Dump
MTK_MINIDUMP_ENABLE                   = n
MTK_FULLDUMP_ENABLE                   = y

# boot reason check
MTK_BOOTREASON_CHECK_ENABLE           = y

#VRTC SRAM power source control
#y : RTC SRAM power provided by VRTC, n: RTC SRAM power controlled by HW
#y for HW RTC mode , n for SW RTC mode
#AG3335A/AG3335S don't support HW RTC mode, should never set to y.
MTK_SW_CTL_VRTC_VSRAM_POWER           = y

# VCCK External Buck Config
# This configuration describe which level your external buck is
# Three levels: low, normal, high
# If none use Vcck external buck, please set to none
MTK_VCCK_EXTERNAL_BUCK = low

# RTC Retention System Random Access Memory power down level
# Two levels: none, level1
#             none: no cell is powered down
#             level1: cell #0 is powered down
# NOTICE: 
#   - MTK_GNSS_L5_ENABLE, MTK_GNSS_NAVIC_ENABLE or AIR_GNSS_FUSION_ENABLE is mutually exclusive to this feature option.
AIR_RETSRAM_POWER_DOWN_LEVEL          = none