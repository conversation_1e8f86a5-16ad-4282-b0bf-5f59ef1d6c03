IoT_SDK_for_Location_V3.2.2.AG3335 2024/07/12 16:45:40 GMT +08:00 EPOC GAPP LayoutPartition MNL_AGP MNL_ALO MNL_BOT MNL_COM MNL_DGP MNL_FSM MNL_GPS MNL_LOP MNL_MEM MNL_MSG MNL_STR MNL_S_TAG MNL_TIM MNL_TOW MPLOG MUX_PORT MUX_USB SPP_PORT SYSLOG USB USB_CASE USB_MAIN USB_RESOURCE atci atci_gpio atci_mux_port atci_reg atci_serialport atci_sflash atcmd common hal main mnl nvdm os printf [M:hal C:error F: L: ]: aes_operate fail. [M:hal C:error F: L: ]: aes_operate fail. [M:hal C:error F: L: ]: 
0xe000e100 =0x%x [M:hal C:error F: L: ]: 
0xe000e200 =0x%x [M:hal C:error F: L: ]: 
0xe000e300 =0x%x [M:hal C:error F: L: ]: NULL input. [M:hal C:error F: L: ]: Invalid plain text length: %lu. [M:hal C:error F: L: ]: Inadequate encrypted buffer. [M:hal C:error F: L: ]: key length is %lu, invalid. It has to be 16, 24 or 32. [M:hal C:error F: L: ]: do_aes_encrypt fail. [M:hal C:error F: L: ]: NULL input. [M:hal C:error F: L: ]: Invalid encrypted text length: %lu. [M:hal C:error F: L: ]: Plain text buffer lengthL %lu is too small, encrypted length is: %lu [M:hal C:error F: L: ]: key length is %lu, invalid. It has to be 16, 24 or 32. [M:hal C:error F: L: ]: do_aes_decrypt fail [M:hal C:error F: L: ]: NULL input. [M:hal C:error F: L: ]: Invalid plain text length: %lu. [M:hal C:error F: L: ]: Inadequate encrypted buffer. [M:hal C:error F: L: ]: key length is %lu, invalid. It has to be 16, 24 or 32. [M:hal C:error F: L: ]: do_aes_encrypt fail. [M:hal C:error F: L: ]: NULL input. [M:hal C:error F: L: ]: Invalid encrypted text length: %lu. [M:hal C:error F: L: ]: Plain text buffer lengthL %lu is too small, encrypted length is: %lu [M:hal C:error F: L: ]: key length is %lu, invalid. It has to be 16, 24 or 32. [M:hal C:error F: L: ]: do_aes_decrypt fail [M:hal C:error F: L: ]: NULL input. [M:hal C:error F: L: ]: do_aes_encrypt fail. [M:hal C:error F: L: ]: irq_number = %d 
 [M:hal C:info F: L: ]: LPOSC in-use, set_osc_freq skipped!
 [M:hal C:info F: L: ]: hf_fsys_ck freq=%d
 [M:hal C:info F: L: ]: Load CAPID done, CAPID RG = %lx
 [M:hal C:info F: L: ]: DCXO LDO efuse settings [%x]
 [M:hal C:error F: L: ]: [I2C_SLAVE%d][timer callback]:invalid port=%d
 [M:hal C:error F: L: ]: [I2C_SLAVE%d][send_dma]:timeout event=%d
 [M:hal C:error F: L: ]: [I2C_SLAVE%d][send_receive]:timeout event=%d
 [M:hal C:error F: L: ]: [I2C_SLAVE%d][timer callback]: busy
 [M:hal C:error F: L: ]: [I2C_SLAVE%d][timer callback]:error status=%d
 [M:hal C:error F: L: ]: [I2C_SLAVE%d][timer callback]:NO timer callback
 [M:hal C:error F: L: ]: [I2C_SLAVE%d][isr]:invalid port=%d
 [M:hal C:error F: L: ]: [I2C_SLAVE%d]:tx dma timeout!
 [M:hal C:error F: L: ]: [I2C_SLAVE%d]:rx dma timeout!
 [M:hal C:error F: L: ]: [I2C_SLAVE%d][isr]:busy
 [M:hal C:error F: L: ]: [I2C_SLAVE%d][isr]:error status=%d
 [M:hal C:error F: L: ]: [I2C_SLAVE%d][isr]:NO isr callback
 [M:hal C:error F: L: ]: [I2C_SLAVE%d][send_polling]:event=%d
 [M:hal C:error F: L: ]: [I2C_SLAVE%d][receive_polling]:event=%d
 [M:hal C:error F: L: ]: [I2C_SLAVE%d][send_dma]:timer busy
 [M:hal C:error F: L: ]: [I2C_SLAVE%d][send_dma]:pdma init error %d
 [M:hal C:error F: L: ]: [I2C_SLAVE%d][send_dma]:pdma configure error %d
 [M:hal C:error F: L: ]: [I2C_SLAVE%d][send_dma]:pdma register callback error %d
 [M:hal C:error F: L: ]: [I2C_SLAVE%d][send_dma]:pdma start error %d
 [M:hal C:error F: L: ]: [I2C_SLAVE%d][receive_dma]:timer busy
 [M:hal C:error F: L: ]: [I2C_SLAVE%d][receive_dma]:pdma init error %d
 [M:hal C:error F: L: ]: [I2C_SLAVE%d][receive_dma]:pdma configure error %d
 [M:hal C:error F: L: ]: [I2C_SLAVE%d][receive_dma]:pdma register callback error %d
 [M:hal C:error F: L: ]: [I2C_SLAVE%d][receive_dma]:pdma start error %d
 [M:hal C:error F: L: ]: [I2C_SLAVE%d]:timeout!
 [M:hal C:warning F: L: ]: [I2C_SLAVE%d]:residue data %d!
 [M:hal C:error F: L: ]: [I2C_SLAVE%d]:timeout!
 [M:hal C:warning F: L: ]: [I2C_SLAVE%d]:LD!
 [M:hal C:warning F: L: ]: [I2C_SLAVE%d]:TM! r_l(%d)
 [M:hal C:warning F: L: ]: [I2C_SLAVE%d]:NE! r_l(%d)
 [M:hal C:error F: L: ]: memeory layout FS base changed to: 0x%x 
 [M:hal C:error F: L: ]: FAT base setting is different in LD and driver FS_RESERVED_BASE= 0x%x, NOR_FLASH_BASE_ADDRESS = 0x%x
 [M:hal C:info F: L: ]: FAT base address = 0x%x
 [M:hal C:warning F: L: ]: FAT length reduce FS_RESERVED_LENGTH = 0x%x, NOR_ALLOCATED_FAT_SPACE = 0x%x 
 [M:hal C:warning F: L: ]: FAT length increase FS_RESERVED_LENGTH = 0x%x, NOR_ALLOCATED_FAT_SPACE = 0x%x 
 [M:hal C:info F: L: ]: need to adjust FAT size 0x%x
 [M:hal C:info F: L: ]: FAT length is 0x%x
 [M:hal C:error F: L: ]: [SPIS%d][init]:busy.
 [M:hal C:error F: L: ]: [SPIS%d] Bus clock enable failed!
 [M:hal C:error F: L: ]: [SPIS%d] Clock enable failed!
 [M:hal C:error F: L: ]: [SPIS%d][send]:size error. expected:%d request:%d
 [M:hal C:error F: L: ]: [SPIS%d][receive]:size error.
 [M:hal C:error F: L: ]: [SPIS%d][init]:default clock source selection error.
 [M:hal C:error F: L: ]: 
 Slave reaceived data length(0x%x) is larger than size!
 [M:hal C:warning F: L: ]: [SPIS] read S2, ERROR and cleared!! slave_status(0x%x), goto CWR
 [M:hal C:warning F: L: ]: [SPIS] read S2 slave_status(0x%x), RD_ERR or WR_ERR
 [M:hal C:error F: L: ]: [SPIS] read S2 slave_status(0x%x)(0x%x), timeout
 [M:hal C:warning F: L: ]: [SPIS] read S2, ERROR and cleared!! slave_status(0x%x), goto CWR
 [M:hal C:warning F: L: ]: [SPIS] read S2 slave_status(0x%x), RD_ERR or WR_ERR
 [M:hal C:error F: L: ]: [SPIS] read S0 slave_status(0x%x)(0x%x), timeout
 [M:hal C:error F: L: ]: [SPIS] read S1 slave_status(0x%x)(0x%x), timeout
 [M:hal C:error F: L: ]: [SPIS] read S2 slave_status(0x%x)(0x%x), timeout
 [M:hal C:warning F: L: ]: [SPIS] write S2, ERROR and cleared!! slave_status(0x%x), goto CRD
 [M:hal C:warning F: L: ]: [SPIS] write S2 slave_status(0x%x), RD_ERR or WR_ERR
 [M:hal C:error F: L: ]: [SPIS] write S0 slave_status(0x%x)(0x%x), timeout
 [M:hal C:error F: L: ]: [SPIS] write S1 slave_status(0x%x)(0x%x), timeout
 [M:hal C:error F: L: ]: [SPIS] write S2 slave_status(0x%x)(0x%x), timeout
 [M:hal C:info F: L: ]: rtc initialize started...................!!!! [M:hal C:warning F: L: ]: -> rtc 1st power on
 [M:hal C:warning F: L: ]: -> system is back from rtc mode [M:hal C:warning F: L: ]: -> rtc back from power exception [M:hal C:error F: L: ]: hal_eint_init fail: %d
 [M:hal C:error F: L: ]: hal_eint_unmask fail: %d
 [M:hal C:warning F: L: ]: -> rtc use ext xosc-32k [M:hal C:warning F: L: ]: -> rtc use iner dcxo-32k [M:hal C:info F: L: ]: measure 32k:F32k(%d),EOSC(%d),XOSC(%d), DCXO(%d) [M:hal C:info F: L: ]: rtc initialized done...................!!!! [M:hal C:warning F: L: ]: -> system will enter lowpower(RTC Mode) [M:hal C:warning F: L: ]: -> rtc sram cell%d set to sleep mode [M:hal C:warning F: L: ]: -> rtc sram cell%d already in %d (1:slp 2:pd) mode [M:hal C:error F: L: ]: -> enter rtc mode failed, pmu power off system fail!! [M:hal C:warning F: L: ]: -> rtc sram cell%d restore to normal mode [M:hal C:warning F: L: ]: -> system will enter lowpower(Retention Mode) [M:hal C:warning F: L: ]: -> rtc sram cell%d set to sleep mode [M:hal C:warning F: L: ]: -> rtc sram cell%d already in %d (1:slp 2:pd) mode [M:hal C:error F: L: ]: -> enter rtc retention mode failed, pmu power off system fail!! [M:hal C:info F: L: ]: [hal][rtc] hal_rtc_set_time: not location [M:hal C:info F: L: ]: [hal][rtc] hal_rtc_set_time(%d-%d-%d %d:%d:%d) [M:hal C:info F: L: ]: [hal][rtc] hal_rtc_get_time(%d-%d-%d %d:%d:%d) [M:hal C:info F: L: ]: [hal][rtc] hal_rtc_set_alarm(%d-%d-%d %d:%d:%d) [M:hal C:info F: L: ]: rtc user alarm triggered!!
 [M:hal C:info F: L: ]: rtc tick triggered!!
 [M:hal C:info F: L: ]: rtc gps alarm triggered!!
 [M:hal C:info F: L: ]: rtc eint triggered!!
 [M:hal C:info F: L: ]: [hal][rtc] hal_rtc_gps_set_time: record timestamp %d, gpt %d!! [M:hal C:error F: L: ]: invalid argument
 [M:hal C:info F: L: ]: [hal][rtc] hal_rtc_gps_set_time(%d-%d-%d %d:%d:%d) [M:hal C:error F: L: ]: rtc_wait_busy timeout, RTC_BBPU = %x!
 [M:hal C:error F: L: ]: rtc_lpd_init fail : RTC_LPD_CON = %x!
 [M:hal C:warning F: L: ]: EOSC->GPIO_%d, XOSC->GPIO_%d [M:hal C:error F: L: ]: [hal][rtc][inner] set osc32con%d fail
 [M:hal C:warning F: L: ]: -> rtc power by alarm!
 [M:hal C:warning F: L: ]: -> rtc power by tick!
 [M:hal C:warning F: L: ]: -> rtc power by eint!
 [M:hal C:warning F: L: ]: -> rtc power by gps alarm!
 [M:hal C:error F: L: ]: rtc_set_power_key fail : rtc_wrtgr = %x!
 [M:hal C:error F: L: ]: -> high_frequency <= 32768, frequency = %u, xosccali = %d
 [M:hal C:error F: L: ]: -> low_frequency >= 32768, frequency = %u, xosccali = %d
 [M:hal C:warning F: L: ]: -> xosccali = %d
 [M:hal C:warning F: L: ]: -> eosc cali frequency = %u, cali_val = %d
 [M:hal C:warning F: L: ]: -> rtc cali value %d = (32768 - %d) [M:hal C:error F: L: ]: rtc cali is too big
 [M:hal C:error F: L: ]: rtc cali is too small
 [M:hal C:info F: L: ]: ======= %d  Dump RTC Register ====== [M:hal C:info F: L: ]: POWERKEY1(%x),  POWERKEY2(%x),  LPD_CON(%x),  WRTGR(%x),  RESETCON(%x) [M:hal C:info F: L: ]: OSC32CON0(%x),  CON32CON1(%x),  CON32CON2(%x),  BBPU(%x),  CALI(%x) [M:hal C:info F: L: ]: EINT(%x),  CAP(%x),  BOND(%x),  SPARE(%x),  DIFF(%x) [M:hal C:info F: L: ]: SPARE0(%x), SPARE1(%x), SPARE2%x), SPARE3(%x), SPARE4(%x), SPARE5(%x) [M:hal C:info F: L: ]: GPIO0(%x), GPIO1(%x), GPIO2(%x), CII_EN(%x),SRAM_CON0(%x),SRAM_CON1(%x) [M:hal C:info F: L: ]: AL_MASK(%x), AL0(%x),    AL1(%x),    AL2(%x),    AL3(%x) [M:hal C:info F: L: ]: TC0(%x), TC1(%x), TC2(%x), TC3(%x),  EINT(%x) [M:hal C:info F: L: ]: ======= Dump RTC Register End ====== [M:hal C:error F: L: ]: rtc wait latch timeout!
 [M:hal C:error F: L: ]: rtc wait msmod done timeout!
 [M:hal C:error F: L: ]: ASSERT
 [M:hal C:error F: L: ]: ASSERT
 [M:hal C:info F: L: ]: usb_hw_epfifowrite Error: pSrc is NULL!!
 [M:hal C:info F: L: ]: ASSERT
 [M:hal C:info F: L: ]: usb_hisr: EP0 Int disabled [M:hal C:error F: L: ]: ASSERT, g_UsbDrvInfo->reset_hdlr == NULL
 [M:hal C:error F: L: ]: g_UsbDrvInfo->resume_hdlr == NULL 
 [M:hal C:error F: L: ]: usb_hisr : ep0_hdlr = NULL Fail [M:hal C:error F: L: ]: usb_hisr: skip ep0 handler [M:hal C:info F: L: ]: USB_INTRUSB_DISCON
 [M:hal C:error F: L: ]: ASSERT
 [M:hal C:error F: L: ]: ASSERT
 [M:hal C:info F: L: ]: ASSERT
 [M:hal C:info F: L: ]: ASSERT
 [M:hal C:info F: L: ]: ASSERT
 [M:hal C:info F: L: ]: ASSERT
 [M:hal C:info F: L: ]: ASSERT
 [M:hal C:info F: L: ]: hal_eint_init fail: %d
 [M:hal C:info F: L: ]: hal_eint_register_callback fail: %d
 [M:hal C:info F: L: ]: dma fail
 [M:hal C:info F: L: ]: compare data fail
 [M:hal C:info F: L: ]: hal_usb_dcm_enable: 0x%x
 [M:hal C:error F: L: ]: ASSERT
 [M:hal C:info F: L: ]: usb DP pull high
 [M:hal C:error F: L: ]: ASSERT
 [M:hal C:error F: L: ]: ASSERT
 [M:hal C:error F: L: ]: ASSERT
 [M:hal C:error F: L: ]: ASSERT
 [M:hal C:error F: L: ]: hal_usb_update_endpoint_0_state skip clear TRANSACTION_END [M:hal C:error F: L: ]: hal_usb_update_endpoint_0_state reg_state:%X csr0:0x%X stall [M:hal C:error F: L: ]: ASSERT
 [M:hal C:error F: L: ]: ASSERT
 [M:hal C:error F: L: ]: ASSERT
 [M:hal C:error F: L: ]: ASSERT
 [M:hal C:error F: L: ]: ASSERT
 [M:hal C:error F: L: ]: ASSERT
 [M:hal C:error F: L: ]: ASSERT
 [M:hal C:error F: L: ]: ASSERT
 [M:hal C:error F: L: ]: ASSERT
 [M:hal C:error F: L: ]: ASSERT
 [M:hal C:error F: L: ]: ASSERT
 [M:hal C:error F: L: ]: [PMU] vrf_voltage Error input [M:hal C:error F: L: ]: [PMU] vsram_voltage Error input [M:hal C:info F: L: ]: E pmu_eint_handler [M:hal C:info F: L: ]: interrupt index = %d [M:hal C:error F: L: ]: [PMU] vusb Error input [M:hal C:info F: L: ]: [PMU]on off reason:%x [M:hal C:info F: L: ]: Set STRUP_LATCH_CON register failed [M:hal C:error F: L: ]: [PMU] vsram_voltage Error input [M:hal C:error F: L: ]: [PMU] vrf_voltage Error input [M:hal C:error F: L: ]: [PMU] vrtc_voltage Error input [M:hal C:info F: L: ]: [pinmux] pin=%d mode=%d
 [M:hal C:info F: L: ]: host read: offset:0x%x real_addr:0x%x value:0x%x attr:0x%x length:%u
 [M:hal C:error F: L: ]:   ---attribute error----
 [M:hal C:error F: L: ]: hal_spi_slave_send status err:%d
 [M:hal C:info F: L: ]: host write: offset:0x%X real_addr:0x%X value:%x attr:0x%x length:%u
 [M:hal C:error F: L: ]:   ---attribute error----
 [M:hal C:error F: L: ]: hal_spi_slave_receive err:%d
 [M:hal C:info F: L: ]:   ---Receive POWERON command----
 [M:hal C:info F: L: ]:   ---Receive POWEROFF command----
 [M:hal C:info F: L: ]: HAL_SPI_SLAVE_EVENT_CRD_FINISH request_address:0x%x,request_length:%u
 [M:hal C:error F: L: ]: virtual register object not found
 [M:hal C:error F: L: ]: bsp slave chip Config Read error len, virtual register index:%d len_limiter:%d,but request_length:%d
 [M:hal C:error F: L: ]: bsp slave chip receive error
 [M:hal C:info F: L: ]:   ---Receive CRD_FINISH command----
 [M:hal C:info F: L: ]: HAL_SPI_SLAVE_EVENT_CWR_FINISH request_address:0x%x,request_length:%u
 [M:hal C:error F: L: ]: virtual register object not found
 [M:hal C:error F: L: ]: bsp slave chip Config Write error len, virtual register index:%d len_limiter:%d,but request_length:%d
 [M:hal C:error F: L: ]: bsp slave chip receive error
 [M:hal C:info F: L: ]:   ---Receive CWR_FINISH command----
 [M:hal C:info F: L: ]:   ---Receive RD_FINISH command----
 [M:hal C:info F: L: ]: send done result:%d
 [M:hal C:info F: L: ]:   ---Receive WR_FINISH command----
 [M:hal C:info F: L: ]: receive done result:%d
 [M:hal C:error F: L: ]:   ---detect RD_ERR----
 [M:hal C:error F: L: ]:   ---detect WR_ERR----
 [M:hal C:error F: L: ]:   ---detect TMOUT_ERR----
 [M:hal C:error F: L: ]: interrupt_status:0x%04x fsm_status:0x%x [M:hal C:error F: L: ]:   HAL_SPI_SLAVE_FSM_ERROR_PWROFF_AFTER_CR, fsm is poweroff
 [M:hal C:error F: L: ]:   HAL_SPI_SLAVE_FSM_ERROR_PWROFF_AFTER_CW, fsm is poweroff
 [M:hal C:error F: L: ]:   HAL_SPI_SLAVE_FSM_ERROR_CONTINOUS_CR, fsm is CR
 [M:hal C:error F: L: ]:   HAL_SPI_SLAVE_FSM_ERROR_CR_AFTER_CW, fsm is CR
 [M:hal C:error F: L: ]:   HAL_SPI_SLAVE_FSM_ERROR_CONTINOUS_CW, fsm is CW
 [M:hal C:error F: L: ]:   HAL_SPI_SLAVE_FSM_ERROR_CW_AFTER_CR, fsm is CW
 [M:hal C:error F: L: ]:   HAL_SPI_SLAVE_FSM_ERROR_WRITE_AFTER_CR, fsm is poweron
 [M:hal C:error F: L: ]:   HAL_SPI_SLAVE_FSM_ERROR_READ_AFTER_CW, fsm is poweron
 [M:hal C:error F: L: ]:   HAL_SPI_SLAVE_FSM_INVALID_OPERATION, fsm is poweron
 [M:hal C:info F: L: ]: SPI Slave port init
 [M:hal C:error F: L: ]: hal_spi_slave_set_early_miso failed
 [M:hal C:warning F: L: ]: HAL_SPI_SLV0_MISO_PIN pin is configured with %d(0:None,1:EPT,2:User), GPIO%d ->AFUNC%d
 [M:MUX_PORT C:info F: L: ]: there is two receive buffer !!!##port:%d hw_rx_read_ptr:%d,total:%d
 [M:MUX_PORT C:error F: L: ]: Mux error: port dismatch with handle, port:%d handle:0x%x consume_len:%d new_pkg_len:%d rx_receive:%d [M:MUX_PORT C:error F: L: ]: Mux error: The packet is larger than rx buffer, port:%d handle:0x%x consume_len:%d new_pkg_len:%d rx_receive:%d [M:MUX_PORT C:info F: L: ]: Mux warning: The packet not receive done, port:%d handle:0x%x consume_len:%d new_pkg_len:%d rx_receive:%d [M:MUX_PORT C:info F: L: ]: CM4 break while loop %d,%d,%d
 [M:MUX_PORT C:info F: L: ]: CM4 continue while loop %d,%d,%d
 [M:MUX_PORT C:error F: L: ]: Mux error: port push rx buffer fail, port:%d  new_pkg_len:%d [M:MUX_PORT C:info F: L: ]: MUX_CMD_CLEAN only support UART and USB now!
 [M:MUX_PORT C:error F: L: ]: mux sw r w point check error!!! flag:0x%x [M:MUX_PORT C:error F: L: ]: p->rx_buff_start:0x%x  [M:MUX_PORT C:error F: L: ]: p->rx_buff_len:0x%x  [M:MUX_PORT C:error F: L: ]: p->rx_buff_end:0x%x  [M:MUX_PORT C:error F: L: ]: p->rx_buff_read_point:0x%x  [M:MUX_PORT C:error F: L: ]: p->rx_buff_write_point:0x%x  [M:MUX_PORT C:error F: L: ]: p->rx_buff_available_len:0x%x  [M:MUX_PORT C:error F: L: ]: p->rx_receiving_write_point:0x%x  [M:MUX_PORT C:error F: L: ]: p->tx_buff_start:0x%x  [M:MUX_PORT C:error F: L: ]: p->tx_buff_len:0x%x  [M:MUX_PORT C:error F: L: ]: p->tx_buff_end:0x%x  [M:MUX_PORT C:error F: L: ]: p->tx_buff_read_point:0x%x  [M:MUX_PORT C:error F: L: ]: p->tx_buff_write_point:0x%x  [M:MUX_PORT C:error F: L: ]: p->tx_buff_available_len:0x%x  [M:MUX_PORT C:error F: L: ]: p->tx_sending_read_point:0x%x  [M:MUX_PORT C:info F: L: ]: port_mux_uart_clear_fifo_failed!
 [M:MUX_PORT C:info F: L: ]: port_mux_uart_control()--> CONNECT ! record_size = %d
 [M:MUX_PORT C:info F: L: ]: port_mux_uart_control()--> DISCONNECT !
 [M:MUX_PORT C:info F: L: ]: port_mux_uart mcr_status %08x
 [M:MUX_PORT C:info F: L: ]: port_mux_uart_control()--> CLEAN !
 [M:MUX_PORT C:info F: L: ]: MUX UART error control cmd:0x%x
 [M:MUX_USB C:error F: L: ]: bt rx port control read data error status[%d] handle[%d],rx_buff_len = %d [M:MUX_USB C:error F: L: ]: bt rx buffer not enough to save, len : %d [M:common C:info F: L: ]: ERROR!!! MUX SPI Slave can not support port_mux_spi_slave_control!!!
 [M:common C:info F: L: ]: SPI slave receive %d data
 [M:common C:info F: L: ]: p->rx_buff_start:0x%x  
 [M:common C:info F: L: ]: p->rx_buff_len:0x%x  
 [M:common C:info F: L: ]: p->rx_buff_end:0x%x  
 [M:common C:info F: L: ]: p->rx_buff_read_point:0x%x  
 [M:common C:info F: L: ]: p->rx_buff_write_point:0x%x  
 [M:common C:info F: L: ]: p->rx_buff_available_len:0x%x  
 [M:common C:info F: L: ]: p->rx_receiving_write_point:0x%x  
 [M:common C:info F: L: ]: bsp_slave_chip_deinit fail!! error_code=%d
 [M:common C:info F: L: ]: this is function of spi_slave_virtual_register_rx_free_size_callback()
 [M:common C:info F: L: ]: Master read Slave Virtual REG of Rx_Free_Len reg, value is :%d
 [M:common C:info F: L: ]: this is function of spi_slave_virtual_register_tx_buffer_callback()
 [M:hal C:error F: L: ]: ERROR!!! MUX I2C Slave can not support port_mux_i2c_slave_control!!!
 [M:hal C:info F: L: ]: I2C slave receive %d data
 [M:hal C:error F: L: ]: i2c_slave_virtual_register_tx_buffer_callback Error!!! direction:%d 
 [M:SYSLOG C:info F: L: ]: Logging: query syslog port from nvdm, status:%d quantity:%d [M:SYSLOG C:info F: L: ]: Logging: syslog port must <= 1 [M:SYSLOG C:info F: L: ]: Logging: query syslog port from nvdm ok, port=%d [M:SYSLOG C:info F: L: ]: Logging: one-wire is invalid, use nvdm port=%d [M:SYSLOG C:info F: L: ]: Logging: query syslog port from nvdm fail, use default port=%d [M:SYSLOG C:info F: L: ]: Logging: begin mux_init syslog port:%d tx_buf_size:%d rx_buf_size:%d [M:SYSLOG C:error F: L: ]: filter_cpu_config_save fail [M:SYSLOG C:error F: L: ]: filter_module_config_save fail [M:common C:error F: L: ]: pc tool command data length too long [M:SYSLOG C:info F: L: ]: Logging:  PC logging tool command id = 0x%04x [M:common C:info F: L: ]: swla cannot enable because of the lack of working buffer[0x%x, 0x%x]
 [M:common C:info F: L: ]: [parameter error]invalid xAction:%d.
 [M:common C:error F: L: ]: [parameter error]invalid xOperation:%d.
 [M:common C:info F: L: ]: receive swla xOperation:%d.
 [M:common C:error F: L: ]: SWLA working memory is not enough to start SWLA.
 [M:hal C:info F: L: ]: [Boot Reason Flag]:0x%x,0x%x
 [M:nvdm C:error F: L: ]: hal_flash_read: address = 0x%x, buffer = 0x%x, length = %d, status = %d [M:nvdm C:error F: L: ]: hal_flash_write: address = 0x%x, buffer = 0x%x, length = %d, status = %d [M:nvdm C:error F: L: ]: hal_flash_erase: address = 0x%08x, status = %d [M:nvdm C:error F: L: ]: nvdm_port_mutex_creat error
 [M:nvdm C:error F: L: ]: nvdm_port_mutex_take error
 [M:nvdm C:error F: L: ]: nvdm_port_mutex_give error
 [M:nvdm C:error F: L: ]: nvdm_port_protect_mutex_create error
 [M:nvdm C:error F: L: ]: nvdm_port_protect_mutex_take error
 [M:nvdm C:error F: L: ]: nvdm_port_protect_mutex_give error
 [M:nvdm C:warning F: L: ]: Canceled %d non-blocking write data [M:nvdm C:error F: L: ]: invalid data item at (%d, %d) with %d bytes( %d, %d ) [M:nvdm C:warning F: L: ]: garbage collection finished, about %d ms used [M:nvdm C:warning F: L: ]: nvdm init finished [M:nvdm C:error F: L: ]: alloc peb_info fail [M:nvdm C:error F: L: ]: Count of PEB for NVDM region must greater than or equal to 2 [M:nvdm C:info F: L: ]: calculate total valid data size [M:nvdm C:error F: L: ]: reclaim_idx=%d, transfered_peb=%d, transfering_peb=%d [M:nvdm C:info F: L: ]: found a peb in transfered status [M:nvdm C:info F: L: ]: found a peb in transfering status [M:nvdm C:info F: L: ]: scan all non-reserved pebs including reclaiming pebs and transfering peb [M:nvdm C:info F: L: ]: update erase count for unknown pebs [M:nvdm C:info F: L: ]: reclaiming_peb[%d] = %d [M:nvdm C:info F: L: ]: transfered_peb = %d [M:nvdm C:info F: L: ]: transfering_peb = %d [M:nvdm C:info F: L: ]: after verify peb header [M:nvdm C:error F: L: ]: peb_header validate fail, pnum=%d [M:nvdm C:error F: L: ]: peb_header validate fail, pnum=%d [M:nvdm C:error F: L: ]: find more than one transfered peb, first=%d, second=%d [M:nvdm C:error F: L: ]: find more than one transfering peb, first=%d, second=%d [M:nvdm C:error F: L: ]: peb_header validate fail, pnum=%d [M:nvdm C:info F: L: ]: before verify peb header [M:nvdm C:info F: L: ]: scan and verify peb headers [M:nvdm C:error F: L: ]: reclaiming_peb alloc fail [M:nvdm C:error F: L: ]: detect g_valid_data_size abnormal [M:nvdm C:info F: L: ]: space_is_enough: g_valid_data_size = %d, new add size = %d [M:nvdm C:warning F: L: ]: config information: [0]: %d  [1]: %d  [2]: %d  [3]: %d  [4]: %d  [5]: %d  [6]: %d [M:nvdm C:info F: L: ]: find_free_peb: target_peb = %d, reserved_peb = %d, reserved_peb_cnt = %d [M:nvdm C:info F: L: ]: %d [M:nvdm C:info F: L: ]: reclaim peb_list(sort):  [M:nvdm C:info F: L: ]: %d [M:nvdm C:info F: L: ]: reclaim peb_list(no-sort):  [M:nvdm C:info F: L: ]: reclaim blocks select by valid size = %d [M:nvdm C:info F: L: ]: %d [M:nvdm C:info F: L: ]: reclaim peb_list(sort):  [M:nvdm C:info F: L: ]: %d [M:nvdm C:info F: L: ]: reclaim peb_list(no-sort):  [M:nvdm C:info F: L: ]: reclaim blocks select by erase count = %d [M:nvdm C:info F: L: ]: mean_erase_count = %d [M:nvdm C:info F: L: ]: non_reserved_pebs = %d [M:nvdm C:error F: L: ]: GC buffer alloc fail [M:nvdm C:warning F: L: ]: start garbage collection!!! [M:nvdm C:info F: L: ]: merge peb: %d, data_size: %d [M:nvdm C:info F: L: ]: found a target peb(%d) for reclaiming [M:nvdm C:error F: L: ]: target_peb=%d [M:nvdm C:info F: L: ]: found no valid data in reclaiming pebs when relocate_pebs() [M:nvdm C:error F: L: ]: pnum=%d [M:nvdm C:error F: L: ]: pnum=%d [M:nvdm C:error F: L: ]: pnum=%d [M:nvdm C:error F: L: ]: pnum=%d [M:nvdm C:error F: L: ]: pnum=%d [M:nvdm C:error F: L: ]: len=%d [M:nvdm C:error F: L: ]: offset=0x%x [M:nvdm C:error F: L: ]: pnum=%d [M:nvdm C:error F: L: ]: magic=0x%x, erase_count=0x%x, status=0x%x, peb_reserved=0x%x [M:nvdm C:error F: L: ]: len=%d [M:nvdm C:error F: L: ]: offset=0x%x [M:nvdm C:error F: L: ]: pnum=%d [M:nvdm C:error F: L: ]: pnum=%d [M:nvdm C:info F: L: ]: version: %x [M:nvdm C:info F: L: ]: peb_reserved: %x [M:nvdm C:info F: L: ]: status: %x [M:nvdm C:info F: L: ]: erase_count: %x [M:nvdm C:info F: L: ]: magic: %x [M:nvdm C:info F: L: ]: peb header(%d) info show below: [M:nvdm C:warning F: L: ]: g_valid_data_size = %d [M:nvdm C:warning F: L: ]: %3d     %4d    %4d     %4d       %8d              %d [M:nvdm C:warning F: L: ]: peb    valid    free    dirty    erase_count    is_reserved [M:nvdm C:warning F: L: ]: region info show below: [M:nvdm C:error F: L: ]: pnum=%d [M:nvdm C:error F: L: ]: addr=0x%x, pnum=%d, offset=0x%x, len=%d [M:nvdm C:error F: L: ]: pnum=%d, offset=0x%x, len=%d [M:nvdm C:error F: L: ]: Error item status(0x%x) at src_pnum=%d, pos=0x%x [M:nvdm C:error F: L: ]: old_src_pnum=%d, old_pos=0x%x, new_src_pnum=%d, new_pos=0x%x, item_size=%d [M:nvdm C:error F: L: ]: alloc data_item_headers fail [M:nvdm C:error F: L: ]: Max size of data item must less than or equal to 2048 bytes [M:nvdm C:error F: L: ]: abnormal_data_item = %d at %d block with offset 0x%x [M:nvdm C:info F: L: ]: copy1(pnum=%d, offset=0x%x), copy2(pnum=%d, offset=0x%x) [M:nvdm C:info F: L: ]: detect two valid copy of data item [M:nvdm C:error F: L: ]: too many data items in nvdm region [M:nvdm C:warning F: L: ]: detect checksum error [M:nvdm C:error F: L: ]: Detect index of data item with out of range, max = %d, curr = %d [M:nvdm C:error F: L: ]: pnum=%d, offset=0x%x [M:nvdm C:info F: L: ]: scanning pnum(%d) to analysis data item info [M:nvdm C:info F: L: ]: nvdm_query_data_item_length: begin to query [M:nvdm C:info F: L: ]: nvdm_query_next_data_item_name: enter [M:nvdm C:info F: L: ]: nvdm_query_next_group_name: enter [M:nvdm C:info F: L: ]: nvdm_query_end: enter [M:nvdm C:info F: L: ]: nvdm_query_begin: enter [M:nvdm C:info F: L: ]: nvdm_delete_all: enter [M:nvdm C:info F: L: ]: nvdm_delete_group: enter [M:nvdm C:info F: L: ]: nvdm_delete_data_item: enter [M:nvdm C:warning F: L: ]: Can't send queue!! [M:nvdm C:error F: L: ]: Can't alloc memory!! [M:nvdm C:info F: L: ]: nvdm_write_data_item_non_blocking: begin to write [M:nvdm C:info F: L: ]: old data item overwrite [M:nvdm C:info F: L: ]: new data item append [M:nvdm C:warning F: L: ]: too many data items in nvdm region [M:nvdm C:info F: L: ]: peb free space is not enough [M:nvdm C:info F: L: ]: find_data_item_by_hashname return %d [M:nvdm C:info F: L: ]: nvdm_write_data_item: begin to write [M:nvdm C:info F: L: ]: nvdm_read_data_item: begin to read [M:nvdm C:info F: L: ]: hashname = 0x%x [M:nvdm C:info F: L: ]: hash_name: 0x%x [M:nvdm C:info F: L: ]: type: %d [M:nvdm C:info F: L: ]: index: %d [M:nvdm C:info F: L: ]: value_size: %d [M:nvdm C:info F: L: ]: data_item_name_size: %d [M:nvdm C:info F: L: ]: group_name_size: %d [M:nvdm C:info F: L: ]: sequence_number: %d [M:nvdm C:info F: L: ]: offset: 0x%x [M:nvdm C:info F: L: ]: pnum: %d [M:nvdm C:info F: L: ]: status: 0x%x [M:nvdm C:info F: L: ]: data item header info show below: [M:USB_MAIN C:info F: L: ]: QueueReceive ID  = %d [M:USB_MAIN C:info F: L: ]: USB_ACM_MSG [M:USB_MAIN C:info F: L: ]: task max-usage: %d [M:USB_MAIN C:info F: L: ]: usb task not initlize [M:USB_MAIN C:info F: L: ]: Send Queue in Task !! id = %d  [M:USB_MAIN C:info F: L: ]: Send Queue in ISR !! id = %d  [M:USB_MAIN C:error F: L: ]: Send Queue fail!! Queue size = %d  [M:USB_MAIN C:info F: L: ]: usb task have inited, return ! [M:USB_MAIN C:error F: L: ]: usb_queue_handle create Fail! [M:USB_MAIN C:error F: L: ]: usb_task_main : task create Fail! [M:USB_MAIN C:info F: L: ]: usb_common_get_bc12 ret[%d] type[%d] [M:USB_MAIN C:info F: L: ]: usb app init type [%d] [M:USB_MAIN C:error F: L: ]: ap_usb_init : Fail [M:USB_MAIN C:info F: L: ]: usb_cable_init_mt2822 [M:USB_MAIN C:info F: L: ]: usb usb_cable_detect [M:USB_MAIN C:info F: L: ]: USB in [M:USB_MAIN C:info F: L: ]: usb 3335 USBCOM init s, hisr cnt %d [M:USB_MAIN C:info F: L: ]: usb 3335 USBCOM init e, hisr cnt %d [M:USB_MAIN C:info F: L: ]: USB1 CONNECTION callback [M:USB_MAIN C:info F: L: ]: USB2 CONNECTION callback [M:USB_MAIN C:info F: L: ]: usb_device_test_case case [%d] [M:USB_MAIN C:info F: L: ]: usb_device_test_case slt pass [M:USB_MAIN C:info F: L: ]: usb_device_test_case slt fail [M:USB_MAIN C:info F: L: ]: Boot-USB_in [M:USB_MAIN C:info F: L: ]: ap_usb_init(USB_CDC_ACM) [M:USB_MAIN C:info F: L: ]: USBCOM init, hisr cnt %d [M:USB_MAIN C:info F: L: ]: usb usb_entry_force_enumeration step1 [%d]
 [M:USB_MAIN C:info F: L: ]: usb usb_entry_force_enumeration step2 [%d]

 [M:USB_RESOURCE C:error F: L: ]: USB_Check_Ep_Number : Fail [M:USB_RESOURCE C:error F: L: ]: USB_Check_Descriptor : Fail [M:USB_RESOURCE C:error F: L: ]: USB_Register_CreateFunc : Fail [M:USB_RESOURCE C:error F: L: ]: USB_Register_Device_Code : Fail [M:USB_RESOURCE C:error F: L: ]: USB_Software_Speed_Init : ifcreate_number = 0 Fail [M:USB_RESOURCE C:error F: L: ]: USB_Software_Speed_Init: usb_ifcreate_tbl is NULL Fail [M:USB_RESOURCE C:error F: L: ]: USB_Software_Speed_Init : wTotalLength %d is not match Conf_index:%d Fail [M:USB_RESOURCE C:error F: L: ]: USB_Software_Create : check registered function Fail [M:USB_RESOURCE C:error F: L: ]: USB_Software_Create : create function Fail [M:USB_RESOURCE C:error F: L: ]: USB_Software_Create : cfgdscr.wTotalLength=%d bytes > sizeof(USB_TEST_BUFFER)=%d bytes Fail [M:USB_RESOURCE C:error F: L: ]: USB_Software_Init Fail [M:USB_RESOURCE C:error F: L: ]: USB_Software_Enable Fail [M:USB C:info F: L: ]: USB_Get_Memory : size = %X [M:USB C:error F: L: ]: USB_Free_Memory : free null Fail [M:USB C:info F: L: ]: USB_Free_Memory : free addr 0x%X [M:USB C:info F: L: ]: USB_Init_Acm_Status [M:USB C:error F: L: ]: USB_EP0_Command_Hdlr bError:%d [M:USB C:info F: L: ]: USB_Stdcmd: bRequest = 0x%X wIndex = 0x%X wValue = 0x%X [M:USB C:info F: L: ]: USB_Cmd_GetDescriptor : wValue = 0x%X [M:USB C:info F: L: ]: USB_Cmd_SetConfiguration [M:USB C:info F: L: ]: bRequest = 0x%X is out of case [M:USB C:error F: L: ]: USB_Endpoint0_Hdlr b_sent_stall [M:USB C:error F: L: ]: USB_Endpoint0_Hdlr b_transaction_end [M:USB C:error F: L: ]: USB_Endpoint0_Hdlr RX status polling next set up cmd fail !! IntrTX:0x%X nCount:%d [M:USB_CASE C:info F: L: ]: usb_case_register_atci_callback [M:USB_CASE C:info F: L: ]: usb_case_atci_call[%d] [M:USB_CASE C:info F: L: ]: usb_case_register_race_callback [M:USB_CASE C:info F: L: ]: usb_case_race_call[%d] [M:USB_CASE C:info F: L: ]: usb_case_register_logs_callback [M:USB_CASE C:info F: L: ]: usb_case_logs_call[%d] [M:common C:error F: L: ]: USB2UART_Update_Transmit_Data, ASSERT
 [M:common C:error F: L: ]: ASSERT
 [M:common C:error F: L: ]: ASSERT, USB_Acm_BulkOut_Hdr
 [M:common C:error F: L: ]: USB2UART_Check_Config, txpipe=0x%x, init=%d, state=%d
 [M:common C:error F: L: ]: USB2UART_GetBytes, enumeration not ready [M:common C:info F: L: ]: ASSERT [M:common C:error F: L: ]: USB2UART_PutBytes, enumeration not ready, stop dma transfer then return error
 [M:hal C:info F: L: ]: reg. usb_callback port:%d
 [M:hal C:info F: L: ]: serial_port_usb open NOT ready
 [M:atci C:warning F: L: ]: ATCI hdl: atci_send_response: fail, atci does not ready!
 [M:atci C:warning F: L: ]: ATCI hdl: atci_send_response, atci response queue full(%d), drop this urc data!
 [M:atci C:warning F: L: ]: ATCI hdl: atci_send_response, atci response queue full(%d), drop this rsp data!
 [M:atci C:warning F: L: ]: ATCI hdl: atci_send_response, len(%d) flag(%x), too long, drop!
 [M:atci C:warning F: L: ]: ATCI hdl: atci_send_response, atci_mem_alloc fail 
 [M:atci C:warning F: L: ]: ATCI hdl: atci_send_response: fail, atci does not ready!
 [M:atci C:warning F: L: ]: ATCI hdl: atci_send_response, msg_num: %x
 [M:atci C:warning F: L: ]: ATCI hdl: atci_send_response, atci response queue full(%d), drop this urc data!
 [M:atci C:warning F: L: ]: ATCI hdl: atci_send_response, atci response queue full(%d), drop this rsp data!
 [M:atci C:warning F: L: ]: ATCI hdl: atci_send_response, tail_len = %x, len = %x, len_byte = %x
 [M:atci C:warning F: L: ]: ATCI hdl: atci_send_response, data_len = %x
 [M:atci C:warning F: L: ]: ATCI hdl: atci_send_response, atci_mem_alloc fail 
 [M:atci C:warning F: L: ]: ATCI hdl: atci_init_hdlr_tbl_hash_value, exclude atcmd_table success
 [M:atci C:error F: L: ]: ATCI hdl: atci handler register fail, hdlr table extend 
 [M:atci C:info F: L: ]: ATCI hdl: atci handler: register success 
 [M:atci C:error F: L: ]: ATCI hdl: atci handler: register fail 
 [M:atci C:warning F: L: ]: ATCI hdl: atci_input_command_handler:input buf is null
 [M:atci C:error F: L: ]: ATCI hdl: find command handler fail 
 [M:atci C:error F: L: ]: ATCI main: atci_mux_find_index_by_handle, not found %d
 [M:atci C:error F: L: ]: ATCI main: atci_mux_find_index_by_handle, unuse index %d
 [M:atci C:info F: L: ]: ATCI main: atci_mux_find_index_by_handle handle %d index %d
 [M:atci C:info F: L: ]: ATCI main: atci_mux_init init mono mux port %d status %d
 [M:atci C:info F: L: ]: ATCI main: atci_mux_init open mono mux port %d status %d
 [M:atci C:info F: L: ]: ATCI main: create atci sleep lock handler success %d 
 [M:atci C:info F: L: ]: ATCI main: create atci sleep lock timer fail
 [M:atci C:info F: L: ]: ATCI main: create atci sleep lock timer success
 [M:atci C:info F: L: ]: ATCI main: atci sleep lock
 [M:atci C:info F: L: ]: ATCI main: atci restart sleep lock timer
 [M:atci C:info F: L: ]: ATCI main: atci sleep unlock
 [M:atci C:info F: L: ]: ATCI main: atci_serial_port_data_callback 1, read event, data_len %d, g_data_len %d [M:atci C:info F: L: ]: ATCI main: handle wrong!
 [M:atci C:info F: L: ]: ATCI main: wakeup sleep event!
 [M:atci C:info F: L: ]: ATCI main: atci_local_init, enter flag:%d 
 [M:atci C:info F: L: ]: ATCI main: atci_local_init, success 
 [M:atci C:info F: L: ]: ATCI main: atci_init_int, enter
 [M:atci C:info F: L: ]: ATCI main: atci_init_int, success 
 [M:atci C:error F: L: ]: ATCI main: atci_init_int, fail 
 [M:atci C:info F: L: ]: ATCI main: atci_mux_usb_plug plug in
 [M:atci C:info F: L: ]: ATCI main: atci_mux_usb_plug plug out
 [M:atci C:info F: L: ]: ATCI main: atci_mux_usb_plug mux_close stat %d
 [M:atci C:info F: L: ]: ATCI main: atci_mux_usb_plug mux_deinit stat %d
 [M:atci C:info F: L: ]: ATCI main: atci_mux_share_reinit port %d, now share port %d [M:atci C:info F: L: ]: ATCI main: atci_mux_share_reinit mux_close stat %d [M:atci C:info F: L: ]: ATCI main: atci_mux_share_reinit mux_open stat %d [M:atci C:info F: L: ]: ATCI main: atci_mux_share_resume now share port %d [M:atci C:info F: L: ]: ATCI main: atci_mux_share_resume mux_close stat %d [M:atci C:info F: L: ]: ATCI main: atci_mux_share_resume open share mux port %d status %d
 [M:atci C:info F: L: ]: ATCI main: atci_mux_share_resume syslog port MUX_AIRAPP_0
 [M:atci C:info F: L: ]: ATCI main: atci_mux_share_resume query syslog port fail
 [M:atci C:info F: L: ]: ATCI main: atci_mux_init open share mux port %d status %d
 [M:atci C:info F: L: ]: ATCI main: atci_mux_init syslog port MUX_AIRAPP_0
 [M:atci C:info F: L: ]: ATCI main: atci_mux_init query syslog port fail
 [M:atci C:info F: L: ]: ATCI main: atci_mux_init register usb callback
 [M:atci C:info F: L: ]: ATCI main: atci_mux_init syslog port == CONFIG_ATCI_PORT
 [M:atci C:info F: L: ]: ATCI main: atci_mux_init mux_open success 
 [M:atci C:warning F: L: ]: ATCI main: atci_port_send_data, g_atci_mux_index_inuse %d is UNUSE
 [M:atci C:error F: L: ]: ATCI main: atci_port_send_data, g_atci_mux_index_inuse error:%d
 [M:atci C:error F: L: ]: ATCI main: atci_port_send_data,mux_control fail:%d
 [M:atci C:error F: L: ]: ATCI main: atci_port_send_data,fail:%d
 [M:atci C:error F: L: ]: ATCI main: atci_port_read_data,fail:%d
 [M:atci C:error F: L: ]: ATCI main: atci_send_data_int,fail
 [M:atci C:info F: L: ]: ATCI main: atci_send_data_int, send data len:%x
 [M:atci C:info F: L: ]: ATCI main: atci_send_data, enter, cache_left_len:%x 
 [M:atci C:info F: L: ]: ATCI main: atci_send_data, g_atci_mux_index_inuse is 0 and data not null [M:atci C:error F: L: ]: ATCI main: atci_send_data, fail
 [M:atci C:info F: L: ]: ATCI main: atci_send_data, sending data(len:%x), sent data(len:%x)
 [M:atci C:info F: L: ]: ATCI main: atci_send_data, g_atci_mux_index_inuse is 0 [M:atci C:info F: L: ]: ATCI main: atci_send_data, atci_mem_free
 [M:atci C:info F: L: ]: ATCI main: atci_send_data, send AT cmd response done
 [M:atci C:warning F: L: ]: ATCI main: atci_send_data, alloc memory fail, drop the cached data(data_len:%x)
 [M:atci C:info F: L: ]: ATCI main: atci_send_data, cached data(data_len:%d)
 [M:atci C:info F: L: ]: ATCI main: atci_read_mux_data, enter,g_atci_port_handle:%x, data_len = %d, g_data_len = %d
 [M:atci C:warning F: L: ]: ATCI main: atci_read_mux_dataalloc mem to get data from port
 [M:atci C:warning F: L: ]: ATCI main: atci_read_mux_data alloc mem fail
 [M:atci C:warning F: L: ]: ATCI main: atci_read_mux_data atci_port_read_data len is bigger than %d
 [M:atci C:info F: L: ]: ATCI main: atci_read_mux_data atci_port_read_data read_data_len %d, total len %d
 [M:atci C:warning F: L: ]: ATCI main: atci_read_mux_data drop this cmd, need wait the response of previous AT cmd 
 [M:atci C:warning F: L: ]: ATCI main: atci_read_mux_data fail 
 [M:atci C:warning F: L: ]: ATCI main: atci_read_mux_data fail, input AT cmd length is larger than %d 
 [M:atci C:warning F: L: ]: ATCI main: atci_read_mux_data fail, receive data is not AT cmd type, drop this cmd! 
 [M:atci C:error F: L: ]: ATCI main: atci_processing, create mutex fail
 [M:atci C:info F: L: ]: ATCI main: atci_processing,enter
 [M:atci C:info F: L: ]: ATCI main: atci_processing, take AT handler mutex 
 [M:atci C:info F: L: ]: ATCI main: atci_processing,take protect mutex 
 [M:atci C:info F: L: ]: ATCI main: atci_processing, commad_in_processing(%x), input_queue_msg_num(%d)
 [M:atci C:info F: L: ]: ATCI main: atci_processing, msg_id(%x)\r
 [M:atci C:info F: L: ]: ATCI main: atci_processing msg_data(%d) handle(%d)
 [M:atci C:warning F: L: ]: ATCI main: atci_processing g_data_len == 0 [M:atci C:info F: L: ]: ATCI main: atci_processing, handle AT input
 [M:atci C:info F: L: ]: ATCI main: atci_processing, cached_queue_msg_num(%d)
 [M:atci C:info F: L: ]: ATCI main: atci_processing, g_send_data_cache_left_len(%x)
 [M:atci C:info F: L: ]: ATCI main: atci_processing, handle AT response
 [M:atci C:info F: L: ]: ATCI main: atci_processing, handle AT urc 
 [M:atci C:info F: L: ]: ATCI main: atci_processing() finish, at cmd switch to race cmd.
 [M:atci C:info F: L: ]: ATCI main: atci_data_processing_for_deinit, cached_queue_msg_num(%d)
 [M:atci C:info F: L: ]: ATCI main: atci_data_processing_for_deinit, notify_queue_msg_num(%d)
 [M:atci C:error F: L: ]: ATCI main: atci_processing() get response queue data fail
 [M:atci C:info F: L: ]: ATCI main: atci_data_processing_for_deinit, handle AT response
 [M:atci C:info F: L: ]: ATCI main: atci_data_processing_for_deinit, handle AT urc 
 [M:atci C:info F: L: ]: ATCI main: atci_data_processing_for_deinit, handle AT send cmd done
 [M:atci C:error F: L: ]: ATCI main: atci_deinit, mux_query_user_handle fail
 [M:atci C:error F: L: ]: ATCI main: atci_deinit, atci_mux_find_index_by_handle fail
 [M:atci C:error F: L: ]: ATCI main: atci_deinit, mux_close status(%d)
 [M:atci C:info F: L: ]: ATCI main: atci_deinit success, port(%d), handle(%x)
 [M:atci C:info F: L: ]: ATCI main: atci_deinit, enter (flag:%d)
 [M:atci C:error F: L: ]: ATCI main: atci_deinit %x 
 [M:atci C:error F: L: ]: ATCI main: atci_deinit, fail 
 [M:atci C:info F: L: ]: ATCI main: atci_deinit, success 
 [M:atci C:info F: L: ]: ATCI main: atci_deinit_keep_table, enter (flag:%d)
 [M:atci C:error F: L: ]: ATCI main: atci_deinit_keep_table %x 
 [M:atci C:info F: L: ]: ATCI main: atci_deinit_keep_table, success 
 [M:atcmd C:info F: L: ]: ATCMD: atci_cmd_hdlr_test
 [M:atcmd C:info F: L: ]: ATCMD: AT Test OK.
 [M:atcmd C:info F: L: ]: ATCMD: AT Read done.
 [M:atcmd C:info F: L: ]: ATCMD: AT Active OK.
 [M:atcmd C:info F: L: ]: ATCMD: AT Executing...
 [M:atcmd C:info F: L: ]: ATCMD: change atci log print level into info.
 [M:atcmd C:warning F: L: ]: ATCMD: change atci log print level into warning.
 [M:atcmd C:warning F: L: ]: ATCMD: AT Read mode after caculate, hash1=%x,hash2=%x [M:atcmd C:info F: L: ]: ATCMD: at_cmd_init register success
 [M:atcmd C:error F: L: ]: ATCMD: at_cmd_init register fail
 [M:atcmd C:warning F: L: ]: ATCMD: atci_cmd_hdlr_sdkinfo
 [M:atcmd C:warning F: L: ]: ATCMD: AT Executing...
 [M:common C:info F: L: ]: atci_cmd_hdlr_wdt 
 [M:atci_reg C:info F: L: ]: [REG]atci_cmd_hdlr_reg 
 [M:atci_reg C:info F: L: ]: [REG]read register address:0x%x
 [M:atci_reg C:info F: L: ]: [REG]register address:0x%x, set register value:0x%x
 [M:atci_reg C:error F: L: ]: [REG]register address failed as error input
 [M:atci_reg C:info F: L: ]: [REG]read register address:0x%x
 [M:atci_reg C:info F: L: ]: [REG]register address:0x%x, set register value:0x%x
 [M:atci_reg C:error F: L: ]: [REG]register address failed as error input
 [M:atcmd C:info F: L: ]: [clock]atci_cmd_hdlr_clock
 [M:common C:info F: L: ]: Command is null [M:common C:info F: L: ]: command lengh error [M:hal C:info F: L: ]: Enter Sleep
 [M:hal C:info F: L: ]: Wakeup from Sleep
 [M:hal C:info F: L: ]: ========================= [M:hal C:info F: L: ]: sm_wfi[%d]sm_lp[%d] [M:hal C:info F: L: ]: ========================= [M:hal C:info F: L: ]: SLEEP MANAGER AT_COMMAND SUPPORT  [M:hal C:info F: L: ]: HQASLEEP: %d [M:hal C:info F: L: ]: ====[RTC]==== [M:hal C:info F: L: ]: =======[IDLE_192Mhz_0p6v]======
 [M:hal C:info F: L: ]: =======[IDLE_192Mhz_0p7v]======
 [M:hal C:info F: L: ]: =======[IDLE_384Mhz_0p7v]======
 [M:hal C:info F: L: ]: ======[Freq:%d]======
 [M:hal C:info F: L: ]: SPM_MTCMOS_AON  %d
 [M:hal C:info F: L: ]: SPM_MTCMOS_L1  %d
 [M:hal C:info F: L: ]: SPM_MTCMOS_L5  %d
 [M:hal C:info F: L: ]: [%d][%d][%d]
 [M:hal C:info F: L: ]: 0:GPT 1:EINT 3:SPI 4:WDT 13:GPS_L1 15:GPS_L5  [M:hal C:info F: L: ]: SPM_CM4_WAKEUP_SOURCE_MASK [%x][%x]
 [M:hal C:info F: L: ]: SPM_CM4_WAKEUP_SOURCE_MASK [%x][%x]
 [M:hal C:info F: L: ]: Debug setting[%x] [M:hal C:info F: L: ]: [low power flag %d] [M:hal C:info F: L: ]: Debug setting[%x] [M:common C:info F: L: ]: input_value1:%d, input_value2:%d
 [M:hal C:info F: L: ]: PDN_PD_COND0 [%x][%x]
 [M:hal C:info F: L: ]: XO_PDN_AO_COND0 [%x][%x]
 [M:hal C:info F: L: ]: XO_PDN_TOP_COND0 [%x][%x]
 [M:hal C:info F: L: ]: XO_PDN_PD_COND0 [%x][%x]
 [M:hal C:info F: L: ]: 2[%d][%d][%d]
 [M:hal C:info F: L: ]: [%x][%x]
 [M:hal C:info F: L: ]: [%x][%x]
 [M:hal C:info F: L: ]: [%x][%x]
 [M:hal C:info F: L: ]: [%x][%x]
 [M:hal C:info F: L: ]: [%x][%x]
 [M:hal C:info F: L: ]: PDN_PD_COND0 [%x][%x]
 [M:hal C:info F: L: ]: XO_PDN_AO_COND0 [%x][%x]
 [M:hal C:info F: L: ]: XO_PDN_TOP_COND0 [%x][%x]
 [M:hal C:info F: L: ]: XO_PDN_PD_COND0 [%x][%x]
 [M:hal C:info F: L: ]: [0x%x  :0x%x]
 [M:hal C:info F: L: ]: input_domain:0x%x, input_value:0x%x
 [M:atcmd C:info F: L: ]: [mnl]dsp_type: %d, set LP mode: %d, duration: %d [M:atcmd C:info F: L: ]: ATCMD: atci_cmd_hdlr_crypto 
 [M:atcmd C:info F: L: ]: ATCMD: 

 param=%s

 [M:atcmd C:info F: L: ]: ATCMD: 

 TYPE=%s

 [M:atcmd C:info F: L: ]: ATCMD: 

 KEY_LEN=%s

 [M:atcmd C:info F: L: ]: ATCMD: 

 MODE=%s

 [M:common C:info F: L: ]: atci_cmd_hdlr_nvdm 
 [M:common C:info F: L: ]: nvdm_read_data_item status = %d [M:common C:info F: L: ]: nvdm_read_data_item length = %d [M:common C:info F: L: ]: length = %d [M:common C:warning F: L: ]: Please input hex string with even byte count.
 [M:common C:info F: L: ]: parse length = %d [M:common C:info F: L: ]: data =  [M:common C:info F: L: ]: 0x%x [M:common C:info F: L: ]: nvdm_write_data_item status = %d [M:common C:info F: L: ]: Input parameter ERROR, please run AT+ENVDM=? to get detail information [M:common C:warning F: L: ]: Unsupport command, please check SYSTEM_DAEMON_TASK_ENABLE [M:common C:warning F: L: ]: status: %d item_type: %d
 [M:common C:warning F: L: ]: status: %d item_type: %d
 [M:common C:warning F: L: ]: status: %d item_size: %d
 [M:common C:warning F: L: ]: AT+ENVDM=R ERROR because of pvPortMalloc fail [M:common C:info F: L: ]: nvdm_read_data_item status = %d [M:common C:info F: L: ]: nvdm_read_data_item length = %d [M:common C:info F: L: ]: nvdm_delete_data_item status = %d [M:common C:info F: L: ]: nvdm_delete_group status = %d [M:common C:info F: L: ]: nvdm_delete_all status = %d [M:common C:info F: L: ]: length = %d [M:common C:info F: L: ]: parse length = %d [M:common C:info F: L: ]: nvdm_write_data_item status = %d [M:common C:info F: L: ]: length = %d [M:common C:info F: L: ]: parse length = %d [M:common C:info F: L: ]: nvdm_write_data_item status = %d [M:atci_serialport C:info F: L: ]: [serialport]atci_cmd_hdlr_serial_port query command usage
 [M:atci_serialport C:info F: L: ]: [serialport]atci_cmd_hdlr_serial_port read port config
 [M:atci_serialport C:info F: L: ]: [serialport]atci_cmd_hdlr_serial_port write port config
 [M:atci_serialport C:info F: L: ]: [serialport]atci_cmd_hdlr_serial_port switch port
 [M:atci_serialport C:info F: L: ]: [serialport]atci_cmd_hdlr_serial_port modify parameters
 [M:atci_serialport C:info F: L: ]: [serialport]atci_cmd_hdlr_serial_port show parameters
 [M:atci_mux_port C:info F: L: ]: [MUX_atci_port][ERROR_PARAMETER]: please check user_name or device [M:atci_mux_port C:info F: L: ]: [MUX_atci_port][ERROR_NVDM_NOT_FOUND]: cannot query atci user in NVDM [M:atci_mux_port C:info F: L: ]: [MUX_atci_port][ERROR_NVDM_NOT_FOUND]: cannot query syslog user in NVDM [M:atci_mux_port C:info F: L: ]: [MUX_atci_port]syslog port in NVDM must < 2 [M:atci_mux_port C:info F: L: ]: [MUX_atci_port]atci port in NVDM must < 2 [M:atci_mux_port C:info F: L: ]: [MUX_atci_port]atci setting port can't be syslog port [M:atci_mux_port C:info F: L: ]: [MUX_atci_port]mux port delete ATCI from NVDM: error [M:atci_mux_port C:info F: L: ]: [MUX_atci_port]mux port delete ATCI from NVDM: OK [M:atci_mux_port C:info F: L: ]: [MUX_atci_port]mux port open ATCI save to NVDM: error  [M:atci_mux_port C:info F: L: ]: [MUX_atci_port]mux port open ATCI save to NVDM: OK  [M:atci_mux_port C:info F: L: ]: [MUX_atci_port]atci_cmd_hdlr_mux_port query command usage
 [M:atci_mux_port C:info F: L: ]: [MUX_atci_port]atci_cmd_hdlr_mux_port query current status
 [M:atci_mux_port C:info F: L: ]: [MUX_atci_port]status = %d mux port %d user number %d  [M:atci_mux_port C:info F: L: ]: [MUX_atci_port]mux query user name status = %d [M:atci_mux_port C:info F: L: ]: [MUX_atci_port]atci_cmd_hdlr_mux_port read port config
 [M:atci_mux_port C:info F: L: ]: [MUX_atci_port]status = %d mux port %d user number %d  [M:atci_mux_port C:info F: L: ]: [MUX_atci_port]mux query user name status = %d [M:atci_mux_port C:info F: L: ]: [MUX_atci_port]atci_cmd_hdlr_mux_port write port config
 [M:atci_mux_port C:info F: L: ]: [MUX_atci_port]atci_cmd_hdlr_mux_port ignore data of a port
 [M:atci_mux_port C:info F: L: ]: [MUX_atci_port]atci_cmd_hdlr_mux_port config port setting in nvdm
 [M:atci_mux_port C:info F: L: ]: [MUX_atci_port][delete_all_users]status = %d mux_port %d user number %d  [M:atci_mux_port C:info F: L: ]: [MUX_atci_port][delete_all_users]mux query user name status = %d [M:atci_mux_port C:info F: L: ]: [MUX_atci_port][delete_all_users]mux_close_delete_from_nvdm fail port = %d [M:atci_mux_port C:info F: L: ]: [MUX_atci_port][delete_all_users]mux_close_delete_from_nvdm success port = %d [M:atci_mux_port C:info F: L: ]: [MUX_atci_port]at_mux_delete_port_setting_from_nvdm failed port=%d
 [M:atcmd C:error F: L: ]: ATSS: memory malloced failed.
 [M:atci_sflash C:info F: L: ]: [SFLASH]NVDM EC dump from power on: 
 [M:atci_sflash C:info F: L: ]: [SFLASH]%8d, %8d, %8d, %8d, %8d, %8d, %8d, %8d 
 [M:atci_sflash C:info F: L: ]: [SFLASH]
 NVDM EC dump from power on Finished. 
 [M:atci_sflash C:info F: L: ]: [SFLASH]FS EC dump from power on: 
 [M:atci_sflash C:info F: L: ]: [SFLASH]NVDM EC dump start : 
 [M:atci_sflash C:info F: L: ]: [SFLASH]%8d, %8d, %8d, %8d, %8d, %8d, %8d, %8d 
 [M:atci_sflash C:info F: L: ]: [SFLASH]
 NVDM EC dump start finished 
 [M:atci_sflash C:info F: L: ]: [SFLASH]NVDM EC dump end: 
 [M:atci_sflash C:info F: L: ]: [SFLASH]%8d, %8d, %8d, %8d, %8d, %8d, %8d, %8d 
 [M:atci_sflash C:info F: L: ]: [SFLASH]
 NVDM EC dump end finished 
 [M:atci_sflash C:info F: L: ]: [SFLASH]FS EC dump start: 
 [M:atci_sflash C:info F: L: ]: [SFLASH]%8d, %8d, %8d, %8d, %8d, %8d, %8d, %8d 
 [M:atci_sflash C:info F: L: ]: [SFLASH]
 FS EC dump from power on finished 
 [M:atci_sflash C:info F: L: ]: [SFLASH]%8d, %8d, %8d, %8d, %8d, %8d, %8d, %8d 
 [M:atci_sflash C:info F: L: ]: [SFLASH]
 FS EC dump start finished 
 [M:atci_sflash C:info F: L: ]: [SFLASH]FS EC dump end: 
 [M:atci_sflash C:info F: L: ]: [SFLASH]%8d, %8d, %8d, %8d, %8d, %8d, %8d, %8d 
 [M:atci_sflash C:info F: L: ]: [SFLASH]
 FS EC dump end finished 
 [M:atci_sflash C:info F: L: ]: [SFLASH]Test from first power on:  
 [M:atci_sflash C:info F: L: ]: [SFLASH]NVDM Total erase count: 0x%x 
 [M:atci_sflash C:info F: L: ]: [SFLASH]FS Total erase count: 0x%x 
 [M:atci_sflash C:info F: L: ]: [SFLASH]Test duration: %d second
 [M:atci_sflash C:info F: L: ]: [SFLASH]NVDM Total erase count: 0x%x 
 [M:atci_sflash C:info F: L: ]: [SFLASH]NVDM MAX count: 0x%x 
 [M:atci_sflash C:info F: L: ]: [SFLASH]FS Total erase count: 0x%x 
 [M:atci_sflash C:info F: L: ]: [SFLASH]FS MAX count: 0x%x 
 [M:atci_sflash C:info F: L: ]: [SFLASH]FS Total erase count: 0x%x 
 [M:atci_sflash C:info F: L: ]: [SFLASH]tci_cmd_hdlr_Sflash_lifecycle 
 [M:atci_sflash C:info F: L: ]: [SFLASH]OTP read: addr:0x%x, length:0x%x, data = 0x%x
 [M:atci_sflash C:info F: L: ]: [SFLASH]OTP read: addr:0x%x, length:0x%x, data = 0x%x
 [M:atci_sflash C:info F: L: ]: [SFLASH]OTP read: addr:0x%x, length:0x%x, data = 0x%x
 [M:atci_sflash C:info F: L: ]: [SFLASH]OTP write: addr:0x%x, length:0x%x, data = 0x%x
 [M:atci_sflash C:info F: L: ]: [SFLASH]flash write: addr:0x%x, length:0x%x, data = 0x%x
 [M:atci_sflash C:info F: L: ]: [SFLASH]Flash erase: addr:0x%x, blk_type = %d, result = 0x%x
 [M:mnl C:error F: L: ]: LP_lyr,[timer], low_power_timer_callback , pxTimer = 0x%x (0x%x)
 [M:mnl C:warning F: L: ]: LP_lyr,[timer], pl_lowpower_stop_os_timer g_timer_for_lowpower = NULL
 [M:mnl C:error F: L: ]: LP_lyr,[timer], pl_lowpower_stop_os_timer failed to stop, handler = 0x%x
 [M:mnl C:error F: L: ]: LP_lyr,[timer], pl_lowpower_start_os_timer failed to start, reset timer handle(0x%x)
 [M:mnl C:error F: L: ]: LP_lyr,[timer], pl_lowpower_set_timer dura=%d, MAX=%d 
 [M:mnl C:error F: L: ]: LP_lyr,pl_lowpower_set_timer fail!! [M:mnl C:error F: L: ]: LP_lyr,pl_lowpower_stop_timer, dsp_type=%d 
 [M:mnl C:error F: L: ]: LP_glp,pl_lowpower_glp_set_timer duration >= 1000, (%d)!! [M:mnl C:error F: L: ]: LP_glp,GPS L1 GPT init fail!! [M:mnl C:error F: L: ]: LP_glp,GPS L1 GPT register callback fail!! [M:mnl C:error F: L: ]: LP_glp,GPS L1 GPT start timer fail!! [M:mnl C:error F: L: ]: LP_glp,GPS L5 GPT init fail!!, stop l1 gpt timer [M:mnl C:error F: L: ]: LP_glp,GPS L5 GPT register callback fail!! [M:mnl C:error F: L: ]: LP_glp,GPS L5 GPT start timer fail!! [M:mnl C:error F: L: ]: LP_glp,pl_lowpower_glp_set_timer fail!! [M:mnl C:error F: L: ]: LP_glp,GPS L1 GPT stop timer fail!! [M:mnl C:error F: L: ]: LP_glp,GPS L5 GPT stop timer fail!! [M:mnl C:error F: L: ]: FS,Open file Fail,%d [M:mnl C:info F: L: ]: FS,Open file OK,%d [M:mnl C:error F: L: ]: FS,f_read Fail,%d,%d [M:mnl C:error F: L: ]: FS,f_write Fail,%d,%d [M:mnl C:error F: L: ]: PortLyr,Get Vcore Fail!! Vcore/Freq does not match [M:mnl C:warning F: L: ]: PortLyr,pl_platform_set_dvfs failed to set dvfs (%d to %d), results: %d! [M:mnl C:info F: L: ]: GFE_lyr,GNSS_GEOFENCING_PIN: %d [M:mnl C:info F: L: ]: GFE_lyr,GNSS_GEOFENCING:GPIO_DATA_HIGH [M:mnl C:info F: L: ]: GFE_lyr,GNSS_GEOFENCING_PIN: %d [M:mnl C:info F: L: ]: GFE_lyr,GNSS_GEOFENCING:GPIO_DATA_LOW [M:mnl C:error F: L: ]: PortLyr,[timer], mtk_gps_sys_stop_timer failed to stop, id=%d, handler = 0x%x
 [M:GAPP C:error F: L: ]: [Sleep] gnss_app_time_get_rtc:%d-%d-%d,%d-%d-%d [M:GAPP C:warning F: L: ]: [Sleep] gnss_app_sleep_eint_restart_timer [M:GAPP C:warning F: L: ]: [Sleep] gnss_app_sleep_eint_lock:GIO [M:GAPP C:warning F: L: ]: [Sleep] gnss_app_sleep_eint_unlock:GIO [M:GAPP C:warning F: L: ]: [Sleep]gnss_sleep_timer_handle_func [M:GAPP C:warning F: L: ]: [Sleep] gnss_app_sleep_eint_init:%d
 [M:GAPP C:warning F: L: ]: [Sleep] gnss_app_sleep_lock:GAPP [M:GAPP C:warning F: L: ]: [Sleep] gnss_app_sleep_unlock:GAPP [M:GAPP C:warning F: L: ]: [Sleep] gnss_app_sleep_init:%d
 [M:GAPP C:info F: L: ]: send data: [M:GAPP C:info F: L: ]: ,%02x [M:GAPP C:info F: L: ]: send data result:%d,%d [M:GAPP C:info F: L: ]: ,%02x [M:GAPP C:info F: L: ]: send rtcm: [M:GAPP C:info F: L: ]: ,%02x [M:GAPP C:info F: L: ]: send others-b [M:GAPP C:info F: L: ]: ,%02x [M:GAPP C:info F: L: ]: gnss_task_main [M:GAPP C:info F: L: ]: gnss_task_msg_handler:%d
 [M:GAPP C:error F: L: ]: gnss_app_convert_32K_free_count_to_tow: send command FAIL, %d! [M:GAPP C:error F: L: ]: gnss_app_convert_32K_free_count_to_tow: get parameter FAIL! [M:GAPP C:error F: L: ]: gnss_app_get_curr_tow: send command FAIL, %d! [M:GAPP C:error F: L: ]: gnss_app_get_curr_tow: get parameter FAIL! [M:GAPP C:error F: L: ]: gnss_app_get_gnss_tow: send command FAIL, %d! [M:GAPP C:error F: L: ]: gnss_app_get_gnss_tow: get parameter FAIL! [M:GAPP C:info F: L: ]: GNSS power on confirm. [M:GAPP C:info F: L: ]: GNSS power off confirm. [M:GAPP C:info F: L: ]: send data result:%d,%d [M:GAPP C:info F: L: ]: ,%02x [M:GAPP C:error F: L: ]: read GSV mode setting, fail [M:GAPP C:error F: L: ]: read GSV type setting, fail [M:GAPP C:info F: L: ]: NMEA data, total len: %d [M:GAPP C:info F: L: ]: LOG data, total len: %d [M:GAPP C:info F: L: ]: Dump data, total len: %d [M:GAPP C:info F: L: ]: Location data, PVT_SV_IN_VIEW_INFO:0x%X, PVT_SV_STATUS:0x%X [M:GAPP C:info F: L: ]: Binary data, total len: %d [M:GAPP C:info F: L: ]: RTCM data, total len: %d [M:GAPP C:info F: L: ]: Fusion, DR stage = %d, Static status = %d, Motion Alarm = %d [M:GAPP C:info F: L: ]: Raw data, RAW_CHANNEL:%d, RAW_SATELLITE:%d [M:GAPP C:warning F: L: ]: [Sleep]gnss_app_sleep_eint_irq [M:GAPP C:error F: L: ]: gnss_app_send_msg: FAIL! [M:GAPP C:info F: L: ]: gnss_app_init [M:GAPP C:info F: L: ]: gnss_app_task_init:%d, result:%d
 [M:GAPP C:info F: L: ]: gnss_app_rtcm_task_main [M:GAPP C:info F: L: ]: gnss_task_msg_handler:%d
 [M:GAPP C:info F: L: ]: gnss_app_rtcm_task_init:%d, result:%d
 [M:GAPP C:info F: L: ]: [IO] gnss_io_irq_handle, port:%d, user:%d, type:%d, len:%d [M:GAPP C:error F: L: ]: [IO] gnss_io_irq_handle, Malloc Fail! P:%d, Len:%d [M:GAPP C:error F: L: ]: [IO] gnss_io_irq_handle, Malloc Fail! P:%d, Len:%d [M:GAPP C:error F: L: ]: [IO] gnss_io_irq_handle, Malloc Fail! P:%d, Len:%d [M:GAPP C:error F: L: ]: [IO] gnss_io_irq_handle, Malloc Fail! P:%d, Len:%d [M:GAPP C:error F: L: ]: [IO] gnss_io_irq_handle, Malloc Fail! P:%d, Len:%d [M:GAPP C:error F: L: ]: [IO] gnss_io_irq_handle, Malloc Fail! P:%d, Len:%d [M:GAPP C:error F: L: ]: [IO] gnss_io_irq_handle, Malloc Fail! P:%d, Len:%d [M:GAPP C:error F: L: ]: [IO] gnss_io_irq_handle, Malloc Fail! P:%d, Len:%d [M:GAPP C:info F: L: ]: [Notify] Notify host data ready end:%d [M:GAPP C:error F: L: ]: [IO] gnss_io_add_user fail:%d, %d, %d [M:GAPP C:info F: L: ]: [IO] gnss_io_add_user %d, %d, %d [M:GAPP C:info F: L: ]: [IO] gnss_io_remove_user %d, %d, %d [M:GAPP C:warning F: L: ]: [IO] gnss_io_remove_all_user %d, %d [M:GAPP C:info F: L: ]: [Notify] Notify host data ready:%d [M:GAPP C:info F: L: ]: [Notify] Notify host data ready:%d [M:GAPP C:error F: L: ]: gnss_io_data_notify_wakeup start timer [M:GAPP C:error F: L: ]: gnss_io_data_notify_wakeup restart timer [M:GAPP C:error F: L: ]: gnss_io_save_setting_to_nvdm fail [M:GAPP C:info F: L: ]: gnss_io_load_setting_from_nvdm fail [M:GAPP C:warning F: L: ]: [IO] gnss_io_deinit_port %d, %d [M:GAPP C:error F: L: ]: [IO] gnss_io_deinit_port fail-1! %d, %d [M:GAPP C:error F: L: ]: [IO] gnss_io_deinit_port fail-2! %d, %d [M:GAPP C:error F: L: ]: [IO] gnss_io_init fail [M:GAPP C:error F: L: ]: [IO] gnss_io_init check type fail:%d [M:GAPP C:warning F: L: ]: [IO] gnss_io_init [M:GAPP C:warning F: L: ]: [IO] gnss_io_deinit [M:GAPP C:warning F: L: ]: [IO] gnss_io_close_port, %d:%d [M:GAPP C:error F: L: ]: [IO] gnss_io_send_data_int, Fail![%d,%d]:%d [M:GAPP C:info F: L: ]: [IO] gnss_io_send_data_int, port not ready![%d,%d]:%d [M:GAPP C:error F: L: ]: [IO] gnss_io_send_data_int, data is empty![%d,%d] [M:GAPP C:error F: L: ]: [IO] gnss_io_send_data_int, reset error_check_timer fail! [M:GAPP C:info F: L: ]: [IO] gnss_io_send_data_by_user, %d:%d:%d [M:GAPP C:info F: L: ]: [IO] gnss_io_send_data_by_port, [%d,%d]:%d [M:GAPP C:error F: L: ]: [IO] gnss_io_send_data_by_port, Fail-1![%d,%d]:%d [M:GAPP C:warning F: L: ]: [IO] gnss_io_open_port, %d:%d [M:GAPP C:info F: L: ]: [IO] gnss_io_set_data_type, [%d,%d]:%d [M:GAPP C:warning F: L: ]: [IO] gnss_io_set_data_type1, [%d,%d]new:%d, org:%d [M:GAPP C:warning F: L: ]: [IO] load default setting fail! %d out of [%d:%d] [M:GAPP C:info F: L: ]: [IO] load default setting fail! error port type:%d [M:GAPP C:error F: L: ]: [IO] load default setting fail! %d data type error %d [M:GAPP C:warning F: L: ]: [IO] gnss_io_load_config [M:GAPP C:info F: L: ]: [IO] gnss_io_init from NVDM [M:GAPP C:info F: L: ]: [IO] gnss_io_init from Flash [M:GAPP C:info F: L: ]: [IO] gnss_io_init from Hard Code [M:GAPP C:info F: L: ]: [IO] gnss_io_set_baudrate, [%d,%d]:%d [M:GAPP C:info F: L: ]: [IO] gnss_io_set_flow_control, [%d,%d]:%d [M:GAPP C:info F: L: ]: [IO] gnss_io_get_data_type, [%d,%d] [M:GAPP C:info F: L: ]: [IO] gnss_io_get_baudrate, [%d,%d] [M:GAPP C:info F: L: ]: [IO] gnss_io_get_flow_control, [%d,%d] [M:GAPP C:info F: L: ]: [Notify] Notify host data ready:%d [M:GAPP C:info F: L: ]: [Notify] Notify host data ready begin:%d [M:GAPP C:info F: L: ]: [MUX] gnss_io_rx_protocol_callback0 [M:GAPP C:info F: L: ]: [MUX] gnss_io_rx_protocol_callback malloc fail! [M:GAPP C:info F: L: ]: [MUX] gnss_io_rx_protocol_callback1, %d, len:%d [M:GAPP C:warning F: L: ]: [MUX] gnss_io_mux_init,[%d,%d], type:%d, rx:%d, tx:%d, bau:%d, sw_flow:%d [M:GAPP C:error F: L: ]: [MUX] gnss_io_mux_init, Fail![%d,%d],r:%d [M:GAPP C:info F: L: ]: [MUX] gnss_io_mux_init, Success![%d,%d] [M:GAPP C:warning F: L: ]: [MUX] gnss_io_mux_deinit, %d [M:GAPP C:info F: L: ]: [MUX] gnss_io_mux_write, %d, len:%d [M:GAPP C:error F: L: ]: [MUX] gnss_io_mux_write, Fail! %d send %d: %d [M:GAPP C:info F: L: ]: [MUX] gnss_io_mux_read, %d, len:%d [M:GAPP C:error F: L: ]: [MUX] gnss_io_mux_read, Fail! %d: %d [M:GAPP C:error F: L: ]: [SDCARD] gnss_io_sdcard_log_count_init_from_nvdm, %d [M:GAPP C:error F: L: ]: [SDCARD] gnss_io_sdcard_open_int, result:%d [M:GAPP C:info F: L: ]: [SDCARD] gnss_io_sdcard_read_int, len:0 [M:GAPP C:error F: L: ]: [SDCARD] gnss_io_sdcard_write_int, sync fail:%d [M:GAPP C:error F: L: ]: [SDCARD] gnss_io_sdcard_write_int, write fail:%d [M:GAPP C:info F: L: ]: [SDCARD] gnss_io_sdcard_write_int, len:%d [M:GAPP C:error F: L: ]: gnss_io_test_port_init fail [M:GAPP C:error F: L: ]: gnss_app_log_test_handler:%d [M:GAPP C:warning F: L: ]: gnss_app_log_test_handler:%d [M:GAPP C:info F: L: ]: gnss_app_log_test_handler:%d [M:GAPP C:info F: L: ]: atci_register_handler register success [M:GAPP C:error F: L: ]: atci_register_handler register fail [M:GAPP C:info F: L: ]: gnss_app_load_default_setting:%d,%d [M:GAPP C:error F: L: ]: gnss_app_read_default_setting fail 1 [M:GAPP C:error F: L: ]: gnss_app_read_default_setting fail 2 [M:GAPP C:error F: L: ]: gnss_app_check_nvdm_setting Without NVDM setting. error code: %d [M:GAPP C:error F: L: ]: gnss_app_check_nvdm_setting NVDM setting not match. Read:%d, Real:%d [M:GAPP C:error F: L: ]: gnss_app_clear_nvdm_setting Clear all:%d [M:GAPP C:error F: L: ]: [LOCUS]locus_demo_fopen fail:%d [M:GAPP C:error F: L: ]: [LOCUS]locus_demo_fread fail:%d
 [M:GAPP C:error F: L: ]: [LOCUS]locus_demo_fwrite fail:%d [M:GAPP C:error F: L: ]: [LOCUS]locus_demo_fdelete fail:%d [M:GAPP C:error F: L: ]: [LOCUS]locus_demo_init_from_nvdm [M:GAPP C:error F: L: ]: [LOCUS]locus_data_init_from_hard_code [M:GAPP C:error F: L: ]: [LOCUS]locus_demo_init_from_default [M:GAPP C:info F: L: ]: [LOCUS]locus_demo_check_status: %08X,%02X,%08X,%08X,%04X,%04X,%04X,%04X,%02X [M:GAPP C:info F: L: ]: [LOCUS]locus_demo_check_status[false]: not enable [M:GAPP C:info F: L: ]: [LOCUS]locus_demo_check_status[false]: no fix [M:GAPP C:info F: L: ]: [LOCUS]locus_demo_check_status: buffer full [M:GAPP C:info F: L: ]: [LOCUS]locus_demo_check_status[false]: full stop [M:GAPP C:info F: L: ]: [LOCUS]locus_demo_check_status: over loop [M:GAPP C:info F: L: ]: [LOCUS]locus_demo_check_status[true]: normal save [M:GAPP C:info F: L: ]: [LOCUS]locus_demo_check_status[true]: save by user [M:GAPP C:info F: L: ]: [LOCUS]locus_demo_check_status[true]: save before sleep [M:GAPP C:info F: L: ]: [LOCUS]locus_demo_check_status[true]: out of time, %d > (%d + %d) [M:GAPP C:info F: L: ]: [LOCUS]locus_demo_check_status[true]: out of speed, %d > %d [M:GAPP C:info F: L: ]: [LOCUS]locus_demo_check_status[true]: out of distance, ([%d:%d] - [%d:%d]) >%d [M:GAPP C:info F: L: ]: [LOCUS]locus_demo_check_status[false]: final return [M:GAPP C:info F: L: ]: [LOCUS]Save data: %08X,%02X,%08X,%08X,%04X,%04X,%04X,%04X,%02X [M:GAPP C:error F: L: ]: [LOCUS]locus_demo_parse_data checksum error! read:%d, real:%d [M:GAPP C:error F: L: ]: [LOCUS]locus_demo_parse_data record size error! read:%d, real:%d [M:GAPP C:error F: L: ]: [LOCUS]locus_demo_output_nmea pare data fail! [M:GAPP C:error F: L: ]: [LOCUS]locus_demo_init_int,en:%d,md:%d,dt:%d,fs:%d,tm:%d,sp:%d,ds:%d,max:%d,wp:%d [M:GAPP C:error F: L: ]: [LOCUS]locus_demo_init, data type not match, clear old data! [M:GAPP C:error F: L: ]: [LOCUS]locus_demo_init [M:GAPP C:error F: L: ]: [LOCUS]locus_demo_start [M:GAPP C:error F: L: ]: [LOCUS]locus_demo_stop [M:GAPP C:error F: L: ]: [LOCUS]locus_demo_force_save [M:GAPP C:error F: L: ]: [LOCUS]locus_demo_save_before_entry_sleep [M:GAPP C:error F: L: ]: [LOCUS]locus_demo_location_callback Covert Fail [M:GAPP C:error F: L: ]: [LOCUS]locus_demo_clear_data [M:GAPP C:error F: L: ]: [LOCUS]locus_demo_clear_setting [M:GAPP C:error F: L: ]: [LOCUS]locus_demo_set_mode: %d, %d [M:GAPP C:error F: L: ]: [LOCUS]locus_demo_set_threshold, type:%d, data:%d [M:GAPP C:info F: L: ]: [LOCUS]locus_demo_get_data [M:GAPP C:info F: L: ]: [BATCHING] batching flush data size:%d [M:GAPP C:error F: L: ]: [BATCHING] malloc failed, not enough buffer [M:GAPP C:error F: L: ]: [BATCHING] batching binary data encode fail!! Drop this packet [M:GAPP C:info F: L: ]: [BATCHING] batching_demo_start [M:GAPP C:info F: L: ]: [BATCHING] batching_demo_stop [M:GAPP C:info F: L: ]: [BATCHING] set config, time_int:%d,dis_thr:%d,spd_thr:%d,format:%d [M:GAPP C:info F: L: ]: [BATCHING] force flush all cache location [M:GAPP C:info F: L: ]: [BATCHING] force clear all cache location [M:GAPP C:info F: L: ]: [BATCHING] First record fix location when batching is on [M:GAPP C:error F: L: ]: [BATCHING] data is full but not flush, so drop them. It's abnormal please check! [M:GAPP C:error F: L: ]: [BATCHING] location batch FAILED! [M:GAPP C:info F: L: ]: [BATCHING] location batch success! [M:GAPP C:info F: L: ]: [BATCHING] force log incoming location [M:MNL_BOT C:info F: L: ]: BotMsg1,L5_dsp_download_task, wait semaphone  [M:MNL_BOT C:error F: L: ]: BotMsg5,fatal error: failed to malloc memory for boot info [M:MNL_BOT C:info F: L: ]: BotMsg5,%02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X [M:MNL_BOT C:info F: L: ]: BotMsg5,%02X %02X %02X %02X %02X [M:MNL_BOT C:info F: L: ]: BotMsg5,L5 makeDSPBootInfo, info_len=%d recv_len=%d [M:MNL_BOT C:error F: L: ]: BotMsg5,fatal error: failed to parse dsp response(len=%d) for pck_no(%d) [M:MNL_BOT C:debug F: L: ]: BotMsg5,L5_dsp_download set g_l5_waiting_first_dbtt=%d [M:MNL_BOT C:info F: L: ]: BotMsg5,L5 DSP(%d th), parse response, res=%d [M:MNL_BOT C:info F: L: ]: BotMsg5,L5_dsp_download_task done, dual_state=%d [M:MNL_BOT C:info F: L: ]: BotMsg1,L1_dsp_download_task, wait semaphone  [M:MNL_BOT C:info F: L: ]: BotMsg1,L1_dsp_download_task begin to download [M:MNL_BOT C:error F: L: ]: BotMsg1,fatal error: failed to malloc memory for boot info [M:MNL_BOT C:info F: L: ]: BotMsg1,%02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X [M:MNL_BOT C:info F: L: ]: BotMsg1,%02X %02X %02X %02X %02X [M:MNL_BOT C:debug F: L: ]: BotMsg1,makeDSPBootInfo , info_len = %d recv_len %d
 [M:MNL_BOT C:error F: L: ]: BotMsg1,fatal error: failed to parse dsp response(len = %d) for pck_no(%d) [M:MNL_BOT C:debug F: L: ]: BotMsg1,L1 DSP(%d th) , parse response , res = %d [M:MNL_BOT C:debug F: L: ]: BotMsg1,Set DVFS result:%d [M:MNL_BOT C:debug F: L: ]: BotMsg1,hf_fsys_ck freq=%d [M:MNL_BOT C:error F: L: ]: BotMsg1,Fatal Error: failed to set DVFS, err=%d [M:MNL_BOT C:info F: L: ]: BotMsg1,L1_dsp_download_task done, switch to RF_LDO dual_state=%d [M:MNL_BOT C:info F: L: ]: BotMsg,L1_dsp_download_task: vrf_level_shift 1 [M:MNL_BOT C:debug F: L: ]: BotMsg,dsp_transmiter_reopen_l5, sent comd18 [M:MNL_BOT C:error F: L: ]: BotMsg,dsp_transmiter_reopen_l5 can't be called, L5 task doesn't exist! [M:MNL_BOT C:error F: L: ]: BotMsg,dsp_transmiter_reopen_l5 [M:MNL_BOT C:error F: L: ]: BotMsg,dsp_transmiter_reopen_l5, made comd18 [M:MNL_BOT C:error F: L: ]: BotMsg,dsp_transmiter_reopen_l5, sent comd18, begin to dl L5 [M:MNL_BOT C:error F: L: ]: BotMsg,failed to notify L5 download task [M:MNL_BOT C:error F: L: ]: BotMsg,dsp_transmiter_close_l5 1 [M:MNL_BOT C:error F: L: ]: BotMsg,dsp_transmiter_close_l5, send comd17 [M:MNL_BOT C:error F: L: ]: BotMsg,dsp_transmiter_close_l5, 0xC0003134=0x%x [M:MNL_BOT C:error F: L: ]: BotMsg,dsp_transmiter_close_l5, , send/comd4 to close mtcmos [M:MNL_BOT C:error F: L: ]: BotMsg,0xA210020C=[0x%x], 0xC000300C=[0x%x],0xC0003134=[0x%x] [M:MNL_BOT C:error F: L: ]: BotMsg,L5 close done [M:MNL_BOT C:debug F: L: ]: BotMsg,dsp_transmiter_close: switch to DCXO_LDO [M:MNL_BOT C:info F: L: ]: BotMsg,dsp_transmiter_action_close_l1: vrf_level_shift 0 [M:MNL_BOT C:info F: L: ]: BotMsg,=1=[%x] [M:MNL_BOT C:error F: L: ]: BotMsg,dsp_transmiter_close_l1, close L1 done [M:MNL_BOT C:info F: L: ]: BotMsg,dsp_transmiter_close: switch to DCXO_LDO [M:MNL_BOT C:info F: L: ]: BotMsg,dsp_transmiter_close: vrf_level_shift 0 [M:MNL_BOT C:error F: L: ]: BotMsg,L5 COMD4 failed %d times (w=%d), SPM power off [M:MNL_BOT C:info F: L: ]: BotMsg,L5 re-send COMD4 (w=%d) [M:MNL_BOT C:error F: L: ]: BotMsg,L5 COMD4 failed after ACK (w=%d) [M:MNL_BOT C:info F: L: ]: BotMsg,1_s=0x%x,5_s=%x,1.d=0x%x,5.d=0x%x [M:MNL_BOT C:info F: L: ]: BotMsg,L5 apb dma mode deinit [M:MNL_BOT C:info F: L: ]: BotMsg,L1 sent COMD4 [M:MNL_BOT C:error F: L: ]: BotMsg,L1 COMD4 failed %d times (w=%d), SPM power off [M:MNL_BOT C:info F: L: ]: BotMsg,L1 re-send COMD4 (w=%d) [M:MNL_BOT C:error F: L: ]: BotMsg,L1 COMD4 failed after ACK (w=%d) [M:MNL_BOT C:info F: L: ]: BotMsg,1_s=0x%x,5_s=%x,1.d=0x%x,5.d=0x%x [M:MNL_BOT C:info F: L: ]: BotMsg,L1 apb dma mode deinit done [M:hal C:debug F: L: ]: ###waiting L5 first DBTT,  checked dbtt (len=%d) [M:hal C:error F: L: ]: ###waiting L5 first DBRM(%d) [M:hal C:error F: L: ]: ###waiting L5 first DBTT, but recived unexpected msg(len=%d) type=%d [M:MNL_BOT C:info F: L: ]: BotMsg,dsp_transmiter_download_ram_code [M:MNL_BOT C:error F: L: ]: BotMsg,l1_cfg == NULL [M:MNL_BOT C:info F: L: ]: BotMsg,dsp_transmiter_download_ram_code create L1 task [M:MNL_BOT C:error F: L: ]: BotMsg,dsp_transmiter_download_ram_code failed to create semaphone for L1 download task [M:MNL_BOT C:error F: L: ]: BotMsg,failed to create L1 download task [M:MNL_BOT C:error F: L: ]: BotMsg,failed to notify L1 download task [M:MNL_BOT C:info F: L: ]: BotMsg,dsp_transmiter_download_ram_code create L5 task [M:MNL_BOT C:error F: L: ]: BotMsg,dsp_transmiter_download_ram_code failed to create semaphone for L5 download task [M:MNL_BOT C:error F: L: ]: BotMsg,failed to create L5 download task [M:MNL_BOT C:error F: L: ]: BotMsg,failed to notify L5 download task [M:MNL_LOP C:error F: L: ]: LPCTRL,gnss_dsp_wakeup_non_stop_mode !! dsp_type:%d, res =%d [M:MNL_LOP C:error F: L: ]: LPCTRL,gnss_dsp_wakeup_lp_mode fail!! dsp_type:%d [M:MNL_LOP C:info F: L: ]: TerTest,gnss_dsp_wakeup_lp_mode, dsp_type=%d  to set gpt timer 10ms, g_dsp_is_wakeingup=%d [M:MNL_LOP C:error F: L: ]: LPCTRL,Stop GPS gpt timer fail!! dsp_type: %d [M:MNL_LOP C:info F: L: ]: LPCTRL,GPS gpt timeout!! dsp_type: %d [M:MNL_LOP C:debug F: L: ]: LPCTRL,GPS gpt timeout!! [M:MNL_LOP C:debug F: L: ]: LPCTRL,dsp_transmiter_state_machine, res =  %d, action=%d,state=%d [M:MNL_LOP C:debug F: L: ]: LPCTRL,dsp_transmiter_open, res =  %d [M:MNL_LOP C:debug F: L: ]: LPCTRL,dsp_transmiter_close(type), res =  %d [M:MNL_GPS C:debug F: L: ]: BEE,easy: bee , _fgSendNewEph=%d,NumNewEph=%d, [M:MNL_GPS C:debug F: L: ]: BEE,easy: bee , _fgSendNewEph=%d,copy to param, [M:MNL_GPS C:debug F: L: ]: BEE,easy: bee ,[mtk_bee.c] MTK_Bee_Get_New_Eph_Data _fgSendNewEph %d NumNewEphToSend %d [M:MNL_GPS C:debug F: L: ]: BEE,easy: bee ,[mtk_bee.c] 1_MTK_Bee_Receive_New_Eph_Ack SvId %d [M:MNL_GPS C:debug F: L: ]: BEE,easy: bee ,[mtk_bee.c] 2_MTK_Bee_Receive_New_Eph_Ack _fgSendNewEph %d [M:MNL_TIM C:debug F: L: ]: HALTIME,%d, [M:MNL_TIM C:debug F: L: ]: HALTSTA,%d, [M:MNL_TIM C:info F: L: ]: HALTIME,%d,REST [M:MNL_TIM C:info F: L: ]: HALTIME,%d,REST [M:MNL_TIM C:info F: L: ]: HALTIME,%d,Fail [M:MNL_TIM C:debug F: L: ]: HALSETRTC,%d, [M:MNL_COM C:error F: L: ]: PoSwFal,%d [M:MNL_COM C:error F: L: ]: BiPktMn,hal_MTK_Binary_DecodePacket,ret_val=%d [M:MNL_TOW C:debug F: L: ]: MNLTOW,GNSS, notify tick = %u, solution tick = %u, tow = %u [M:MNL_TOW C:warning F: L: ]: MNLTOW,no mnl_tow_gnss_refernece_time_mutex for GNSS [M:MNL_TOW C:warning F: L: ]: MNLTOW,no mnl_tow_gnss_refernece_time_mutex for Converter [M:MNL_TOW C:debug F: L: ]: MNLTOW,free_count = %u, gnss_tick = %u, diff = %d, gnss_tow = %u, tow = %u [M:MNL_TOW C:warning F: L: ]: MNLTOW,no mnl_tow_gnss_refernece_time_mutex for curr TOW [M:MNL_TOW C:debug F: L: ]: MNLTOW,count_in_ms = %u, gnss_tick = %u, diff = %d, gnss_tow = %u, tow = %u [M:MNL_TOW C:warning F: L: ]: MNLTOW,no mnl_tow_gnss_refernece_time_mutex for gnss TOW [M:MNL_BOT C:info F: L: ]: 5566,mtk_gps_set_param [M:MNL_ALO C:error F: L: ]: CfgFail,%d [M:MNL_MSG C:error F: L: ]: AlocFal,%d [M:MNL_MSG C:error F: L: ]: PtkAloc, [M:MNL_MSG C:error F: L: ]: PmtkSnd, [M:MNL_MSG C:error F: L: ]: AgpAloc, [M:MNL_MSG C:error F: L: ]: AgpSndX, [M:MNL_MSG C:error F: L: ]: PtkAloc, [M:MNL_MSG C:error F: L: ]: PmtkSnd, [M:MNL_MSG C:error F: L: ]: PtkAloc, [M:MNL_MSG C:error F: L: ]: PmtkSnd, [M:MNL_MSG C:error F: L: ]: PtkAloc, [M:MNL_MSG C:error F: L: ]: PmtkSnd, [M:MNL_MSG C:error F: L: ]: PtkAloc, [M:MNL_MSG C:error F: L: ]: PmtkSnd, [M:MNL_MSG C:error F: L: ]: PtkAloc, [M:MNL_MSG C:error F: L: ]: PmtkSnd, [M:MNL_MSG C:error F: L: ]: PtkAloc, [M:MNL_MSG C:error F: L: ]: PmtkSnd, [M:MNL_MSG C:error F: L: ]: PtkAloc, [M:MNL_MSG C:error F: L: ]: PmtkSnd, [M:MNL_MSG C:error F: L: ]: PtkAloc, [M:MNL_MSG C:error F: L: ]: PmtkSnd, [M:MNL_MSG C:error F: L: ]: PtkAloc, [M:MNL_MSG C:error F: L: ]: PmtkSnd, [M:MNL_MSG C:error F: L: ]: PtkAloc, [M:MNL_MSG C:error F: L: ]: PmtkSnd, [M:MNL_MSG C:error F: L: ]: PtkAloc, [M:MNL_MSG C:error F: L: ]: PmtkSnd, [M:MNL_MSG C:error F: L: ]: PtkAloc, [M:MNL_MSG C:error F: L: ]: PmtkSnd, [M:MNL_MSG C:error F: L: ]: PtkAloc, [M:MNL_MSG C:error F: L: ]: PmtkSnd, [M:MNL_MSG C:error F: L: ]: PtkAloc, [M:MNL_MSG C:error F: L: ]: PmtkSnd, [M:MNL_MSG C:error F: L: ]: PtkAloc, [M:MNL_MSG C:error F: L: ]: PmtkSnd, [M:MNL_MSG C:error F: L: ]: PtkAloc, [M:MNL_MSG C:error F: L: ]: PmtkSnd, [M:MNL_MSG C:error F: L: ]: PtkAloc, [M:MNL_MSG C:error F: L: ]: PmtkSnd, [M:MNL_MSG C:error F: L: ]: set_NMEA_format,%d [M:MNL_MSG C:error F: L: ]: set_NMEA_format_fail,%d [M:MNL_MSG C:debug F: L: ]: dsp,handle_dsp_async_rtun(%d) req_id(%d) [M:MNL_MSG C:warning F: L: ]: dsp,read unexpected REG addr(0x%x%x!=0x%x)@L1 [M:MNL_MSG C:warning F: L: ]: dsp,read unexpected REG addr(0x%x%x!=0x%x)@L5 [M:MNL_MSG C:error F: L: ]: dsp,handle async_req(%d) failed(%d) [M:MNL_MSG C:warning F: L: ]: dsp,ignore, the same async_req(%d@%d) [M:MNL_COM C:debug F: L: ]: RUTN,add_async_req(%d,%d),pos=%d [M:MNL_MSG C:error F: L: ]: dsp,async_req(%d) full(%d) [M:MNL_GPS C:error F: L: ]: OutOfCh2,%d,%d [M:MNL_GPS C:info F: L: ]: FrstDtt2,%02X,%02x [M:MNL_GPS C:error F: L: ]: UdefDtH2,%d,%02X,%d [M:MNL_GPS C:error F: L: ]: UdefCmd,%d,%02X [M:MNL_GPS C:error F: L: ]: ,HBD,ERR,Cmd,%d,DataSize,%d,MaxSize,%d
 [M:MNL_COM C:debug F: L: ]: BryEPkt,%d [M:MNL_COM C:debug F: L: ]: BryC2Bf,%d [M:MNL_GPS C:error F: L: ]: OutOfCh,%d,%d [M:MNL_GPS C:error F: L: ]: OutOfCh,HBD,ERR,OUT_OF_CH: Cmd,%d,CH,%d
 [M:MNL_GPS C:info F: L: ]: FrstDtt,%02X,%02x [M:MNL_GPS C:error F: L: ]: UdefDtH,%d,%02X [M:MNL_GPS C:error F: L: ]: UdefCmd,%d,%02X [M:MNL_GPS C:error F: L: ]: ,HBD,ERR,Cmd,%d,DataSize,%d,MaxSize,%d
 [M:MNL_COM C:error F: L: ]: GtDtTFl,%d [M:MNL_BOT C:debug F: L: ]: BotUrV2,BINFO(): stage 0 [M:MNL_BOT C:info F: L: ]: BotUrV2,BINFO(): x,%d,y,%x,s,%d [M:MNL_BOT C:info F: L: ]: RF_BOOT,RF Gain:%d / %d [M:MNL_BOT C:info F: L: ]: BW_BOOT,RF BW:%d / OP:%d [M:MNL_BOT C:info F: L: ]: BW_BOOT,RF BW:%d / OP:%d [M:MNL_BOT C:debug F: L: ]: BotUrV2,_BootDSP_UART_V2(): code_size,%d,start_addr,%d,exec_addr,%d,cipher_key,%d,code_frag_num,%d [M:MNL_BOT C:debug F: L: ]: BotUrV2,_BootDSP_UART_V2(): stage 1 [M:MNL_BOT C:info F: L: ]: BotUrV2,RFG,%d,%d,%X,%d,%d,%d,%d [M:MNL_COM C:warning F: L: ]: RUTN,Host_Based_Query_DSP_Version, faild, L1 not ready [M:MNL_COM C:debug F: L: ]: RUTN,Host_Based_Query_DSP_Version [M:MNL_COM C:warning F: L: ]: RUTN,Host_Based_Query_DSP_Version, faild, L5 not ready [M:MNL_COM C:debug F: L: ]: RUTN,Host_Based_Query_DSP2_Version [M:MNL_MSG C:debug F: L: ]: MCUB,Host_Based_handle_MCUB_data, len=%d, 0x%x,0x%x,0x%x,0x%x,0x%x,0x%x,0x%x,0x%x,0x%x [M:MNL_MSG C:debug F: L: ]: MCUB,Host_Based_l5_handle_MCUB_data, len=%d, 0x%x,0x%x,0x%x,0x%x,0x%x,0x%x,0x%x,0x%x,0x%x [M:mnl C:info F: L: ]: NVRAM,mnl_navigation_data_save_to_file_start:UpdtNVS [M:mnl C:info F: L: ]: NVRAM,mnl_navigation_data_save_to_file_end:UpdtNVE [M:MNL_GPS C:info F: L: ]: FTModeCmd,FT_Mode,%d,Send_Comd [M:MNL_GPS C:info F: L: ]: FTModeCmd,FT_Mode,%d,Send_Comd [M:MNL_GPS C:info F: L: ]: EpoLoad,HOST_EPO,BAD_Load,%x,%u,%u [M:MNL_AGP C:info F: L: ]: AGPSMGN,self PMTK001,353,3,%d,%d,%d,%d [M:MNL_AGP C:info F: L: ]: AGPSMGN,pSM [M:MNL_AGP C:info F: L: ]: AGPSMGN,pSm [M:MNL_AGP C:info F: L: ]: AGPSMGN,pV [M:MNL_ALO C:info F: L: ]: AlcInit, [M:MNL_ALO C:info F: L: ]: AlcCfgP,fgAL_Always_Locate_Center_Test_Proc type = 0x%x [M:MNL_ALO C:info F: L: ]: AlcCfgP,%d,%d,%d,%d,%d [M:MNL_ALO C:warning F: L: ]: 1st-Set,%d,%d,%d [M:MNL_ALO C:info F: L: ]: PMCmod1,%d [M:MNL_ALO C:info F: L: ]: NormTyp, [M:MNL_ALO C:debug F: L: ]: PMCmod2,%d [M:MNL_ALO C:info F: L: ]: PrdType,%d [M:MNL_ALO C:error F: L: ]: BadType,%d [M:MNL_ALO C:error F: L: ]: Bad-Cfg,%d,%d,%d,%d,%d [M:MNL_ALO C:info F: L: ]: DeeCfgX,%d,%d,%d,%d [M:MNL_ALO C:warning F: L: ]: PvtAdrX,%d [M:MNL_ALO C:warning F: L: ]: PvtWpXX,%d,%d [M:MNL_ALO C:warning F: L: ]: OldPosA,%d [M:MNL_ALO C:debug F: L: ]: NoPvtDb,Get_Idx_No_DB [M:MNL_ALO C:error F: L: ]: EdInitN, [M:MNL_ALO C:info F: L: ]: EdInitD, [M:MNL_ALO C:error F: L: ]: NuStUpX, [M:MNL_ALO C:info F: L: ]: NuStUpt,%d,%d,%d [M:MNL_ALO C:error F: L: ]: EnDtXMs, [M:MNL_ALO C:error F: L: ]: EnDtXEd, [M:MNL_ALO C:error F: L: ]: EnDtXSt, [M:MNL_ALO C:error F: L: ]: EnDtXSd, [M:MNL_ALO C:error F: L: ]: EnDtXNs, [M:MNL_ALO C:warning F: L: ]: EdTimBk,%d,%d [M:MNL_ALO C:info F: L: ]: UpEdHst,HistoryUpt,After,sec,%d,nw,%d,nr,%d [M:MNL_ALO C:debug F: L: ]: EdHsUpt, [M:MNL_ALO C:info F: L: ]: ED-TowS,%d [M:MNL_ALO C:info F: L: ]: EDState,%d,%u,%u,%u,%u,%u,%u,%d [M:MNL_ALO C:info F: L: ]: ED--s2s, [M:MNL_ALO C:info F: L: ]: EDStat2,%d,%u,%u,%u,%u [M:MNL_ALO C:info F: L: ]: EdStaRM, [M:MNL_ALO C:error F: L: ]: EdHistX, [M:MNL_ALO C:info F: L: ]: EdHsCK2,State Change to No Fix [M:MNL_ALO C:info F: L: ]: EdHsCK3,State Change to %d [M:MNL_ALO C:info F: L: ]: EdHsCK1,[%d]too less total count [M:MNL_ALO C:info F: L: ]: EdCkNFx,No Fix for 5 states [M:MNL_ALO C:warning F: L: ]: EdSumFL, [M:MNL_ALO C:info F: L: ]: EPHext,%u,%u,%u,[%u,%u] [M:MNL_ALO C:info F: L: ]: SV_for_Fix,%d,%d [M:MNL_ALO C:info F: L: ]: Chk_p_ext,%d,%d,%d [M:MNL_ALO C:info F: L: ]: FixCondition,%d,%d,%d [M:MNL_ALO C:info F: L: ]: P_EXT_Stop,%d [M:MNL_ALO C:error F: L: ]: CduPtrX,%X,%X [M:MNL_ALO C:error F: L: ]: CpiPtrX,%X [M:MNL_ALO C:error F: L: ]: CpuPtrX,%X,%X [M:MNL_ALO C:info F: L: ]: CdmPvtU, [M:MNL_ALO C:error F: L: ]: CdmBadP, [M:MNL_ALO C:error F: L: ]: PmcWkLX,%d [M:MNL_ALO C:info F: L: ]: PmcStpL,%d, %d [M:MNL_ALO C:error F: L: ]: PmcStpX,%d [M:MNL_ALO C:info F: L: ]: PmcInit, [M:MNL_ALO C:error F: L: ]: PcInPtX, [M:MNL_ALO C:info F: L: ]: PmcUint, [M:MNL_ALO C:debug F: L: ]: PcCfPtX,fgAL_Periodic_Mode_Controller_Uninit , free %d messages  [M:MNL_ALO C:error F: L: ]: PcCfPtX, [M:MNL_ALO C:info F: L: ]: PmcRunT,%d [M:MNL_ALO C:error F: L: ]: PcDPtrX, [M:MNL_ALO C:warning F: L: ]: NormalM, [M:MNL_LOP C:debug F: L: ]: TerTest,fgAL_Periodic_Mode_Controller, dsp start to run, transfer message to MNL, num=%d [M:MNL_ALO C:error F: L: ]: Sleep_T,failed to send msg to PNT [M:MNL_LOP C:debug F: L: ]: TerTest,fgAL_Periodic_Mode_Controller, transfer the %dth message(0x%x) to MNL  [M:MNL_LOP C:debug F: L: ]: TerTest,fgAL_PMC_Check_Periodic_Run_State_is_Off, g_AL_CENTER_DB.DbPerRun.fgOff=%d [M:MNL_MEM C:error F: L: ]: DgAloc2,fgAL_Periodic_Mode_buff_msg, no size to buffer message when DSP is sleep [M:MNL_LOP C:debug F: L: ]: TerTest,fgAL_Periodic_Mode_buff_msg, num = %d, next to wakeup dsp, msg_addr=0x%x [M:MNL_ALO C:warning F: L: ]: RtaBlok,%u,%u,%d,%u [M:MNL_ALO C:info F: L: ]: RtaInf1,%u,%d,%u,%u,%u,%d,%d [M:MNL_ALO C:debug F: L: ]: RtaInf2,%x,%u,%u,%u,%u,%u,%u,%u,%u,%u,%u,%u,%u,%u,%u,%u,%u,%u [M:MNL_ALO C:info F: L: ]: RtaInf3,%d,%d,%d,%d,%u,%u,%u,%d,%d [M:MNL_ALO C:debug F: L: ]: RtaInf4,%d,%d,%d,%d,%d,%d,%d,%d,%u,%u,%u,%u,%u,%u [M:MNL_ALO C:info F: L: ]: AlSCIni, [M:MNL_ALO C:error F: L: ]: SDbPtrX, [M:MNL_ALO C:info F: L: ]: PMCmod4,%d [M:MNL_ALO C:error F: L: ]: StDBPtX, [M:MNL_ALO C:info F: L: ]: AlSCCfg,%d,%d,%d,%d,%d [M:MNL_ALO C:info F: L: ]: AlSCCfg,Stop State Change [M:MNL_ALO C:info F: L: ]: AlSCbkN, [M:MNL_ALO C:error F: L: ]: SDbPtrX,Back_to_Normal [M:MNL_ALO C:error F: L: ]: VtCfgMx,%u [M:MNL_ALO C:info F: L: ]: VtCfgDb,%u,%u,%u [M:MNL_ALO C:warning F: L: ]: VtSBmp0, [M:MNL_ALO C:info F: L: ]: VtBtMap,%d,%d [M:MNL_ALO C:error F: L: ]: VtMgTxF, [M:MNL_ALO C:info F: L: ]: VtSMgTx, [M:MNL_ALO C:info F: L: ]: VtAldyS, [M:MNL_ALO C:warning F: L: ]: VtTSmp0, [M:MNL_ALO C:info F: L: ]: Vt-Bmap,%d,%d [M:MNL_ALO C:info F: L: ]: Vt--Dis, [M:MNL_ALO C:info F: L: ]: VtClrAb, [M:MNL_ALO C:debug F: L: ]: VtStop1,Stop one vtime, new bit map is: %d [M:MNL_ALO C:info F: L: ]: VtStatX,%d,%d [M:MNL_ALO C:error F: L: ]: VtMgSdF,send vtmsg fail [M:MNL_ALO C:warning F: L: ]: VtSetAb, [M:MNL_ALO C:warning F: L: ]: VtBacNm, [M:MNL_ALO C:error F: L: ]: VtTikOv,%u,%u,%u [M:MNL_ALO C:info F: L: ]: VtTikBk,%u,%u [M:MNL_ALO C:debug F: L: ]: sw_test,u4DeltaTick=%u,u4NewTick=%u,u4OldTick=%u [M:MNL_COM C:error F: L: ]: AddQFal,%d [M:MNL_MSG C:debug F: L: ]: BiPktMn,0xDE+0xDF scaned, pCur=0x%x,pRewrite=0x%x [M:MNL_MSG C:debug F: L: ]: BiPktMn,0xDE+0xE0 scaned, pCur=0x%x,pRewrite=0x%x [M:MNL_MSG C:debug F: L: ]: BiPktMn,while done,start(0x%x,0x%x) scan=%d,*pCur=0x%x,*pSize=0x%x [M:MNL_MSG C:debug F: L: ]: BiPktMn,pck ready, scan=%d,handle_len=%d [M:MNL_MSG C:debug F: L: ]: BiPktMn,DSP_Binary_Ctl,res=%d, dsp_data(%d),_u2DSP_BOOT_STATE=%d [M:MNL_MSG C:debug F: L: ]: BiPktMn,DSP_Binary_Ctl, th%d,pkg.size(%d).cksum(0x%x), offsize(%d), rest(%d)=%d [M:MNL_MSG C:error F: L: ]: BiPktMn,DSP_Binary_Ctl, package size(%d) > Max [M:MNL_MSG C:error F: L: ]: BiPktMn,DSP_Binary_Ctl, failed checksum(0x%x != 0x%x) [M:MNL_MSG C:error F: L: ]: BiPktMn,failed to decode msg_id(%d),cnt(%d) [M:MNL_MSG C:debug F: L: ]: BiPktMn,DSP_Binary_Ctl,decode %d dsp msgs, [M:MNL_MSG C:debug F: L: ]: BiPktMn,DSP_Binary_Ctl_l5,res=%d, dsp_data(%d),_u2DSP_BOOT_STATE=%d [M:MNL_MSG C:debug F: L: ]: BiPktMn,DSP_Binary_Ctl_l5, th%d,pkg.size(%d).cksum(0x%x), offsize(%d), rest(%d)=%d [M:MNL_MSG C:error F: L: ]: BiPktMn,DSP_Binary_Ctl_l5, package size(%d) > Max [M:MNL_MSG C:error F: L: ]: BiPktMn,DSP_Binary_Ctl_l5, failed checksum(0x%x != 0x%x) [M:MNL_MSG C:error F: L: ]: BiPktMn,l5 failed to decode msg_id(%d),cnt(%d) [M:MNL_MSG C:info F: L: ]: RtcmInX,Differnet Station id [M:MNL_MSG C:info F: L: ]: RtcmInX,Differnet Station id by check MSM header [M:MNL_GPS C:error F: L: ]: RtcmInX,Alloc memory fail! [M:MNL_GPS C:error F: L: ]: RtcmInX,Alloc memory fail! [M:MNL_MSG C:error F: L: ]: RtcmInX,Malloc fail ! [M:MNL_MSG C:error F: L: ]: RtcmInX,Malloc fail ! [M:MNL_MSG C:error F: L: ]: RtcmInX,Malloc fail ! [M:MNL_MSG C:info F: L: ]: RtcmInX,Differnet Station id by MsgID 1005, ID:%d [M:MNL_MSG C:info F: L: ]: RtcmInX,Differnet Station id by MsgID 1006, ID:%d [M:MNL_GPS C:error F: L: ]: RtcmEnc,Data length > 1kB [M:MNL_GPS C:error F: L: ]: RtcmEnc,Encode para error [M:MNL_MSG C:error F: L: ]: RtcmEnc,Wrong data alloc for MSM7 payload [M:MNL_MSG C:error F: L: ]: RtcmEnc,Wrong data alloc [M:MNL_MSG C:error F: L: ]: RtcmEnc,Wrong data alloc for MSM4 payload [M:MNL_MSG C:error F: L: ]: RtcmEnc,Wrong data alloc [M:MNL_GPS C:debug F: L: ]: RtcmEnc,In rtcm3_encode_handle, type: %d [M:MNL_DGP C:debug F: L: ]: RTCM2,=== Message 1 Start === wn: %u, tow_ms: %u [M:MNL_DGP C:debug F: L: ]: RTCM2,fact:%d,udre:%d,prn:%d,prc:%d,rrc:%d,iod:%d [M:MNL_DGP C:debug F: L: ]: RTCM2,Data decode rtcm2, type: %d, len: %d [M:MNL_DGP C:debug F: L: ]: RTCM2,=== Message 2 Start === wn: %u, tow_ms: %u [M:MNL_DGP C:debug F: L: ]: RTCM2,fact:%d,udre:%d,prn:%d,prc:%d,rrc:%d,iod:%d [M:MNL_DGP C:debug F: L: ]: RTCM2,Station id: %d [M:MNL_DGP C:debug F: L: ]: RTCM2,DGPS,Prn:%d,PrcOK:%d,RrcOK:%d,IOD:%d,%d [M:MNL_GPS C:warning F: L: ]: EpOvDu1,%d,%d [M:MNL_GPS C:warning F: L: ]: EpOvDu2,%d,%d [M:MNL_GPS C:warning F: L: ]: BeOvDu1,%d,%d [M:MNL_GPS C:warning F: L: ]: BeOvDu2,%d,%d [M:MNL_GPS C:warning F: L: ]: EpOvDu1,%d,%d [M:MNL_GPS C:warning F: L: ]: EpOvDu2,%d,%d [M:MNL_GPS C:warning F: L: ]: GAEpOvDu1,%d,%d [M:MNL_GPS C:warning F: L: ]: GAEpOvDu2,%d,%d [M:MNL_GPS C:warning F: L: ]: EpOvDu1,%d,%d [M:MNL_GPS C:warning F: L: ]: EpOvDu2,%d,%d [M:MNL_GPS C:warning F: L: ]: QZSSEpOvDu1,%d,%d [M:MNL_GPS C:warning F: L: ]: QZSSEpOvDu2,%d,%d [M:mnl C:error F: L: ]: NVRAM,mnl_navigation_data_invalid_after_load_from_file:NvLdDel [M:mnl C:info F: L: ]: NVRAM,mnl_navigation_data_from_file [M:mnl C:info F: L: ]: NVRAM,mnl_navigation_data_from_rtcram [M:mnl C:error F: L: ]: NVRAM,mnl_navigation_data_invalid_after_load_from_rtcram:NVCrcDl [M:mnl C:error F: L: ]: NVRAM,mnl_navigation_data_invalid_after_load_from_rtcram:NVvrsDl [M:MNL_TIM C:info F: L: ]: IdbtsDa,RTCstatusA,%d,fgSysTimeOK,%d,UseSystime,%d,LoadRTCUpdateOK,RTC_age,%d [M:MNL_TIM C:info F: L: ]: IdbtsDa,RTCstatusB,%d,fgSysTimeOK,%d,UseSystime,%d [M:MNL_TIM C:info F: L: ]: SdbtsF,RTC,TS_NOT_SET,Wn,%d,TS,%d, [M:MNL_TIM C:info F: L: ]: IdtsLdT,LoadNavT,RestartAge,%d [M:MNL_TIM C:debug F: L: ]: IdtsEnd, [M:MNL_MSG C:debug F: L: ]: MNLAPIL,mnl_remove_sv_by_blacklist %d [M:MNL_MSG C:info F: L: ]: AidBDION,EPO,%d,ion,%d,source,%d,a0,%d,a1,%d,a2,%d,a3,%d,b0,%d,b1,%d,b2,%d,b3,%d [M:MNL_MSG C:error F: L: ]: PrcPtkT, [M:MNL_GPS C:info F: L: ]: newEPOaid, clearBadEPO [M:MNL_MSG C:info F: L: ]: AidEPO,SV,%d,path_1NG_b [M:MNL_MSG C:info F: L: ]: AidEPO,SV,%d,hour,%d,clk,%d,orb,%d,ret,%d [M:MNL_MSG C:info F: L: ]: AidEPO,SV,%d,path_2NG,ValidEPO,%d,u4CurrGpsSecs,%d,u4GpsSecs,%d,seq,%d,NAseq,%d,Age_Epo,%d,Naage,%d,NaValidGEPO,%d,clk,%d,orb,%d [M:MNL_GPS C:info F: L: ]: newEPOaid, clearBadGLEPO [M:MNL_GPS C:info F: L: ]: newGLEPOaid, clearBadGLEPO [M:MNL_MSG C:info F: L: ]: AidEPO,SV,%d,path_1NG_b [M:MNL_MSG C:info F: L: ]: AidGLEPO,SV,%d,hour,%d,clk,%d,orb,%d,ret,%d [M:MNL_MSG C:info F: L: ]: AidGLEPO,SV,%d,path_2NG,ValidEPO,%d,u4CurrGpsSecs,%d,u4GpsSecs,%d,seq,%d,NAseq,%d,Age_Epo,%d,Naage,%d,NaValidGEPO,%d,clk,%d,orb,%d [M:MNL_MSG C:debug F: L: ]: AidEPO_C,SV,%d,BadEPOSV,%X,%X,Noused,%X,%X,seq,%d,age,%d,u4GpsSecs,%d,BadEPOGpsHour,%d [M:MNL_MSG C:info F: L: ]: AidBDEPO,SV,%d,hour,%d,clk,%d,orb,%d,ret,%d [M:MNL_MSG C:info F: L: ]: AidBDEPO,SV,%d,path_2NG,ValidEPO,%d,u4CurrGpsSecs,%d,u4GpsSecs,%d,seq,%d,NAseq,%d,Age_Epo,%d,Naage,%d,NaValidGEPO,%d,clk,%d,orb,%d [M:MNL_MSG C:debug F: L: ]: AidEPO_E,SV,%d,BadEPOSV,%X,%X,Noused,%X,%X,seq,%d,age,%d,u4GpsSecs,%d,BadEPOGpsHour,%d [M:MNL_MSG C:info F: L: ]: AidGAPO,SV,%d,hour,%d,clk,%d,orb,%d,ret,%d [M:MNL_MSG C:info F: L: ]: AidGAEPO,SV,%d,path_2NG,ValidEPO,%d,u4CurrGpsSecs,%d,u4GpsSecs,%d,seq,%d,NAseq,%d,Age_Epo,%d,Naage,%d,NaValidGEPO,%d,clk,%d,orb,%d [M:MNL_GPS C:info F: L: ]: newQZSSEPOaid, clearBadQZSSEPO [M:MNL_GPS C:info F: L: ]: newQZSSEPOaid, clearBadQZSSEPO [M:MNL_MSG C:info F: L: ]: AidQZSSEPO,SV,%d,hour,%d,clk,%d,orb,%d,ret,%d [M:MNL_MSG C:info F: L: ]: AidQZSSEPO,SV,%d,path_2NG,ValidEPO,%d,u4CurrGpsSecs,%d,u4GpsSecs,%d,seq,%d,NAseq,%d,Age_Epo,%d,Naage,%d,NaValidGEPO,%d,clk,%d,orb,%d [M:MNL_GPS C:debug F: L: ]: SgmFstS,Adjust_AAD,TOW_FS_Check,%d [M:MNL_GPS C:error F: L: ]: LSV,[ADPATIVE] vPatch_Sat_do_0_6v_check, can't covert visible SV(SV_ID=%d)  [M:MNL_GPS C:debug F: L: ]: LSV,[ADPATIVE] vPatch_Sat_do_0_6v_check, visi_tracked_num =%d (glo:%d), untrack=%d(non-glo:%d), num_dyn_vis=%d  [M:MNL_GPS C:warning F: L: ]: LSV,[ADPATIVE] vPatch_Sat_limit_GAL, can't covert visible SV(SV_ID=%d)  [M:MNL_GPS C:warning F: L: ]: LSV,[ADPATIVE] vPatch_Sat_limit_GAL, can't covert visible SV(SV_ID=%d)  [M:MNL_GPS C:debug F: L: ]: LSV,[ADPATIVE] vPatch_Sat_limit_GAL, num_dyn_may_vis=%d,num_dyn_vis=%d   [M:MNL_GPS C:debug F: L: ]: LSV,vPatch_Sat_limit_maybe_list(%d),num_dyn_vis =%d num_dyn_may_vis=%d,tracked_count=%d,should_limit_gal=%d,kept_gal_ch=%d,gal_trk=%d [M:MNL_GPS C:debug F: L: ]: LSV,[ADPATIVE] vPatch_Sat_limit_maybe_list, tracked_count=%d < %d) , num_dyn_may_vis=%d [M:MNL_GPS C:debug F: L: ]: LSV,[ADPATIVE] vPatch_Sat_limit_maybe_list(Tick=%d), tracked_count=%d,GLO_tracked=%d, num_dyn_may_vis=%d, num_dyn_vis=%d [M:MNL_GPS C:warning F: L: ]: LSV,[ADPATIVE] vPatch_Sat_limit_maybe_list, can't covert visible SV(SV_ID=%d)  [M:MNL_GPS C:debug F: L: ]: LSV,[ADPATIVE] vPatch_Sat_limit_maybe_list, keep GLONASS in visible list, PRN=%d,SVID=%d) [M:MNL_GPS C:debug F: L: ]: LSV,[ADPATIVE] vPatch_Sat_limit_maybe_list, visible_tracked_sv_num=%d),gal_sv_tracked_num(%d) [M:MNL_GPS C:warning F: L: ]: LSV,[ADPATIVE] vPatch_Sat_limit_maybe_list, can't covert SV(SV_ID=%d)  [M:MNL_GPS C:debug F: L: ]: LSV,[ADPATIVE] vPatch_Sat_limit_maybe_list, return *num_dyn_may_vis=%d) [M:MNL_GPS C:error F: L: ]: LSV,patch_sat_re_combine, return , since no untracked SV) [M:MNL_GPS C:debug F: L: ]: LSV,patch_sat_re_combine, un-classified vis SV(%d[%d],%d,%d)) [M:MNL_GPS C:debug F: L: ]: LSV,patch_sat_re_combine, gq_to_add(%d), gal_to_add(%d),bd_to_add(%d),untrack_num(%d) [M:MNL_GPS C:debug F: L: ]: LSV,patch_sat_re_combine, untracked_sv_list[i]=%d, bd_last=%d,gal_last=%d,gq_last=%d [M:MNL_GPS C:error F: L: ]: LSV,patch_sat_re_combine, unexpected error, no empty to add GQ SV) [M:MNL_GPS C:error F: L: ]: LSV,patch_sat_re_combine, unexpected error, no empty to add GAL SV) [M:MNL_GPS C:error F: L: ]: LSV,patch_sat_re_combine, unexpected error, no empty to add BD SV) [M:MNL_GPS C:debug F: L: ]: LSV,remove_reserved_vis_ch, rm=%d,k_glo=%d,vis_len=%d [M:MNL_GPS C:debug F: L: ]: LSV,remove_reserved_vis_ch, gal_num=%d,bd_num=%d,gq_num=%d,glo_num=%d [M:MNL_GPS C:debug F: L: ]: LSV,remove_reserved_vis_ch, ret len=%d [M:MNL_GPS C:debug F: L: ]: LSV,patch_sat_keep_gal_bd_in_maybe,TRK SV(%d),t=%d, P=%d [M:MNL_GPS C:debug F: L: ]: LSV,patch_sat_keep_gal_bd_in_maybe,TRK SV(%d),t=%d, P=%d [M:MNL_GPS C:debug F: L: ]: LSV,patch_sat_keep_gal_bd_in_maybe,trked SVs num = %d [M:MNL_GPS C:debug F: L: ]: LSV,[ADPATIVE] patch_sat_keep_gal_bd_in_maybe, return *num_dyn_may_vis=%d) [M:MNL_GPS C:warning F: L: ]: LSV,[ADPATIVE] patch_keep_gps_glo, can't covert visible SV(SV_ID=%d)  [M:MNL_GPS C:warning F: L: ]: LSV,[ADPATIVE] patch_keep_gps_glo, can't covert visible SV(SV_ID=%d)  [M:MNL_GPS C:debug F: L: ]: LSV,[ADPATIVE] patch_keep_gps_glo, num_dyn_may_vis=%d,num_dyn_vis=%d   [M:MNL_GPS C:debug F: L: ]: LSV,v06x_Patch_swimming_2_Sats, temp_i=%d, *num_dyn_vis=%d,num_dyn_may_vis=%d [M:MNL_GPS C:debug F: L: ]: LSV,to_add=%d, maybe: g=%d,b_h=%d,b_l=%d,q=%d,g_e=%d [M:MNL_GPS C:debug F: L: ]: LSV,v06x_Patch_swimming_2_Sats, ret, vis_num=%d, may_n=%d [M:MNL_GPS C:debug F: L: ]: LSV,v06x_Patch_sat_list(sm=%d),num_dyn_vis=%d,num_used_sv=%d,_u1PowerSavTrkNum=%d,KFsd->FixTime=%d, v0_6_check=%d,Pos_FixType=%d,fixTime=%d,fixRate=%d [M:MNL_MSG C:debug F: L: ]: LSV,non tracked SV in visi_list,SVid=%d, SV_Type =%d,PRN=%d, untrked_count= %d,index_0_6v_bk=%d [M:MNL_MSG C:debug F: L: ]: LSV,vPatch_sat_list, SVid=%d, SV_type=%d, SV_PRN=%d,is_added=%d,in=%d,out=%d,new_num=%d,max_num=%d [M:MNL_GPS C:debug F: L: ]: LSV,vPatch_sat_list(TTick=%d,Search=0x%x), new_num_dyn_vis=%d,index_0_6v_bk=%d,should add %d [M:MNL_MSG C:debug F: L: ]: LSV,vPatch_sat_list, index_bk=%d,new_num_dyn_vis=%d,dyn_Vis_0_6v_bk[%d]=%d [M:MNL_MSG C:debug F: L: ]: LSV,vPatch_sat_list, replace GLO,new_num_dyn_vis=%d, glo_visible_record_index=%d,index_bk=%d,filled=%d [M:MNL_MSG C:debug F: L: ]: LSV,vPatch_sat_list, no extra visi_glo(%d) to replace or valided non-glo SV (%d-%d) [M:MNL_MSG C:debug F: L: ]: LSV,vPatch_sat_list, new_num_dyn_vis=%d, glo_visible_record_index=%d,index_0_6v_bk=%d,index_bk=%d [M:MNL_GPS C:error F: L: ]: DbgCtrl,Malloc fail [M:MNL_GPS C:debug F: L: ]: MNL_ADP,invoke restart : init_config_ut.freq=%d [M:MNL_MSG C:info F: L: ]: ClrXDSP,%d [M:MNL_COM C:info F: L: ]: DBTT_L5,%d [M:MNL_GPS C:info F: L: ]: L5TkDltIn,%d,%d,%d [M:MNL_GPS C:info F: L: ]: L5TkDltNo,%d,%d [M:MNL_GPS C:warning F: L: ]: L5TkGoBak,%d,%d [M:MNL_ALO C:error F: L: ]: RstKrlX, [M:MNL_ALO C:warning F: L: ]: RstKrlO, [M:MNL_COM C:info F: L: ]: DBTT_L1,%d [M:MNL_COM C:warning F: L: ]: DtNxtMM,%d,%d [M:MNL_GPS C:info F: L: ]: TkDltIn,%d,%d,%d [M:MNL_GPS C:info F: L: ]: TkDltNo,%d,%d [M:MNL_GPS C:warning F: L: ]: TkGoBak,%d,%d [M:MNL_ALO C:error F: L: ]: RstKrlX, [M:MNL_ALO C:warning F: L: ]: RstKrlO, [M:MNL_COM C:error F: L: ]: MsgSndF,L5 %x [M:MNL_MSG C:error F: L: ]: MsgAloc,L5 [M:MNL_COM C:error F: L: ]: MsgSndF,L5 %x [M:MNL_MSG C:error F: L: ]: MsgAloc,L5 [M:MNL_COM C:error F: L: ]: MsgSndF,error handle , L5 dbtt timeout %x [M:MNL_GPS C:error F: L: ]: HBD,vSendWaitDBTTMsg malloc fail [M:MNL_GPS C:error F: L: ]: HBD,vSendWaitDBTTMsg send msg fail [M:MNL_GPS C:error F: L: ]: HBD,gnss_al_dsp_on_timeout malloc fail [M:MNL_GPS C:error F: L: ]: HBD,gnss_al_dsp_on_timeout send a message, type=%d  [M:MNL_GPS C:error F: L: ]: HBD,gnss_al_dsp_on_timeout send msg fail [M:MNL_ALO C:debug F: L: ]: sw_test,vMain_Test_Func, sleep_time=%u,_u4SysSlpTime=%u,diff=%u [M:MNL_LOP C:error F: L: ]: GLP,Wrong DSP Sleep time,%d,NEXT:%u,Current:%d,Duty:%d [M:MNL_GPS C:error F: L: ]: SWPRT35,ERR,%d,%d, MODE,%d [M:MNL_GPS C:info F: L: ]: MNLVer,MTK_GPS_EVENT_POWER_OFF_DSP arrived, is_closed =%d  [M:MNL_COM C:warning F: L: ]: WtDtRty,%d [M:MNL_COM C:debug F: L: ]: WtDtCnt,wait DBTT Done, %d [M:MNL_ALO C:info F: L: ]: AlCfgDa,%d,%d,%d,%d,%d [M:MNL_ALO C:error F: L: ]: CfgVTGt, [M:MNL_ALO C:warning F: L: ]: BlockLn,%d [M:MNL_ALO C:warning F: L: ]: BlockEd, [M:MNL_BOT C:info F: L: ]: u4SwMode_inout,data,%d,u4SwMode,%d [M:MNL_BOT C:info F: L: ]: u4SwMode,SV_TYPE,%d,u4SwMode,%d [M:MNL_BOT C:info F: L: ]: u4SwMode,_u1MTKChipID,%d [M:MNL_BOT C:info F: L: ]: u4SwMode,Sys_Cfg.u1SearchGNSS,%d,_u1SearchGNSS,%d,SV_TYPE,%d,*u4SwMode,%d,chip,%d [M:MNL_ALO C:warning F: L: ]: RstKrnl, [M:MNL_MSG C:error F: L: ]: MPTEST,ERR [M:MNL_MSG C:error F: L: ]: LEAVMP,ERR [M:MNL_MSG C:info F: L: ]: CN0_mode,Enrty CN0 mode,%d [M:MNL_MSG C:info F: L: ]: CN0_mode,Exit CN0 mode [M:MNL_MSG C:info F: L: ]: CN0_mode,Exit CN0 mode but now is not in CW mode [M:MNL_MSG C:info F: L: ]: CN0_mode,CN0 mode fail [M:MNL_MSG C:info F: L: ]: CW_MOD,CW_DISABLE, Band %d [M:MNL_MSG C:info F: L: ]: CW_MOD,CW_ENABLE, Band %d [M:MNL_MSG C:error F: L: ]: CW_MOD,ERR [M:MNL_COM C:info F: L: ]: EmptThd,%d [M:MNL_MSG C:info F: L: ]: EnBeeSv, [M:MNL_MSG C:info F: L: ]: DisBeSv, [M:MNL_MSG C:info F: L: ]: EnBrdSv, [M:MNL_MSG C:info F: L: ]: DisBrSv, [M:MNL_MSG C:error F: L: ]: NMEAOu,NMEA,LEN,%d>256 [M:MNL_MSG C:error F: L: ]: SetRTC,ERR [M:MNL_BOT C:info F: L: ]: UInitCfg,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%x,%d,%d
 [M:MNL_BOT C:info F: L: ]: MNLop,GNSSOPMode,%d, _u1MTKChipID,%d,_fgGNSSOPMode,%d,_u1GNSSUsed,%d,sv_type_agps_set,%d [M:MNL_BOT C:info F: L: ]: MNLVer,_u1MTKChipID=%d [M:MNL_MSG C:info F: L: ]: L5InWrp,%d,%d [M:MNL_MSG C:error F: L: ]: PtkAloc, [M:MNL_MSG C:error F: L: ]: PmtkSnd, [M:MNL_LOP C:debug F: L: ]: TerTest,mnl_system_buff_dsp_message, MNL_MSG_SET_CONFIG, type(%d) = %d [M:MNL_LOP C:error F: L: ]: MNLAPI,No size to buffer message when DSP is Sleep/Stop/Off [M:MNL_GPS C:info F: L: ]: MNLAPIL,[MNL] running pl_mnl_startup_case1 [M:MNL_GPS C:info F: L: ]: MNLAPIL,Status (%d) [M:MNL_GPS C:error F: L: ]: MNLAPIL,[MNL] failed to mtk_gps_mnl_run [M:MNL_GPS C:info F: L: ]: MNLAPIL,[MNL] end pl_mnl_startup_case1 [M:MNL_GPS C:error F: L: ]: HBD,mnl_power_off malloc fail [M:MNL_GPS C:error F: L: ]: HBD,mnl_power_off send msg fail [M:MNL_BOT C:debug F: L: ]: BotMsg,dsp_transmiter_send_comd4_to_DSP: switch to DCXO_LDO [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set defined datum: majA:%d, ecc:%d, dX:%d, dY:%d, dZ:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set Initial latitude:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set Initial longitude:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,test jamming:Scantype:%d,ScanTestNum:%d,GlonassSubChnJamScan:%d,JamScanResolution:%d [M:MNL_BOT C:debug F: L: ]: BotMsg,dsp_transmiter_send_comd4_to_DSP: switch to DCXO_LDO [M:MNL_MSG C:warning F: L: ]: dsp,ignore a req to get dsp version since getting is in process [M:MNL_COM C:debug F: L: ]: RUTN,mnl_get_dsp_version(%d),g-%d [M:MNL_GPS C:error F: L: ]: MNLAPIL,warning, unexpected value, mnl_power_on_off_conf, res= %d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,mnl_power_on_off_conf = %d, _fgWaitForFirstDBTT = %d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set high acc:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set min snr:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set estimated num:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set output threshold:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set static threshold:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set high sensitivity:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set mode:%d,%d,%d,%d,%d,%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set elevation mask:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set navigation mode:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set HDOP threshold:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set datum mode:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set dgps mode:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set sbas status:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,slas status:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set sbas prn mode:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set sbas blacklist:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set sbas op mode:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set slas status:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL, sbas status:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set slas op mode:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set AIC status:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,test jamming detection message output:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set RTCM3 output mode: %d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set NVRAM auto save enable:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set QZSS enable:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set limit track num:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set adaptive L5 status:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set raw callback type:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set eph notify enable:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set bd geo enable:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,android enable:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,rtc frequency:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set system start wn:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set immediate speed mode:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set 2d_fix mode:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set slas op region:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set odometer function enable:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set acc type:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set wn estimate status:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set IC type:%d [M:MNL_GPS C:error F: L: ]: MNLAPIL,can't do ulp_enable because Periodic is on [M:MNL_GPS C:error F: L: ]: MNLAPIL,mnl_ulp_enable %d, res=%d [M:MNL_GPS C:error F: L: ]: DbgBinO,Binary encode para error: Message ID %d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,msg%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,Bit field:%X, SVID:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,Test Stop! [M:MNL_GPS C:error F: L: ]: MNLAPIL,Stop Fail! [M:MNL_GPS C:debug F: L: ]: MNLAPIL,MNL_MSG_SET_L5_ENABLE = %d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,geofencing GPIO polarity:%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,set fix rate success:%d [M:MNL_GPS C:error F: L: ]: MNLAPIL,MNL_MSG_LP_CLEAR_L5: error parameter(%d) [M:MNL_LOP C:error F: L: ]: MNLAPI,failed to send msg to PNT [M:MNL_GPS C:debug F: L: ]: MNLAPIL,mnl_is_l5_switchable,_u1SearchGNSS(0x%x), svtype(%02X),start_mode(%d) [M:MNL_DGP C:debug F: L: ]: RTCM,Data input, msg_type: %d, queue: %d [M:MNL_GPS C:error F: L: ]: DbgBinO,Output length large than MAX_LEN! [M:MNL_GPS C:error F: L: ]: DbgBinO,Binary encode para error: Message ID %d [M:MNL_GPS C:error F: L: ]: DbgBinO,EPCI may overflow, throw package, idx:%d, curr_len:%d [M:MNL_GPS C:error F: L: ]: DbgBinO,Binary encode para error: Message ID %d [M:MNL_GPS C:error F: L: ]: DbgBinO,EPCX may overflow, throw package, idx:%d, curr_len:%d [M:MNL_GPS C:error F: L: ]: DbgBinO,Binary encode para error: Message ID %d [M:MNL_GPS C:error F: L: ]: DbgBinO,EPCF may overflow, throw package, idx:%d, curr_len:%d [M:MNL_GPS C:error F: L: ]: DbgBinO,Binary encode flash error: Message ID %d [M:MNL_GPS C:error F: L: ]: DbgBinO,EPCFX may overflow, throw package, idx:%d, curr_len:%d [M:MNL_GPS C:error F: L: ]: DbgBinO,Binary encode flash error: Message ID %d [M:MNL_GPS C:error F: L: ]: DbgBinO,Lite DBWA Data may overflow, throw package, chid:%d, curr_len:%d [M:MNL_GPS C:error F: L: ]: DbgBinO,Binary encode para error: Message ID %d [M:MNL_GPS C:error F: L: ]: DbgBinO,Lite DBQZ Data may overflow, throw package, chid:%d, curr_len:%d [M:MNL_GPS C:error F: L: ]: DbgBinO,Binary encode para error: Message ID %d [M:MNL_GPS C:error F: L: ]: DbgBinO,Lite DBSF Data may overflow, throw package, chid:%d, curr_len:%d [M:MNL_GPS C:error F: L: ]: DbgBinO,Binary encode para error: Message ID %d [M:MNL_GPS C:error F: L: ]: DbgBinO,Lite DBGW Data may overflow, throw package, chid:%d, curr_len:%d [M:MNL_GPS C:error F: L: ]: DbgBinO,Binary encode para error: Message ID %d [M:MNL_GPS C:error F: L: ]: DbgBinO,Lite DBSTR Data may overflow, throw package, chid:%d, curr_len:%d [M:MNL_GPS C:error F: L: ]: DbgBinO,Binary encode para error: Message ID %d [M:MNL_GPS C:error F: L: ]: DbgBinO,Lite DBBD Data may overflow, throw package, chid:%d, curr_len:%d [M:MNL_GPS C:error F: L: ]: DbgBinO,Binary encode para error: Message ID %d [M:MNL_GPS C:error F: L: ]: DbgBinO,Lite DBD2 Data may overflow, throw package, chid:%d, curr_len:%d [M:MNL_GPS C:error F: L: ]: DbgBinO,Binary encode para error: Message ID %d [M:MNL_GPS C:error F: L: ]: DbgBinO,Lite DBS5 Data may overflow, throw package, chid:%d, curr_len:%d [M:MNL_GPS C:error F: L: ]: DbgBinO,Binary encode para error: Message ID %d [M:MNL_GPS C:error F: L: ]: DbgBinO,Lite DBG5 Data may overflow, throw package, chid:%d, curr_len:%d [M:MNL_GPS C:error F: L: ]: DbgBinO,Binary encode para error: Message ID %d [M:MNL_GPS C:error F: L: ]: DbgBinO,Lite DBB5 Data may overflow, throw package, chid:%d, curr_len:%d [M:MNL_GPS C:error F: L: ]: DbgBinO,Binary encode para error: Message ID %d [M:MNL_MSG C:debug F: L: ]: BinDbgL,NEXT: %u [M:MNL_MSG C:debug F: L: ]: BinDbgL,COMD%u [M:MNL_GPS C:error F: L: ]: DbgBinO,Lite DBRM Data may overflow, throw package, chid:%d, curr_len:%d [M:MNL_GPS C:error F: L: ]: DbgBinO,Binary encode para error: Message ID %d [M:MNL_GPS C:error F: L: ]: DbgBinO,Lite DBR5 Data may overflow, throw package, chid:%d, curr_len:%d [M:MNL_GPS C:error F: L: ]: DbgBinO,Binary encode para error: Message ID %d [M:MNL_GPS C:error F: L: ]: DbgBinO,EPCAF may overflow, throw package, idx:%d, curr_len:%d, sv:%d [M:MNL_GPS C:error F: L: ]: DbgBinO,Binary encode para error: Message ID %d [M:MNL_GPS C:error F: L: ]: DbgBinO,EPCFAF may overflow, throw package, idx:%d, curr_len:%d [M:MNL_GPS C:error F: L: ]: DbgBinO,Binary encode flash error: Message ID %d [M:MNL_GPS C:error F: L: ]: DbgBinO,Binary encode para error: Message ID %d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,dsp_manager_notify_dbtt_arrival(%d) , g_dsp_manager_working_state=%d, l1_dbtt(%d),l5_dbtt(%d) [M:MNL_GPS C:debug F: L: ]: MNLAPIL,dsp_manager_is_constellation_switchable , %d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,dsp_manager_set_start_mode , %d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,dsp_manager_set_l5_enable , can't do(%d), current state=%d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,dsp_manager_set_l5_enable , can't do(%d), current state=%d [M:MNL_GPS C:error F: L: ]: MNLAPIL,dsp_manager_start , can't start,current state = %d [M:MNL_GPS C:error F: L: ]: MNLAPIL,failed to download DSP bin, error = $d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,dsp_manager_start , g_gnss_dsp_start_mode=%d, dsp_number(%d) [M:MNL_GPS C:error F: L: ]: MNLAPIL,dsp_manager_stop , can't stop,current state = %d [M:MNL_GPS C:debug F: L: ]: MNLAPIL,dsp_manager_stop , g_gnss_dsp_start_mode=%d, g_dsp_manager_working_state=%d [M:MNL_GPS C:error F: L: ]: MNLAPIL,dsp_manager_stop , unexpeted g_dsp_manager_working_state(%d) [M:mnl C:debug F: L: ]: pl_task,mtk_gps_sys_gps_mnl_callback(%d)--, g_mnl_dbtt_start_count(%d) [M:mnl C:info F: L: ]: pl_task,mnl_gps_run_task()++ [M:mnl C:info F: L: ]: pl_task,mnl_gps_run_task()-- [M:mnl C:info F: L: ]: pl_task,mtk_gps_threads_create(%d)++ [M:mnl C:error F: L: ]: pl_task,mtk_gps_threads_create(%d)-- failed to create GPS_MNL_THREAD_MNL [M:mnl C:info F: L: ]: pl_task,mtk_gps_threads_create(%d)-- [M:mnl C:info F: L: ]: pl_task,mtk_gps_threads_release()++ [M:mnl C:info F: L: ]: pl_task,mtk_gps_threads_release()-- [M:mnl C:info F: L: ]: pl_task,mnl_sys_linkit_gps_run()++ [M:mnl C:error F: L: ]: pl_task,mnl_sys_linkit_gps_run()-- failed GPS_MNL_THREAD_MNL [M:mnl C:info F: L: ]: pl_task,mnl_sys_linkit_gps_run()-- [M:mnl C:debug F: L: ]: pl_task,mnl_epoc_msg_handler:%d [M:mnl C:info F: L: ]: pl_task,mnl_epoc_run_task [M:mnl C:info F: L: ]: pl_task,vEPOC_Init [M:mnl C:info F: L: ]: pl_task,mtk_gps_epoc_run()++ [M:mnl C:info F: L: ]: pl_task,mtk_gps_epoc_run()-- [M:mnl C:info F: L: ]: pl_task,mtk_gps_EPOC_run:%d,%d [M:mnl C:info F: L: ]: pl_task,mtk_gps_epoc_run()-- [M:mnl C:error F: L: ]: PortLyr,[MNL] failed to mtk_gps_sys_msg_send [M:mnl C:error F: L: ]: PortLyr,[MNL] failed to notify dsp data ready [M:mnl C:error F: L: ]: PortLyr,[MNL] failed to notify dsp data ready [M:mnl C:info F: L: ]: PortLyr,[MNL] mtk_gps_sys_msg_queue_deinit, g_mnl_queueHandle = 0x%x [M:mnl C:info F: L: ]: PortLyr,[MNL] mtk_gps_sys_msg_queue_init, g_mnl_queueHandle = 0x%x [M:mnl C:info F: L: ]: PortLyr,[MNL] mtk_gps_sys_create_mutex index %d! [M:mnl C:info F: L: ]: MnlDvfs,Init freq, Target:%d,DSP:%d [M:mnl C:warning F: L: ]: PortLyr,L1-HTL: DSP L1 is not ready, miss data %d bytes [M:hal C:debug F: L: ]: apb_l1_mcu_mode_no_data_isr()++ GPS_APB_STA=0x%08x [M:hal C:debug F: L: ]: apb_l1_mcu_mode_no_data_isr()-- GPS_APB_STA=0x%08x [M:hal C:debug F: L: ]: apb_l5_mcu_mode_no_data_isr()++ GPS_APB_STA=0x%08x [M:hal C:debug F: L: ]: apb_l5_mcu_mode_no_data_isr()-- GPS_APB_STA=0x%08x [M:hal C:debug F: L: ]: apb_l1_mcu_mode_data_isr()++ GPS_APB_STA=0x%08x,l1 indicator=%d,mcub=0x%x [M:hal C:debug F: L: ]: apb_l1_mcu_mode_data_isr() empty, GPS_APB_STA=0x%x,len=%d,l1 indicator=%d,mcub=0x%x,w_count=%d,start=0x%x [M:hal C:warning F: L: ]: apb_l1_mcu_mode_data_isr() do scan all: len=%d,l1 indicator=%d,mcub=0x%x,w_count=%d,data=0x%x [M:hal C:debug F: L: ]: apb_l1_mcu_mode_data_isr()--2 GPS_APB_STA=0x%08x,w_count=%d,d2af=0x%x [M:hal C:debug F: L: ]: apb_l5_mcu_mode_data_isr()++ GPS_APB_STA=0x%08x,l5 indicator=%d,mcub=0x%x [M:hal C:debug F: L: ]: apb_l5_mcu_mode_data_isr() empty, GPS_APB_STA=0x%x,len=%d,l5 indicator=%d,mcub=0x%x,w_count=%d,start=0x%x [M:hal C:debug F: L: ]: apb_l5_mcu_mode_data_isr()--2 GPS_APB_STA=0x%08x [M:hal C:warning F: L: ]: mcub_l1_get_raw_data, type = 0 [M:hal C:warning F: L: ]: mcub_l1_get_raw_data, no end flag [M:hal C:warning F: L: ]: mcub_l5_get_raw_data, type = 0 [M:hal C:warning F: L: ]: mcub_l5_get_raw_data, no end flag [M:hal C:debug F: L: ]: pdma_lock_reference_init L1R=%d is_alive=%d, L1T=%d , L5R=%d, L5T=%d [M:hal C:error F: L: ]: pdma_lock_reference_add sleep handle is invalid [M:hal C:debug F: L: ]: ####GPS HAS DATA EVENT RECEIVED! start DMA Recv L1,apb_sta = 0x%x,is_emp=%d,in =%d,last miss=%d #### [M:hal C:error F: L: ]: pdma_lock_reference_reduce sleep handle is invalid [M:hal C:debug F: L: ]: ####fatal error :  DSP L1 DMA RX is full(len=%d) !#### [M:hal C:debug F: L: ]: ####GPS NO DATA EVENT RECEIVED, dma state = %d , apb_sta=0x%x, len=%d, en_rcv=%d, status=%d!#### [M:hal C:debug F: L: ]: apb_dma_mode_no_data_isr_l1() dma state = %d , apb_sta=0x%x, len=%d, en_rcv=%d !#### [M:hal C:debug F: L: ]: GPS HAS DATA EVENT RECEIVED! start DMA Recv L5 , apb_sta = %x [M:hal C:debug F: L: ]: ####CM4 DMA TX start for DSP L1 ! len=%d,addr=0x%x#### [M:hal C:debug F: L: ]: ####CM4 DMA TX start for DSP L5 !#### [M:hal C:debug F: L: ]: CM4 DMA RX EVENT RECEIVED for DSP L5 ! [M:hal C:debug F: L: ]: GPS NO DATA EVENT RECEIVED L5, dma state = %d, recv_len = %d apb_sta = %x [M:hal C:debug F: L: ]: apb_dma_mode_no_data_isr_l5()  dma state = %d, recv_len = %d apb_sta = %x [M:hal C:debug F: L: ]: pdma_tx_stop :  DSP_NO=%d  [M:hal C:debug F: L: ]: ####CM4 DMA TX EVENT RECEIVED for DSP L1 !#### [M:hal C:debug F: L: ]: CM4 DMA TX EVENT RECEIVED for DSP L5! [M:hal C:debug F: L: ]: apb_get_l1_data() pdma_rx_buff_len_l1=%d l1_data_in=%d [M:hal C:debug F: L: ]: apb_dma_mode_deinit_l1  ! [M:hal C:debug F: L: ]: apb_get_l5_data(%d), enable L5 data, l5_data_in %d->1 [M:hal C:debug F: L: ]: A2DF flag3 is 1, waiting flag3 = 0 [M:hal C:debug F: L: ]: L1 set A2DF flag3, it's about %d us from power on [M:hal C:debug F: L: ]: A2DF flag3 is 0, waiting D2AF flag3 = 1 [M:hal C:debug F: L: ]: dsp set D2AF flag 3 = 1 [M:hal C:debug F: L: ]: L5 set A2DF flag3, it's about %d us from power on [M:hal C:debug F: L: ]: L5 dsp set D2AF flag 3 = 1 [M:hal C:debug F: L: ]: before setting mcub , l5_ready=%d [M:hal C:debug F: L: ]: L1&L5 mcub transmission done, pre_l5_ready=%d [M:hal C:error F: L: ]: gnss_l1_config_power_on: unexpected cpu feq =%d [M:hal C:error F: L: ]: gnss_l5_config_power_on: unexpected cpu feq =%d [M:hal C:debug F: L: ]: gnss_drv_dsp_enable, HAL_CLOCK_CG_GPSADC_L1 [M:hal C:debug F: L: ]: gnss_drv_dsp_enable, HAL_CLOCK_CG_GPSADC_L1 [M:hal C:debug F: L: ]: gnss_drv_dsp_enable, L5 mtcmos on [M:hal C:debug F: L: ]: gnss_drv_dsp_enable, L1 mtcmos on [M:hal C:error F: L: ]: parameter error in gnss_drv_dsp_set_next_state_of_poweron [M:hal C:debug F: L: ]: create L1 event [M:hal C:debug F: L: ]: create L5 event [M:hal C:debug F: L: ]: gnss_drv_dsp_recv: DSP(%d), event arrived [M:hal C:error F: L: ]: gnss_drv_l1_poweroff_check_reg: 0xC0003120=0x%x,0xC00004B8=0x%x [M:hal C:error F: L: ]: rf sta 0xC0003120=0x%x, 0xC0003148=0x%x,0xA21001E0=0x%x,0xA2010040=0x%x,0xA201003C=0x%x, 0xC00006C0=0x%x,0xC0002400=0x%x, [4]=%d, [M:hal C:error F: L: ]: rf reg(0x%x) sta  0xC000240C=0x%x 0xC0002400=0x%x ,0xC0003240=0x%x,0xC0003244=0x%x,0xC0003248=0x%x, [M:hal C:error F: L: ]: gnss_drv_get_comd4_ack [M:hal C:warning F: L: ]: l1 mcub raw data(%d) is oversize(%d) [M:hal C:warning F: L: ]: l1 unexpected mcub Seq(%d) [M:hal C:debug F: L: ]: mcub_l1_data_handler,type=%d, no=%d,end=%d, [%d].[8]=0x%x, rawdata[8]=%d [M:hal C:warning F: L: ]: l1 mcub msg is overflow(%d-%d) [M:hal C:error F: L: ]: mcub-1, ACK af=0x%x,type=%d,a0=0x%x,a1=0x%x  [M:hal C:error F: L: ]: mcub_l1_interrupt_mode_data_isr(isr), unexpected mcub type=%d,type=%d,d2a0=0x%x,d2a1=0x%x  [M:hal C:warning F: L: ]: l5 mcub raw data(%d) is oversize(%d) [M:hal C:warning F: L: ]: l5 unexpected mcub Seq(%d) [M:hal C:debug F: L: ]: mcub_l5_data_handler, type=%d, no=%d, end=%d, [%d].[8]=0x%x, rawdata[8]=%d [M:hal C:warning F: L: ]: l5 mcub msg is overflow(%d-%d) [M:hal C:error F: L: ]: mcub-5, ACK af=0x%x,a0=0x%x,a1=0x%x  [M:hal C:error F: L: ]: mcub_l5_interrupt_mode_data_isr(isr), unexpected mcub type=%d, d2a0=0x%x, d2a1=0x%x  [M:hal C:debug F: L: ]: gnss_drv_dsp_switch_to_mcu_mode: dsp type = %d start, pdma_rx_working:l1(%d)-l5(%d) [M:hal C:debug F: L: ]: gnss_drv_dsp_send_p: dsp type = %d, write %d end [M:hal C:debug F: L: ]: gnss_drv_dsp_recv_p: DSP(%d), waiting polling event (%d ms) [M:hal C:debug F: L: ]: gnss_drv_dsp_recv_p: DSP(%d), polling event arrived [M:mnl C:info F: L: ]: MnlDSP,gnss_dsp_shared_buff_init, l1_buff_size=%d [M:mnl C:info F: L: ]: MnlDSP,gnss_dsp_shared_buff_init, l5_buff_size=%d [M:mnl C:debug F: L: ]: MnlDSP,get_shared_buff,reset, move unread data(%d) [M:mnl C:debug F: L: ]: MnlDSP,l5 get_shared_buff,reset, move unread data(%d) [M:mnl C:error F: L: ]: MnlDSP,gnss_dsp_shared_buff_debug,l5 size=%d, need to be read=%d,w_count=%d [M:mnl C:debug F: L: ]: MnlDSP,detect dbtt, mcub occupied mcu, detect_start=0x%x,w_p=0x%x [M:mnl C:error F: L: ]: MnlDSP,detect dbtt, unexpected detect_state [M:mnl C:warning F: L: ]: MnlDSP,detect dbtt, no_detect_dbtt [M:mnl C:debug F: L: ]: MnlDSP,gnss_dsp_shared_buff_scan_all_for_dbtt, scan=0x%x,detect_state=0x%x, be_occupied=%d [M:mnl C:debug F: L: ]: MnlDSP,detect dbtt, DBTT(0x2C) byte found, low_len = %d,No.=%d [M:mnl C:debug F: L: ]: MnlDSP,scan all for dbtt, DBTT dbtt found, end_posi=0x%x, unread = %d [M:mnl C:error F: L: ]: MnlDSP,detect dbtt, unexpected detect_state [M:MNL_ALO C:debug F: L: ]: NkPosFD,HBD, Run_Faul_Det [M:MNL_ALO C:error F: L: ]: HtStaTS,%d,%d [M:MNL_ALO C:info F: L: ]: ExtrAge,%d [M:MNL_ALO C:debug F: L: ]: NkPosFD,Case A [M:MNL_ALO C:debug F: L: ]: NkPosFD,Case B [M:MNL_ALO C:debug F: L: ]: NkPosFD,Case C [M:MNL_ALO C:debug F: L: ]: NkPosFD,Case D [M:MNL_ALO C:debug F: L: ]: NkPosFD,Case E [M:MNL_DGP C:debug F: L: ]: DGPS,[RTCM/SBAS/SLAS] Diff soln: %d [M:MNL_S_TAG C:info F: L: ]: mnl_service_power_on [M:MNL_S_TAG C:info F: L: ]: mnl_service_power_off [M:MNL_S_TAG C:warning F: L: ]: mnl_service_init: service is ready! [M:MNL_S_TAG C:info F: L: ]: mnl_service_init [M:MNL_S_TAG C:info F: L: ]: MTK_GNSS_L5_ENABLE [M:MNL_S_TAG C:info F: L: ]: mnl_service_deinit [M:MNL_S_TAG C:warning F: L: ]: mnl_service_deinit: service is not init! [M:MNL_S_TAG C:info F: L: ]: mnl_service_register_callback [M:MNL_S_TAG C:info F: L: ]: mnl_service_deregister_callback [M:MNL_S_TAG C:debug F: L: ]: mnl_service_task_msg_handler:%d [M:MNL_S_TAG C:debug F: L: ]: MNL_SERVICE_MSG_POSITION_DATA_READY_CNF [M:MNL_S_TAG C:debug F: L: ]: MNL_SERVICE_MSG_POSITION_DATA_READY_CNF, release 1 [M:MNL_S_TAG C:debug F: L: ]: MNL_SERVICE_MSG_POSITION_DATA_READY_CNF, release 2 [M:MNL_S_TAG C:info F: L: ]: [SP3]without flash data [M:MNL_S_TAG C:info F: L: ]: [EPO]without flash data - GPS [M:MNL_S_TAG C:info F: L: ]: [EPO]without flash data - GLONASS [M:MNL_S_TAG C:info F: L: ]: [EPO]without flash data - BEIDOU [M:MNL_S_TAG C:info F: L: ]: [EPO]without flash data - GALILEO [M:MNL_S_TAG C:info F: L: ]: [EPO]without flash data - QZSS [M:MNL_S_TAG C:debug F: L: ]: MNL_SERVICE_MSG_DR_DATA_READY_CNF [M:MNL_S_TAG C:debug F: L: ]: MNL_SERVICE_MSG_VMD_DATA_READY_CNF [M:MNL_S_TAG C:debug F: L: ]: MNL_SERVICE_MSG_VMD_DATA_READY_CNF, release 1 [M:MNL_S_TAG C:debug F: L: ]: MNL_SERVICE_MSG_ALL_DATA_READY_OUTPUT [M:MNL_S_TAG C:info F: L: ]: Change DVFS, results:%d, current CPU freq:%d [M:MNL_S_TAG C:info F: L: ]: mnl_service_send_data:%d [M:MNL_S_TAG C:info F: L: ]: mnl_service_send_rtcm:%d [M:MNL_S_TAG C:error F: L: ]: mnl_service_decode_binary_proprietary_data_length, wrong parameter [M:MNL_S_TAG C:error F: L: ]: mnl_service_encode_binary_proprietary_packet, wrong parameter [M:MNL_S_TAG C:error F: L: ]: mnl_service_binary_decode_proprietary, wrong para [M:MNL_S_TAG C:error F: L: ]: Null buffer pointer input. [M:MNL_S_TAG C:error F: L: ]: Null buffer pointer input. [M:MNL_S_TAG C:error F: L: ]: Binary msg wrong data. [M:MNL_S_TAG C:error F: L: ]: Null buffer pointer input. [M:MNL_S_TAG C:error F: L: ]: Null buffer pointer input. [M:MNL_S_TAG C:info F: L: ]: Data size is not correct, no need output. [M:MNL_S_TAG C:error F: L: ]: Null buffer pointer input. [M:MNL_S_TAG C:error F: L: ]: Null buffer pointer input. [M:MNL_S_TAG C:error F: L: ]: mnl_service_command_add_urc:%d, Malloc Fail! [M:MNL_S_TAG C:error F: L: ]: Failed to execute PAIR050,%d, since adaptive L5 is on [M:MNL_S_TAG C:debug F: L: ]: PAIR066 , single_band = %d, mnl_service_get_dual_band_state()=%d, (config & 0xEF) = 0x%x [M:MNL_S_TAG C:error F: L: ]: Failed to execute PAIR080, since adaptive L5 is on [M:MNL_S_TAG C:error F: L: ]: failed to set Non-Fitness navigation mode, because ULP is enalbe, please set ULP disable(PAIR700) [M:MNL_S_TAG C:debug F: L: ]: mnl_service_command_common_set_band_num_handle , band(%d) mnl_state %d [M:MNL_S_TAG C:error F: L: ]: Failed to execute PAIR104,%d, since the current constellations can't support [M:MNL_S_TAG C:error F: L: ]: Failed to execute PAIR104,%d, since adaptive L5 is on [M:MNL_S_TAG C:error F: L: ]: Failed to execute PAIR104,%d, since GNSS service is on [M:MNL_S_TAG C:error F: L: ]: Failed to execute PAIR690, since adaptive L5 is on [M:MNL_S_TAG C:error F: L: ]: Failed to execute PAIR690, since clear L5 is enable [M:MNL_S_TAG C:error F: L: ]: can't  do periodic, because ULP is enable  [M:MNL_S_TAG C:error F: L: ]: can't do ulp_enable because fix rate is more than 1HZ  [M:MNL_S_TAG C:error F: L: ]: Can't do ulp_enable because is in GLP mode [M:MNL_S_TAG C:error F: L: ]: Can't do ulp_enable because is in DTM mode [M:MNL_S_TAG C:error F: L: ]: can't do ulp_enable because the current navigation mode is not support [M:MNL_S_TAG C:error F: L: ]: ULP command not support when CPU freq in 96MHz [M:MNL_S_TAG C:error F: L: ]: buffer overflow in NMEA output!!! [M:MNL_S_TAG C:debug F: L: ]: mnl_service_task_main [M:MNL_S_TAG C:debug F: L: ]: mnl_service_task_send_message_to_mnl_force:%d [M:MNL_S_TAG C:debug F: L: ]: mnl_service_task_send_message_to_mnl_force:Fail! MNL is not running! [M:MNL_S_TAG C:debug F: L: ]: mnl_service_task_send_message_to_mnl:%d,periodic=%d [M:MNL_S_TAG C:debug F: L: ]: mnl_service_task_send_message_to_mnl:Fail! Not Power On! [M:MNL_S_TAG C:debug F: L: ]: mnl_service_task_send_message_to_service:%d:%d [M:MNL_S_TAG C:error F: L: ]: %d, Malloc Fail! [M:MNL_S_TAG C:error F: L: ]: mnl_service_task_send_message_to_service: FAIL! [M:MNL_S_TAG C:error F: L: ]: mnl_service_task_send_message_to_fusion:%d:%d [M:MNL_S_TAG C:debug F: L: ]: mnl_service_task_init:%d,%d [M:MNL_S_TAG C:debug F: L: ]: mnl_service_mnl_application_callback:%d [M:MNL_S_TAG C:error F: L: ]: mnl_service_data_inc wrong data format type!! [M:MNL_S_TAG C:debug F: L: ]: mnl_service_mnl_init [M:MNL_S_TAG C:debug F: L: ]: mnl_service_mnl_power_on [M:MNL_S_TAG C:debug F: L: ]: mnl_service_mnl_power_off [M:MNL_S_TAG C:debug F: L: ]: mnl_service_setting_g_init_config: ULP enable, vcore=0.6, fix_interval = 1000 [M:MNL_S_TAG C:debug F: L: ]: mnl_service_setting_g_init_config: nav_config.band_num=0x%x [M:MNL_S_TAG C:info F: L: ]: [SP3]flash_read:%d,%d [M:MNL_S_TAG C:info F: L: ]: mnl_service_setting_is_flash_valid: CRC32 Pass, config_crc = 0x%08X, cal_crc = 0x%08X [M:MNL_S_TAG C:error F: L: ]: mnl_service_setting_is_flash_valid: CRC32 Failed, config_crc = 0x%08X, cal_crc = 0x%08X [M:MNL_S_TAG C:info F: L: ]: mnl_service_setting_is_flash_valid: %d [M:MNL_S_TAG C:error F: L: ]: mnl_service_setting_nvram_fopen fail:%d [M:MNL_S_TAG C:error F: L: ]: mnl_service_setting_nvram_fread fail:%d
 [M:MNL_S_TAG C:error F: L: ]: mnl_service_setting_nvram_fwrite fail:%d [M:MNL_S_TAG C:info F: L: ]: mnl_service_setting_nvram_clear [M:MNL_S_TAG C:info F: L: ]: mnl_service_setting_init_from_nvram [M:MNL_S_TAG C:info F: L: ]: RTC NVRAM Check:%d [M:MNL_S_TAG C:info F: L: ]: mnl_service_setting_init_from_nvram init1 success! [M:MNL_S_TAG C:info F: L: ]: FS NVRAM File Check:%d [M:MNL_S_TAG C:info F: L: ]: FS NVRAM Data Check:%d [M:MNL_S_TAG C:info F: L: ]: mnl_service_setting_init_from_nvram init0 success! [M:MNL_S_TAG C:error F: L: ]: mnl_service_setting_init_from_nvram fail! [M:MNL_S_TAG C:info F: L: ]: mnl_service_setting_nvram_save_to_fs [M:MNL_S_TAG C:info F: L: ]: mnl_service_setting_init_from_default [M:MNL_S_TAG C:info F: L: ]: mnl_service_setting_dump: size %d [M:MNL_S_TAG C:info F: L: ]: nvram_sig: 0x%x [M:MNL_S_TAG C:info F: L: ]: mnl_init_config.checksum: 0x%x [M:MNL_S_TAG C:info F: L: ]: mnl_init_config.if_type: 0x%x [M:MNL_S_TAG C:info F: L: ]: mnl_init_config.pps_mode: 0x%x [M:MNL_S_TAG C:info F: L: ]: mnl_init_config.tcxo_mode: 0x%x [M:MNL_S_TAG C:info F: L: ]: mnl_init_config.pps_duty: 0x%x [M:MNL_S_TAG C:info F: L: ]: mnl_init_config.pps_port: 0x%x [M:MNL_S_TAG C:info F: L: ]: mnl_init_config.u1ClockType: 0x%x [M:MNL_S_TAG C:info F: L: ]: mnl_init_config.hw_Clock_Drift: 0x%x [M:MNL_S_TAG C:info F: L: ]: mnl_init_config.hw_Clock_Freq: 0x%x [M:MNL_S_TAG C:info F: L: ]: mnl_init_config.if_link_spd: 0x%x [M:MNL_S_TAG C:info F: L: ]: mnl_init_config.fix_interval: 0x%x [M:MNL_S_TAG C:info F: L: ]: mnl_init_config.Int_LNA_Config: 0x%x [M:MNL_S_TAG C:info F: L: ]: mnl_init_config.reservedx: 0x%x [M:MNL_S_TAG C:info F: L: ]: mnl_init_config.reservedx_2: 0x%x [M:MNL_S_TAG C:info F: L: ]: mnl_init_config.reservedy: 0x%x [M:MNL_S_TAG C:info F: L: ]: mnl_init_config.reservedy_2: 0x%x [M:MNL_S_TAG C:info F: L: ]: mnl_init_config.freq: 0x%x [M:MNL_S_TAG C:info F: L: ]: mnl_init_config.sv_type: 0x%x [M:MNL_S_TAG C:info F: L: ]: mnl_init_config.dgpsmode: 0x%x [M:MNL_S_TAG C:info F: L: ]: mnl_init_config.platform_freq: 0x%x [M:MNL_S_TAG C:info F: L: ]: nmea_config.checksum: 0x%x [M:MNL_S_TAG C:info F: L: ]: nmea_config.output_version: 0x%x [M:MNL_S_TAG C:info F: L: ]: nmea_config.output_mode: 0x%x [M:MNL_S_TAG C:info F: L: ]: nmea_config.interval[%d]: 0x%x [M:MNL_S_TAG C:info F: L: ]: nmea_config.order[%d]: 0x%x [M:MNL_S_TAG C:info F: L: ]: nmea_config.position_digit: 0x%x [M:MNL_S_TAG C:info F: L: ]: nmea_config.altitude_digit: 0x%x [M:MNL_S_TAG C:info F: L: ]: nmea_config.dop_digit: 0x%x [M:MNL_S_TAG C:info F: L: ]: nmea_config.speed_digit: 0x%x [M:MNL_S_TAG C:info F: L: ]: nmea_config.course_digit: 0x%x [M:MNL_S_TAG C:info F: L: ]: nmea_config.start_dow: 0x%x [M:MNL_S_TAG C:info F: L: ]: nmea_config.start_wn: 0x%x [M:MNL_S_TAG C:info F: L: ]: nmea_config.fg_extra_output: 0x%x [M:MNL_S_TAG C:info F: L: ]: nmea_config.time_blank_when_utc_is_not_valid: 0x%x [M:MNL_S_TAG C:info F: L: ]: proprietary_config.checksum: 0x%x [M:MNL_S_TAG C:info F: L: ]: proprietary_config.output_mode: 0x%x [M:MNL_S_TAG C:info F: L: ]: proprietary_config.output_type: 0x%x [M:MNL_S_TAG C:info F: L: ]: proprietary_config.fg_combined_sv_status: 0x%x [M:MNL_S_TAG C:info F: L: ]: proprietary_config.fg_debug_mode: 0x%x [M:MNL_S_TAG C:info F: L: ]: proprietary_config.interval[%d]: 0x%x [M:MNL_S_TAG C:info F: L: ]: sp3_config.checksum: 0x%x [M:MNL_S_TAG C:info F: L: ]: proprietary_config.interval[%d]: 0x%x [M:MNL_S_TAG C:info F: L: ]: sp3_config.gps_address[%d]: 0x%x [M:MNL_S_TAG C:info F: L: ]: sp3_config.glonass_address[%d]: 0x%x [M:MNL_S_TAG C:info F: L: ]: sp3_config.galileo_address[%d]: 0x%x [M:MNL_S_TAG C:info F: L: ]: sp3_config.beidou_address[%d]: 0x%x [M:MNL_S_TAG C:info F: L: ]: sp3_config.qzss_address[%d]: 0x%x [M:MNL_S_TAG C:info F: L: ]: sp3_config.gps_status[%d]: 0x%x [M:MNL_S_TAG C:info F: L: ]: sp3_config.glonass_status[%d]: 0x%x [M:MNL_S_TAG C:info F: L: ]: sp3_config.galileo_status[%d]: 0x%x [M:MNL_S_TAG C:info F: L: ]: sp3_config.beidou_status[%d]: 0x%x [M:MNL_S_TAG C:info F: L: ]: sp3_config.qzss_status[%d]: 0x%x [M:MNL_S_TAG C:info F: L: ]: sp3_config.flash_aiding: 0x%x [M:MNL_S_TAG C:info F: L: ]: epo_config.checksum: 0x%x [M:MNL_S_TAG C:info F: L: ]: epo_config.gps_address: 0x%x [M:MNL_S_TAG C:info F: L: ]: epo_config.glonass_address: 0x%x [M:MNL_S_TAG C:info F: L: ]: epo_config.gps_segment: 0x%x [M:MNL_S_TAG C:info F: L: ]: epo_config.glonass_segment: 0x%x [M:MNL_S_TAG C:info F: L: ]: epo_config.gps_status[%d]: 0x%x [M:MNL_S_TAG C:info F: L: ]: epo_config.glonass_status[%d]: 0x%x [M:MNL_S_TAG C:info F: L: ]: epo_config.galileo_status[%d]: 0x%x [M:MNL_S_TAG C:info F: L: ]: epo_config.beidou_status[%d]: 0x%x [M:MNL_S_TAG C:info F: L: ]: epo_config.flash_aiding: 0x%x [M:MNL_S_TAG C:info F: L: ]: rtcm_config.checksum: 0x%x [M:MNL_S_TAG C:info F: L: ]: rtcm_config.config.input_version: 0x%x [M:MNL_S_TAG C:info F: L: ]: rtcm_config.config.base_rover: 0x%x [M:MNL_S_TAG C:info F: L: ]: rtcm_config.config.output_mode: 0x%x [M:MNL_S_TAG C:info F: L: ]: rtcm_config.config.output_sv_type: 0x%x [M:MNL_S_TAG C:info F: L: ]: rtcm_config.config.output_eph: 0x%x [M:MNL_S_TAG C:info F: L: ]: rtcm_config.config.output_freq: 0x%x [M:MNL_S_TAG C:info F: L: ]: rtcm_config.config.output_ref_stat: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.checksum: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.high_accuracy: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.min_snr: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.high_sensitivity_mode: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.elev_mask: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.navigation_mode: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.easy_enable: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.background_easy: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.aic_config_value: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.ulp_enble: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.jamming_detection_config_value: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.raw_output_status: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.raw_low_gain_status: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.raw_carr_smooth_enable: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.accurate_cnr_enable: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.nvram_auto_enable: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.static_threshold: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.static_mode: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.debug_output_status: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.ascii_debug_output_status: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.elev_threshold: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.datum_mode: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.ulp_test_mode_enable: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.qzss_enable: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.limit_track_num: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.ulp_enble: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.rtcm_output_enable: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.adp_L5_enable: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.pvt_callback_type: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.raw_meas_callback_type: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.eph_notify_enable: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.raw_navi_msg_output_enable: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.epo_enable: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.bd_geo_enable: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.dsp_off_cpu_level: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.android_enable: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.drone_mode_prop_output_enable: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.output_smoothed_speed_in_rmc: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.immediate_speed_mode: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.enable_2d_fix: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.acc_cal_type: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.estimated_num: 0x%x [M:MNL_S_TAG C:info F: L: ]: navigation_config.output_threshold: 0x%x [M:MNL_S_TAG C:info F: L: ]: datum_config.checksum: 0x%x [M:MNL_S_TAG C:info F: L: ]: pps_config.checksum: 0x%x [M:MNL_S_TAG C:info F: L: ]: pps_config.sync_nmea: 0x%x [M:MNL_S_TAG C:info F: L: ]: pps_config.falling_edge_enable: 0x%x [M:MNL_S_TAG C:info F: L: ]: pps_config.timing_product: 0x%x [M:MNL_S_TAG C:info F: L: ]: pps_config.pps_by_user: 0x%x [M:MNL_S_TAG C:info F: L: ]: pps_config.local_ms: 0x%x [M:MNL_S_TAG C:info F: L: ]: pps_config.phase: 0x%x [M:MNL_S_TAG C:info F: L: ]: pps_config.pps_type: 0x%x [M:MNL_S_TAG C:info F: L: ]: pps_config.pps_pulse_width: 0x%x [M:MNL_S_TAG C:info F: L: ]: pps_config.survey_time: 0x%x [M:MNL_S_TAG C:info F: L: ]: pps_config.fix_cooridinate_type: 0x%x [M:MNL_S_TAG C:info F: L: ]: pps_config.pps_delay_time: 0x%x [M:MNL_S_TAG C:info F: L: ]: sbasstatus_config.checksum:  0x%x [M:MNL_S_TAG C:info F: L: ]: sbasstatus_config.sbasstatus:  %d [M:MNL_S_TAG C:info F: L: ]: sbasstatus_config.sbasprnmode: %d [M:MNL_S_TAG C:info F: L: ]: sbasstatus_config.sbasopmode:  %d [M:MNL_S_TAG C:info F: L: ]: sbasstatus_config.sbas_raw_output:  %d [M:MNL_S_TAG C:info F: L: ]: sbasstatus_config.sbas_blacklist:  0x%x [M:MNL_S_TAG C:info F: L: ]: slas_status_config.checksum: 0x%x [M:MNL_S_TAG C:info F: L: ]: slas_status_config.slasstatus: %d [M:MNL_S_TAG C:info F: L: ]: slas_status_config.slasopmode: %d [M:MNL_S_TAG C:info F: L: ]: slas_status_config.slasopregion: %d [M:MNL_S_TAG C:info F: L: ]: ppp_setting.checksum: 0x%x [M:MNL_S_TAG C:info F: L: ]: ppp_setting.ppp_enable: 0x%x [M:MNL_S_TAG C:info F: L: ]: ppp_setting.ppp_elve_mask: 0x%x [M:MNL_S_TAG C:info F: L: ]: ppp_setting.ppp_snr_mask: 0x%x [M:MNL_S_TAG C:info F: L: ]: ppp_setting.ppp_freq_used: 0x%x [M:MNL_S_TAG C:info F: L: ]: ppp_setting.ppp_sv_type: 0x%x [M:MNL_S_TAG C:info F: L: ]: initial_pos_config.checksum: 0x%x [M:MNL_S_TAG C:info F: L: ]: dcb_setting.checksum: 0x%x [M:MNL_S_TAG C:info F: L: ]: dcb_setting.dcb_type: 0x%x [M:MNL_S_TAG C:info F: L: ]: dcb_setting.sv_type: 0x%x [M:MNL_S_TAG C:info F: L: ]: geofence_config.checksum: 0x%x [M:MNL_S_TAG C:info F: L: ]: geofence_config.FenceNum: 0x%x [M:MNL_S_TAG C:info F: L: ]: geofence_config.ConfLvl: 0x%x [M:MNL_S_TAG C:info F: L: ]: geofence_config.GPIOPolarity: 0x%x [M:MNL_S_TAG C:info F: L: ]: geofence_config.status: 0x%x [M:MNL_S_TAG C:info F: L: ]: geofence_config.rad[%d]: 0x%x [M:MNL_S_TAG C:info F: L: ]: ref_loc_config.checksum: 0x%x [M:MNL_S_TAG C:info F: L: ]: ref_loc_config.station_id: 0x%x [M:MNL_S_TAG C:info F: L: ]: ref_loc_config.itrf: 0x%x [M:MNL_S_TAG C:info F: L: ]: ref_loc_config.enable: 0x%x [M:MNL_S_TAG C:info F: L: ]: ref_loc_config.coordinate_mode: 0x%x [M:MNL_S_TAG C:info F: L: ]: checksum: 0x%x [M:MNL_S_TAG C:debug F: L: ]: binary encode: NULL buffer [M:MNL_S_TAG C:debug F: L: ]: binary encode: wrong parameters [M:MNL_S_TAG C:info F: L: ]: binary decode: wrong parameters [M:MNL_S_TAG C:error F: L: ]: binary encode: wrong checksum [M:MNL_S_TAG C:error F: L: ]: binary encode: wrong endword [M:MNL_S_TAG C:debug F: L: ]: Test binary data: [M:MNL_S_TAG C:debug F: L: ]: %x  [M:MNL_S_TAG C:debug F: L: ]: encode binary data size = %d [M:MNL_S_TAG C:debug F: L: ]: encode binary data:  [M:MNL_S_TAG C:debug F: L: ]: %0x  [M:MNL_S_TAG C:debug F: L: ]: decode binary data status = %d [M:MNL_S_TAG C:debug F: L: ]: decode binary data:  [M:MNL_S_TAG C:debug F: L: ]: %0x  [M:MNL_S_TAG C:error F: L: ]: mnl_service_decode_binary_packet_length: wrong checksum [M:MNL_S_TAG C:error F: L: ]: mnl_service_decode_binary_packet_length: wrong preamble [M:MNL_S_TAG C:error F: L: ]: mnl_service_decode_binary_packet_length: wrong endword [M:MNL_S_TAG C:error F: L: ]: mnl_service_decode_binary_packet_msgid: wrong checksum [M:MNL_S_TAG C:error F: L: ]: mnl_service_decode_binary_packet_msgid: wrong preamble [M:MNL_S_TAG C:error F: L: ]: mnl_service_decode_binary_packet_msgid: wrong endword [M:MNL_S_TAG C:error F: L: ]: mnl_service_binary_data_handle, get length fail [M:MNL_S_TAG C:error F: L: ]: mnl_service_binary_data_handle, Malloc fail [M:MNL_S_TAG C:error F: L: ]: mnl_service_fusion_data_in_handler, no fusion config [M:MNL_S_TAG C:error F: L: ]: buffer overflow in Binay Proprietary data output!!! [M:MNL_S_TAG C:error F: L: ]: buffer overflow in Debug Proprietary output!!! [M:MNL_S_TAG C:warning F: L: ]: Proprietary, output no type, mode:%d,msgid:%d [M:MNL_S_TAG C:info F: L: ]: [SP3] clear flash: %d,%d,ret:%d [M:MNL_S_TAG C:info F: L: ]: [SP3]flash_read:%d,%d [M:MNL_S_TAG C:info F: L: ]: [SP3]flash_write:%d,%d [M:MNL_S_TAG C:info F: L: ]: [SP3]mnl_service_sp3_aiding_data_flash:%d [M:MNL_S_TAG C:info F: L: ]: [SP3]read segment:%d, w:%X,t:%X,c:%d,c_t:%X,c_a:%X [M:MNL_S_TAG C:debug F: L: ]: [SP3]send:%d,%d,%08X,%08X,%08X,%08X [M:MNL_S_TAG C:error F: L: ]: [SP3] mnl_service_sp3_binary_handle, wrong parameter [M:MNL_S_TAG C:debug F: L: ]: [SP3]Binary Start:%c,%d [M:MNL_S_TAG C:debug F: L: ]: [SP3]Binary Data:%c,%d [M:MNL_S_TAG C:debug F: L: ]: [SP3]Binary End:%d [M:MNL_S_TAG C:warning F: L: ]: [RTCM] Send data to mnl error, no data [M:MNL_S_TAG C:warning F: L: ]: [RTCM] Wrong size of data buffer. [M:MNL_S_TAG C:debug F: L: ]: [RTCM2] Send data to mnl, type: %d, len: %d [M:MNL_S_TAG C:debug F: L: ]: [RTCM3] Send data to mnl, type: %d, len: %d [M:MNL_S_TAG C:warning F: L: ]: [RTCM] No data input [M:MNL_S_TAG C:debug F: L: ]: [RTCM2] preamble ok, type: %d [M:MNL_S_TAG C:debug F: L: ]: [RTCM2] Support type: %d [M:MNL_S_TAG C:debug F: L: ]: [RTCM2] Not support type: %d [M:MNL_S_TAG C:debug F: L: ]: [RTCM3] Input RTCM3 not support, type= %u [M:MNL_S_TAG C:warning F: L: ]: [RTCM3] Input RTCM3 parity error, ms_type: %d [M:MNL_S_TAG C:info F: L: ]: [EPO]mnl_service_epo_aiding_data_flash:%d, [%d,%d] [M:MNL_S_TAG C:error F: L: ]: %d, Param Error, get rtc time:%d [M:MNL_S_TAG C:info F: L: ]: Segment:%d, c_hour:%d, e_hour:%d [M:MNL_S_TAG C:error F: L: ]: %d, Get valid file Fail:%d! [M:MNL_S_TAG C:error F: L: ]: %d, fseek Fail:%d! [M:MNL_S_TAG C:error F: L: ]: %d, Check Time Fail:[%d,%d]! [M:MNL_S_TAG C:error F: L: ]: Check SV Time Fail! SV:%d, time [%d,%d], info:%08x [M:MNL_S_TAG C:error F: L: ]: %d, Open File Fail! [M:MNL_S_TAG C:info F: L: ]: [EPO] mnl_service_epo_binary_handle, wrong parameter [M:MNL_S_TAG C:info F: L: ]: [EPO]Binary Start:%c [M:MNL_S_TAG C:info F: L: ]: [EPO]Binary Data:0x%02X%02X%02X,%d [M:MNL_S_TAG C:info F: L: ]: [EPO]Binary End:%c 