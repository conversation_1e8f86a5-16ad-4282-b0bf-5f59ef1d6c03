/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   LauncherView.cpp
@Time    :   2024/12/11 14:00:50
*
**************************************************************************/

#include "LauncherView.h"
#include "../../qwos_app/GUI/Font/QwFont.h"
#include "../../qwos_app/GUI/QwGUITheme.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgSportRemind/sports_remind_def.h"
#include "GUI/QwGUIKey.h"
#include "Image/images.h"
#include "igs_global.h"
#include "remind_response_app/remind_response_app.h"
#include "service_datetime.h"
#ifdef IGS_DEV
#include "basic_app_module/sensor_service_module/sensor_def.h"
#include "basic_app_module/sensor_service_module/sensor_evt_handler.h"
#include "gps_dev.h"
#include "service_health.h"
#include "subscribe_data.h"
#include "subscribe_service.h"
#endif
#include "MvcApp.h"
#include "QwMsgBottomToast/QwMsgBottomToast.h"
#include "backlight_module/backlight_module.h"
#include "battery_srv_module/battery_srv.h"
#include "cfg_header_def.h"
#include "countdown_timer_app/countdown_timer_app.h"
#include "gui_event_service.h"
#include "qw_time_util.h"
#ifndef SIMULATOR
#if GPS_DATA_TEST
static void algo_gps_data_callback(const void *in, uint32_t len)
{
    if (in == NULL)
    {
        SUB_DATA_E("in=NULL %s\n", __FUNCTION__);
        return;
    }

    gps_pub_t *in_data = static_cast<gps_pub_t *> in;
    printf("@@@@@@@@@ speed= %f\n", in_data->speed);
}
#endif   // GPS_DATA_TEST
#endif   // !SIMULATOR
static uint32_t s_prv_dial_goodsid = get_cfg_current_using_dial_goodsid();

LauncherView::LauncherView(PageManager *manager)
    : PageView(manager)
    , p_launcher_time_(nullptr)
    , update_launcher_time_(this, &LauncherView::update_launcher_time)
    , jsContainer_(nullptr)
    , jsAppObjectName_(nullptr)
    , press_timestamp_(0)
    , editDateNum_(0)
    , edit_data_rect_(0)
{}

LauncherView::~LauncherView()
{
    // if (jsContainer_)
    // {
    //     PageManager *page_manager = MvcApp::get_mvc_app()->getPageManager();
    //     if(page_manager == nullptr)
    //     {
    //         assert(false && "LauncherView::page_manager is nullptr");
    //     }
    //     const char* next_page = page_manager->get_next_page();
    //      if(strcmp(next_page, "SelectDial") == 0)
    //     {
    //         qjs_touchgfx_remove_app(jsAppObjectName_);
    //         jsAppObjectName_ = nullptr;
    //     }

    //     remove(*jsContainer_);
    //     if (jsAppObjectName_)
    //     {
    //         js_mem_free(static_cast<void*>(jsAppObjectName_));
    //     }
    // }
    // else
    // {
    //     if (jsAppObjectName_)
    //     {
    //         js_mem_free(static_cast<void*>(jsAppObjectName_));
    //     }
    // }
    // js_stop_dial();
    js_destory_dial();
}

void LauncherView::setup()
{
#ifdef EXPORT_TRANSLATE_KEY
    TM_KEY(TM_DECLARE_END_STR);
#endif   // EXPORT_TRANSLATE_KEY

    char jsPath[50] = {0};
    uint32_t dialGoodsId = get_cfg_current_using_dial_goodsid();
    bool get_dial_container(CacheableContainer * *container);
    rt_kprintf("dialGoodsId = %u s_prv_dial_goodsid:%u\n", dialGoodsId, s_prv_dial_goodsid);
    if (s_prv_dial_goodsid != dialGoodsId)
    {
        // 释放Launcher的表盘资源
        js_destory_dial();
        get_dial_container(&jsContainer_);
        remove(*jsContainer_);
        jsContainer_ = nullptr;
        s_prv_dial_goodsid = dialGoodsId;
    }
    get_dial_path(dialGoodsId, jsPath);

    add(bg_);
    bg_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
    bg_.setColor(lv_color_black());

    if (js_create_dial(jsPath))
    {
        get_dial_container(&jsContainer_);
        add(*jsContainer_);

        // 获取可编辑数据的坐标
        editDateNum_ = get_dial_data_num(NULL);
        for(int i = 0; i < editDateNum_; i++)
        {
            edit_data_positon_t position = {0};
            get_edit_data_positon(i, &position);
            edit_data_rect_[i].x = position.x;
            edit_data_rect_[i].y = position.y;
            edit_data_rect_[i].width = position.width;
            edit_data_rect_[i].height = position.height;
        }
    }
    else
    {
        add(view_);
        view_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);

        view_.add(launcher_time_);
        view_.add(launcher_date_);

        char data_text[50];

        if (p_launcher_time_ != nullptr)
        {
            qw_tm_t tm_time = {0};
            time_t tmp = (*p_launcher_time_->get_val(0));
            service_sec_2_rtctime(tmp, &tm_time);
            sprintf(data_text, "%02d:%02d:%02d", tm_time.tm_hour, tm_time.tm_min, tm_time.tm_sec);

            launcher_time_.setTextFont(&NUMBER_M_FONT);
            launcher_time_.setTextAlignment(CENTER);
            launcher_time_.setColor(lv_color_white());
            launcher_time_.setLabelAlpha(LV_OPA_TRANSP);
            launcher_time_.setTypedDynamicText(data_text);
            launcher_time_.resizeToCurrentTextWithAlignment();
            launcher_time_.setAlign(ALIGN_IN_CENTER);

            sprintf(data_text, "%04d/%02d/%02d", tm_time.tm_year + 1900, tm_time.tm_mon + 1, tm_time.tm_mday);
            launcher_date_.setTextFont(&NUMBER_S_FONT);
            launcher_date_.setTextAlignment(CENTER);
            launcher_date_.setColor(lv_color_white());
            launcher_date_.setLabelAlpha(LV_OPA_TRANSP);
            launcher_date_.setTypedDynamicText(data_text);
            launcher_date_.resizeToCurrentTextWithAlignment();
            launcher_date_.setAlign(ALIGN_IN_CENTER, 0, 100);
        }

        view_.set_background_color(0x000000);
        view_.set_expand_bottom_height(0);
    }
}

void LauncherView::handleTickEvent()
{
    //通知battery可以更新
    gui_notify_battery_run();
    if (press_timestamp_ + 2 == (uint32_t) get_sec_from_rtc())
    {
        remind_trigger(RRT_B_HOLD_TOUCH_DIAL, true);
        set_enter_dial_edit_type(EDIT_MODE_LONG_PRESS);
        manager_->push("SelectDial");
    }
}

#include "../../qwos_app/GUI/QwMsgBox/MsgFocusOn/MsgDND.h"

void LauncherView::handleKeyEvent(uint8_t c)
{
    if (c == KEY_CLK_START)
    {
        uint8_t sports_type = TOTAL_SPORTS_TYPE_ALL;   //全部的运动
        manager_->page_command("SportsMenu", 0, &sports_type);
        manager_->push("SportsMenu");
    }
    else if (c == KEY_HOLD_POWER)
    {
        manager_->push("SystemSettingsMenu");
    }
    else if (c == KEY_HOLD_BACK)
    {
        manager_->push("ToolsMenu");
    }
    else if (c == KEY_LONG_POWER_AND_BACK)
    {
        manager_->push("FactoryMenu");
    }
    else if (c == KEY_KNOB_UP)
    {
        int focus_card = get_custom_app_context(0);
        manager_->page_command("MenuCard", (int) MenuCard_CMD::CTRL_SELECT_INDEX, &focus_card);
        manager_->push("MenuCard");
    }
    else if (c == KEY_KNOB_DOWN)
    {
        int focus_card = get_custom_app_context(get_custom_app_count() - 1);
        manager_->page_command("MenuCard", (int) MenuCard_CMD::CTRL_SELECT_INDEX, &focus_card);
        manager_->push("MenuCard");
    }
    else if (c == KEY_CLK_BACK)   //测试
    {
        const char *page_name = "Launcher";
        manager_->page_command("RTCAccuracyTest", (int) SettingLastPage_CMD::SET_LAST_PAGE, &page_name);
        manager_->push("RTCAccuracyTest");

        // static FULL_TIP_TYPE type_ = FULL_TIP_TYPE::TIP_DND;
        //submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_TEST2, &type_);

        // static sensor_search_infor_t sensor_search_infor;
        // sensor_search_infor.sensor_type = SENSOR_TYPE_HRM;
        // sensor_search_infor.sensor_work_state = SENSOR_WORK_STATE_CONNECTED;
        // submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_SENSOR_CONNECT_NOT_IN_MOTION, &sensor_search_infor);
    }
    else if (c == KEY_HOLD_START)
    {
        int last_sport_type = get_last_sport_type();
        for (int i = 0; i < get_custom_sports_count(); i++)
        {
            if (last_sport_type == get_custom_sports_context(i))
            {
                break;
            }
            if (i == get_custom_sports_count() - 1)
            {
                last_sport_type = get_custom_sports_context(0);
            }
        }
        set_current_sport_mode((SPORTTYPE) last_sport_type);
        manager_->page_command("SportStart", (int) SportStart_CMD::SET_READY_SPORT, NULL);
        // manager_->page_command("SportStart", (int)SportStart_CMD::SET_COUNTDOWN_TIME, (void*)COUNTDOWN_TIMER_EVENT_RESET);
        manager_->push("SportStart");
    }
}

void LauncherView::handleClickEvent(const ClickEvent &evt)
{
    if (evt.getType() == ClickEvent::PRESSED)
    {
        press_timestamp_ = (uint32_t) get_sec_from_rtc();
        for(int i = 0; i < editDateNum_; i++)
        {
            if(edit_data_rect_[i].intersect(evt.getX(), evt.getY()))
            {
                uint32_t data_type = get_dial_data_type(i);
                const char* data_name = get_edit_data_name(data_type);
                if(data_name != NULL)
                {
                    manager_->push(data_name);
                }
                break;
            }
        }
    }
    else
    {
        press_timestamp_ = 0;
    }
}

void LauncherView::handleDragEvent(const DragEvent &evt)
{}

void LauncherView::handleGestureEvent(const GestureEvent &evt)
{
    if (evt.getType() == GestureEvent::GestureEventType::SWIPE_VERTICAL)
    {
        if (evt.getVelocity() > GESTURE_EXIT_ACCURACY)
        {
            int focus_card = get_custom_app_context(get_custom_app_count() - 1);
            manager_->page_command("MenuCard", (int) MenuCard_CMD::CTRL_SELECT_INDEX, &focus_card);
            manager_->push("MenuCard");
        }
        else if (evt.getVelocity() < -GESTURE_EXIT_ACCURACY)
        {
            int focus_card = get_custom_app_context(0);
            manager_->page_command("MenuCard", (int) MenuCard_CMD::CTRL_SELECT_INDEX, &focus_card);
            manager_->push("MenuCard");
        }
    }
}

void LauncherView::set_update_launcher_time(ObserverDrawable<Drawable, uint32_t, 1> *observer)
{
    if (observer != nullptr)
    {
        p_launcher_time_ = observer;
        observer->bind_ctrl(0, launcher_time_);
        observer->bind_notify(update_launcher_time_);
    }
}

void LauncherView::update_launcher_time(Drawable *ctrl, Parameters<uint32_t> *data, int idx)
{
    if (ctrl != nullptr && data != nullptr)
    {
        TextArea *p_text = static_cast<TextArea *>(ctrl);

        if (p_text->getParent() != nullptr)
        {
            char data_text[50];
            qw_tm_t tm_time = {0};
            time_t tmp = (*p_launcher_time_->get_val(0));
            service_sec_2_rtctime(tmp, &tm_time);
            sprintf(data_text, "%02d:%02d:%02d", tm_time.tm_hour, tm_time.tm_min, tm_time.tm_sec);

            p_text->setTypedDynamicText(data_text);
            p_text->resizeToCurrentTextWithAlignment();
            p_text->setAlign(ALIGN_IN_CENTER);

            sprintf(data_text, "%04d/%02d/%02d", tm_time.tm_year + 1900, tm_time.tm_mon + 1, tm_time.tm_mday);
            launcher_date_.setTypedDynamicText(data_text);
            launcher_date_.resizeToCurrentTextWithAlignment();
            launcher_date_.setAlign(ALIGN_IN_CENTER, 0, 100);
        }
    }
}
