00000340 D combo_mem_sw_list
00000296 T __divdi3
00000288 t combo_mem_hw_list
00000284 T bl_print_internal
00000282 T __udivdi3
0000025c T clock_mux_sel
00000200 b buf
00000190 d clock_domain
00000180 T SF_DAL_ProgramData
00000174 T SF_DAL_EraseBlock
00000170 T clock_div_ctrl
0000016c T __aeabi_frsub
00000164 T __subsf3
00000164 T __aeabi_fsub
00000160 T __aeabi_fadd
00000160 T __addsf3
00000154 T clk_mux_init
00000114 T SF_DAL_Flash_Protect
00000110 T SF_DAL_Flash_Unprotect
00000100 T SF_DAL_Init_Common
000000e4 T SF_DAL_Init_MXIC
000000e4 B EntireDiskMtdData
000000e0 t lposc_cali
000000dc T SF_DAL_OTPWrite_MXIC
000000d0 T MapWindow
000000d0 T bl_hardware_init
000000cc T SFI_Dev_Command_List
000000cc T SF_DAL_OTPWrite_WINBOND
000000cc T SF_DAL_OTPWrite_GIGADEVICE
000000cc T CMEM_Init_FullDriver
000000c8 T SF_DAL_OTPRead_WINBOND
000000c4 T uart_internal_config_baudrate
000000c4 T custom_setSFI
000000bc T SF_DAL_OTPRead_GIGADEVICE
000000a8 T SF_DAL_Flash_Protect_sector
000000a0 T clock_freq_meter
0000009c T SF_DAL_CheckDeviceReady
0000009c b g_uart_para
00000094 T SF_DAL_OTPRead_MXIC
00000094 T hal_uart_init
00000090 T rtc_internal_sram_setting
0000008c T SF_DAL_Init_Vendor
0000008c T __floatundisf
0000008c T __aeabi_ul2f
00000088 T hal_clock_enable
00000088 T hal_clock_disable
00000086 T SFI_GPRAM_Write
00000084 T hal_clock_init
00000084 B EntireDiskDriveData
00000080 b tmp_data_buffer
00000080 T SF_DAL_CheckReadyAndResume
0000007c T rtc_internal_set_power_key
0000007c T NOR_ReadID
0000007c T __floatdisf
0000007c T CMEM_Index
0000007c T CMEM_EMIINIT_Index
0000007c T __aeabi_l2f
00000078 T SFI_Dev_Command_Ext
00000070 T SF_DAL_UnlockBlock_MXIC
00000070 t g_uart_baudrate_map
0000006e T SF_DAL_Init_Driver
0000006c T bl_rtc_func_init
00000068 T SFI_MacEnable
00000068 t clock_volt_lv_switch
00000064 T SF_DAL_OTPLock_WINBOND
00000064 T SF_DAL_OTPLock_GIGADEVICE
00000064 T SF_DAL_Init_WINBOND
00000064 t rtc_local_set_osc32conx.constprop.2
00000064 T hal_uart_set_baudrate
00000062 T CMEM_NOR_Construct_RegionInfo_Internal
00000060 T clk_topgen_input_directly
00000060 t bl_itoa
0000005c T SF_DAL_Init_WINBOND_OTP
0000005c T SF_DAL_Init_GIGADEVICE_OTP
0000005c t rtc_local_write_sram_con
0000005c T hal_nvic_restore_interrupt_mask
0000005c T hal_clock_dcm_enable
0000005c T CMEM_EMIINIT_ReadID
0000005a T SF_DAL_MountDevice
00000058 T supported_flash_protect_type
00000058 T hal_gpt_get_free_run_count
00000058 T clock_mux_cur_sel
00000054 T hal_clock_is_enabled
00000050 t upll_enable
00000050 T SF_DAL_Dev_Suspend
00000050 T hal_flash_init
0000004c T uart_internal_config_fifo
0000004c T SFI_GPRAM_Write_C1A4
0000004c T SF_DAL_Dev_EraseBlock
0000004c T rtc_internal_set_osc_pwr
0000004c T rtc_internal_select_osc32
0000004c T hal_cache_is_cacheable
0000004c T combo_mem_id_list
******** T SFI_Dev_CommandAddress
******** T SF_DAL_ShutDown
******** T SF_DAL_Dev_Resume
******** t rtc_local_lowpower_detect
******** T rtc_internal_set_setting_cg
******** T bl_start_user_code
******** T readRawDiskData
******** T SFI_Dev_Command
******** b g_rtc_private_data
******** b EntireDiskRegionInfo
******** T BlockAddress
******** b BankInfo
0000003c B NORFlashMtd
0000003c T memmem
0000003c T hal_nvic_save_and_set_interrupt_mask
0000003c T hal_cache_invalidate_one_cache_line
0000003c T CMEM_EMIINIT_CheckValidDeviceID
0000003c T CMEM_CheckValidDeviceID
******** T uart_internal_config_line
******** T SF_DAL_OTPAccess
******** T SF_DAL_FailCheck
******** T SF_DAL_Dev_ReadID
******** T hal_clock_set_volt_lv
******** T SF_DAL_Dev_ReadStatus
******** T rtc_internal_unlock_protect
******** T rtc_internal_sram_setting_cg
******** t lposc_enable
******** T hal_gpt_delay_ms
******** T CMEM_Init_nor_list
******** T SF_DAL_OTPLock_MXIC
******** T SF_DAL_IOCtrl
******** T SF_DAL_Dev_Command
******** T hal_uart_put_char
******** T hal_rtc_get_f32k_frequency
******** T hal_gpt_delay_us
******** T __gnu_uldivmod_helper
******** T __gnu_ldivmod_helper
******** T CMEM_BlockSize_Internal
0000002e T gpt_convert_ms_to_32k_count
0000002c T SFI_MacLeave
0000002c T SF_DAL_SuspendErase
0000002c T SF_DAL_Dev_WaitReady_EMIINIT
0000002c T hal_flash_protect
0000002c T hal_clock_get_freq_meter
00000028 T uart_internal_write_bytes
00000028 T SFI_MacTrigger
00000028 T NOR_init
00000028 T NOR_Construct_RegionInfo
00000028 T hal_clock_set_pll_dcm_init
00000028 T get_rawdisk_error_code
00000024 t MountDevice
00000024 T hal_flash_read
00000024 T __floatunsisf
00000024 T __aeabi_ui2f
00000023 D sw_verno_str
0000001f D build_date_time_str
0000001e T memcmp
0000001c T ust_get_current_time
0000001c T __floatsisf
0000001c T div_memmem
0000001c T clock_get_upll_status
0000001c T clock_get_rfpll_l1_status
0000001c T __aeabi_i2f
0000001a T SF_DAL_Init_GIGADEVICE
0000001a T bl_print
******** B sf_dal_data_cmd
******** T uart_internal_reset_register
******** t rtc_local_wait_done
******** T hal_nvic_save_and_set_interrupt_mask_special
******** T hal_flash_unprotect
******** T gpt_start_free_run_timer
******** t __func__.9391
******** T clock_get_lposc_status
******** T printf
******** T NOR_Get_FlashSizeFromBankInfo
******** T gpt_open_clock_source
******** t clk_linkage_tbl
******** b rev.6505
******** b rev.6491
******** t upll_disable
******** t rtc_local_trigger_wait
******** T hal_core_status_read
******** T hal_chip_sub_version
******** t __func__.9164
******** T SFI_MacWaitReady
******** T SFI_GPRAM_Write_C1A3
******** T mux_id_to_reg_shift
******** T memcpy
******** t __func__.9199
******** T rtc_internal_set_spar_rg
******** T rtc_internal_reload
******** T rtc_internal_powerkey_is_valid
******** d osc_tune_tbl
******** T memset
******** t lposc_disable
******** T hal_nvic_restore_interrupt_mask_special
******** T hal_gpt_get_duration_count
******** D g_rom_parameter
******** T CMEM_GetIDEntry
0000000e T main
0000000c T SF_DAL_Dev_WriteEnable
0000000c T rtc_internal_irq_status
0000000c D hw_verno_str
0000000c T g_uart_regbase
0000000c T gpt_delay_time
0000000c T CMEM_BlockSize
0000000c T bl_set_debug_level
0000000a T SF_DAL_ResumeErase
0000000a T SF_DAL_OTPQueryLength
0000000a T custom_setSFIExt
0000000a T bl_custom_cm4_start_address
00000008 B StatusMap
00000008 d clk_none_lv
00000008 d clk_0p8_lv
00000008 d clk_0p7_lv
00000008 d clk_0p6_lv
00000008 T abs
00000006 T SF_DAL_NonBlockEraseBlock
00000006 t rfpll_l5_enable
00000006 t rfpll_l5_disable
00000006 t rfpll_l1_enable
00000006 t rfpll_l1_disable
00000006 T custom_get_NORFLASH_Size
00000004 T SFI_ReverseByteOrder
00000004 B sfi_index
00000004 T SF_DAL_UnlockBlock_None
00000004 D sf_dal_resume_time
00000004 T SF_DAL_LockEraseBlkAddr
00000004 T SaveAndSetIRQMask
00000004 b replaced.7235
00000004 B PAGE_BUFFER_SIZE
00000004 b nor_list
00000004 B nor_id
00000004 B mask_irq_func
00000004 d mask_int_time
00000004 d mask_int_start
00000004 d mask_int_end
00000004 b look_up_miss.7237
00000004 b look_up_hit.7236
00000004 T gpt_current_count
00000004 D gNOR_ReturnReadyEnd
00000004 T flash_sw_protected_check
00000004 B flash_device_type
00000004 T DelayAWhile_UST
00000004 T Custom_NOR_Init
00000004 b cmem_nor_index
00000004 B cmem_id
00000004 T clock_get_rfpll_l5_status
00000004 b BlockLayout
00000003 t g_uart_port_to_pdn
00000002 T RestoreIRQMask
00000002 B gpt_running_state
00000002 d cmem_index
00000002 d cmem_emiinit_index
00000002 T __assert_func
00000002 W __aeabi_ldiv0
00000002 W __aeabi_idiv0
00000001 b s_core_status
00000001 B NOR_FLASH_SUSPENDED
00000001 B NOR_FLASH_BUSY
00000001 d flash_init_status
00000001 b debug_level
